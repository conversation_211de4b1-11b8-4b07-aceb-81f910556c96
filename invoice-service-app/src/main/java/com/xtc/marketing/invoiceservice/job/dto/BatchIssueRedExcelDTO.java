package com.xtc.marketing.invoiceservice.job.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 批处理待冲红数据ExcelDTO
 */
@Getter
@Setter
@ToString
public class BatchIssueRedExcelDTO {

    /**
     * 发票类型
     */
    @ExcelProperty("发票类型")
    private String invoiceType;
    /**
     * 销售方税号
     */
    @ExcelProperty("销售方税号")
    private String sellerTaxNo;
    /**
     * 发票号码
     */
    @ExcelProperty("发票号码")
    private String invoiceNo;
    /**
     * 发票代码
     */
    @ExcelProperty("发票代码")
    private String invoiceCode;

}
