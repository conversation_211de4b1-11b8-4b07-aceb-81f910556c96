package com.xtc.marketing.invoiceservice.invoice.executor.command;

import com.baiwang.ispsdk.entity.request.OutputRedinvoiceAddRequest;
import com.baiwang.ispsdk.entity.response.OutputInvoiceQueryResponse;
import com.baiwang.ispsdk.entity.response.OutputRedinvoiceRedforminfoResponse;
import com.baiwang.ispsdk.entity.response.node.OutputRedinvoiceRedforminfo;
import com.xtc.marketing.invoiceservice.exception.BizException;
import com.xtc.marketing.invoiceservice.invoice.converter.BaiWangInvoiceConverter;
import com.xtc.marketing.invoiceservice.invoice.dataobject.SellerDO;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceItemDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.command.NormalIssueRedCmd;
import com.xtc.marketing.invoiceservice.invoice.enums.CreateTypeEnum;
import com.xtc.marketing.invoiceservice.invoice.enums.InvoiceItemTypeEnum;
import com.xtc.marketing.invoiceservice.invoice.executor.query.SellerGetQryExe;
import com.xtc.marketing.invoiceservice.rpc.BaiWangInvoiceRpc;
import com.xtc.marketing.invoiceservice.util.BeanCopier;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 税控发票冲红命令执行器
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class NormalIssueRedCmdExe {

    // 基础设施层注入
    private final BaiWangInvoiceRpc baiWangInvoiceRpc;
    private final BaiWangInvoiceConverter baiWangInvoiceConverter;
    private final SellerGetQryExe sellerGetQryExe;

    /**
     * 税控发票冲红
     *
     * @param cmd 参数
     * @return 发票详情
     */
    public InvoiceDTO execute(NormalIssueRedCmd cmd) {
        // 查询蓝票数据
        InvoiceDTO blueInvoiceDTO = this.queryNormalInvoice(cmd.getSellerTaxNo(), cmd.getInvoiceNo(), cmd.getInvoiceCode());
        // 生成红票
        InvoiceDTO redInvoiceDTO = BeanCopier.copy(blueInvoiceDTO, InvoiceDTO::new, "items");
        redInvoiceDTO.setCreateType(CreateTypeEnum.RED.name());
        // 如果蓝票没有销售方名称，则使用税号查询销售方
        if (StringUtils.isBlank(redInvoiceDTO.getSellerName())) {
            SellerDO seller = sellerGetQryExe.bySellerIdentifyNo(cmd.getSellerTaxNo());
            redInvoiceDTO.setSellerName(seller.getSellerName());
        }
        // 生成流水号 r1_xxxxxxxx 使用 blueInvoiceCode 截取后 17 位，避免下标越界
        String blueInvoiceNo = blueInvoiceDTO.getBlueInvoiceCode() + blueInvoiceDTO.getBlueInvoiceNo();
        String redSerialNo = "r1_%s".formatted(blueInvoiceNo.substring(Math.max(0, blueInvoiceNo.length() - 17)));
        redInvoiceDTO.setSerialNo(redSerialNo);
        // 合并蓝票被折扣行和折扣行的金额
        List<InvoiceItemDTO> redItems = blueInvoiceDTO.getItems().stream()
                .filter(item -> !InvoiceItemTypeEnum.DISCOUNT.equalsName(item.getItemType()))
                .sorted(Comparator.comparing(InvoiceItemDTO::getItemNo))
                .map(blueItem -> this.buildRedItem(blueInvoiceDTO.getItems(), blueItem))
                .flatMap(Optional::stream)
                .toList();
        redInvoiceDTO.setItems(redItems);
        // 开红票
        OutputRedinvoiceAddRequest redRequest = baiWangInvoiceConverter.toOutputRedinvoiceAddRequest(redInvoiceDTO);
        OutputRedinvoiceRedforminfoResponse redResponse = baiWangInvoiceRpc.issueRed(redRequest);
        // 更新发票数据
        OutputRedinvoiceRedforminfo redRpcData = redResponse.getModel().getFirst();
        baiWangInvoiceConverter.updateInvoiceDTO(redInvoiceDTO, redRpcData);
        redInvoiceDTO.setInvoiceAmount(redInvoiceDTO.getPriceAmount() + redInvoiceDTO.getTaxAmount());
        // 转换开票项目数据
        Map<Integer, InvoiceItemDTO> invoiceItemsMap = redInvoiceDTO.getItems().stream()
                .collect(Collectors.toMap(InvoiceItemDTO::getItemNo, Function.identity()));
        redRpcData.getElectricInvoiceDetails().forEach(itemResult -> {
            // 通过蓝票的行号匹配
            InvoiceItemDTO invoiceItem = invoiceItemsMap.get(itemResult.getOriginalInvoiceDetailNo());
            if (invoiceItem == null) {
                throw BizException.of("更新开票业务数据异常，发票项目行号不匹配 [%s]", itemResult.getOriginalInvoiceDetailNo());
            }
        });
        return redInvoiceDTO;
    }

    /**
     * 查询税控发票详情
     *
     * @param sellerTaxNo 销售方税号
     * @param invoiceNo   发票号
     * @param invoiceCode 发票代码
     * @return 发票详情
     */
    public InvoiceDTO queryNormalInvoice(String sellerTaxNo, String invoiceNo, String invoiceCode) {
        OutputInvoiceQueryResponse queryResponse = baiWangInvoiceRpc.queryByInvoiceNoAndInvoiceCode(sellerTaxNo, invoiceNo, invoiceCode);
        if (CollectionUtils.isEmpty(queryResponse.getModel())) {
            throw BizException.of("未查到蓝票数据，请到平台确认");
        }
        return baiWangInvoiceConverter.toInvoiceDTO(queryResponse.getModel().getFirst());
    }

    /**
     * 构建红票项目
     *
     * @param blueItems 蓝票项目列表
     * @param blueItem  当前处理的蓝票项目
     */
    private Optional<InvoiceItemDTO> buildRedItem(List<InvoiceItemDTO> blueItems, InvoiceItemDTO blueItem) {
        InvoiceItemDTO redItem = new InvoiceItemDTO();
        redItem.setItemNo(blueItem.getItemNo());
        redItem.setItemType(InvoiceItemTypeEnum.DISCOUNTED.name());
        redItem.setGoodsName(blueItem.getGoodsName());
        redItem.setTaxClassificationCode(blueItem.getTaxClassificationCode());
        redItem.setSpecification(blueItem.getSpecification());
        redItem.setUnit(blueItem.getUnit());
        redItem.setNum(Math.negateExact(blueItem.getNum()));
        redItem.setTaxRate(blueItem.getTaxRate());
        // 计算冲红金额
        int totalPrice = blueItem.getTotalPrice();
        int taxAmount = blueItem.getTaxAmount();
        // 蓝票项目为被折扣行，则需要汇总折扣行的金额
        if (InvoiceItemTypeEnum.DISCOUNTED.equalsName(blueItem.getItemType())) {
            // 每个被折扣行的下一行为折扣行，集合下标从 0 开始无需 +1
            int discountIndex = blueItem.getItemNo();
            if (discountIndex < blueItems.size()) {
                InvoiceItemDTO discountItem = blueItems.get(discountIndex);
                if (InvoiceItemTypeEnum.DISCOUNT.equalsName(discountItem.getItemType())) {
                    // 被折扣行（正） + 折扣行（负）
                    totalPrice = blueItem.getTotalPrice() + discountItem.getTotalPrice();
                    taxAmount = blueItem.getTaxAmount() + discountItem.getTaxAmount();
                }
            }
        }
        if (totalPrice == 0) {
            return Optional.empty();
        }
        if (totalPrice < 0) {
            throw BizException.of("红票项目金额不允许大于 0 [行号 %s]", blueItem.getItemNo());
        }
        // 金额取反
        redItem.setTotalPrice(Math.negateExact(totalPrice));
        redItem.setTaxAmount(Math.negateExact(taxAmount));
        return Optional.of(redItem);
    }

}
