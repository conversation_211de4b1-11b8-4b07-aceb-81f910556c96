package com.xtc.marketing.invoiceservice.invoice.executor.query;

import com.baiwang.ispsdk.entity.response.OutputEinvoiceQueryResponse;
import com.baiwang.ispsdk.entity.response.node.OutputEinvoiceQuery;
import com.xtc.marketing.invoiceservice.exception.BizException;
import com.xtc.marketing.invoiceservice.invoice.converter.BaiWangInvoiceConverter;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.query.NotSystemRedDetailQry;
import com.xtc.marketing.invoiceservice.rpc.BaiWangInvoiceRpc;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * 非系统红票详情查询执行器
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class NotSystemRedDetailQryExe {

    // 基础设施层注入
    private final BaiWangInvoiceRpc baiWangInvoiceRpc;
    private final BaiWangInvoiceConverter baiWangInvoiceConverter;

    /**
     * 非系统红票详情
     *
     * @param qry 参数
     * @return 发票详情
     */
    public InvoiceDTO execute(NotSystemRedDetailQry qry) {
        OutputEinvoiceQueryResponse queryResponse = baiWangInvoiceRpc.queryRedBySerialNo(qry.getSellerTaxNo(),
                qry.getSerialNo(), qry.getInvoiceDate());
        // 未查询到数据，当作未开票处理
        if (queryResponse == null || CollectionUtils.isEmpty(queryResponse.getModel())) {
            return null;
        }
        OutputEinvoiceQuery data = queryResponse.getModel().getFirst();
        // 发票状态：00 开具成功，03 发票作废
        return switch (data.getInvoiceStatus()) {
            case "00" -> baiWangInvoiceConverter.toInvoiceDTO(data);
            case "03" -> throw BizException.of("发票已作废，请到平台确认");
            // 默认当作未开票处理
            default -> null;
        };
    }

}
