package com.xtc.marketing.invoiceservice.invoice.executor.query;

import com.xtc.marketing.invoiceservice.invoice.converter.InvoiceConverter;
import com.xtc.marketing.invoiceservice.invoice.dao.InvoiceApplyDao;
import com.xtc.marketing.invoiceservice.invoice.dataobject.InvoiceApplyDO;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceApplyDetailDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * 发票申请详情查询执行器
 */
@RequiredArgsConstructor
@Component
public class InvoiceApplyDetailGetQryExe {

    // executor 注入
    private final InvoiceDetailGetQryExe invoiceDetailGetQryExe;
    // 基础设施层注入
    private final InvoiceApplyDao invoiceApplyDao;
    private final InvoiceConverter invoiceConverter;

    /**
     * 查询发票申请详情
     *
     * @param applyId 申请id
     * @return 发票申请详情
     */
    public InvoiceApplyDetailDTO byApplyId(String applyId) {
        Optional<InvoiceApplyDO> applyOpt = invoiceApplyDao.getByApplyId(applyId);
        if (applyOpt.isEmpty()) {
            return null;
        }
        InvoiceApplyDetailDTO detail = invoiceConverter.toInvoiceApplyDetailDTO(applyOpt.get());
        InvoiceDTO invoiceDTO = invoiceDetailGetQryExe.byInvoiceId(detail.getInvoiceId());
        detail.setInvoice(invoiceDTO);
        return detail;
    }

}
