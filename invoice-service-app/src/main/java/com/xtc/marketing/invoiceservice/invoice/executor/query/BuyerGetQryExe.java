package com.xtc.marketing.invoiceservice.invoice.executor.query;

import com.xtc.marketing.invoiceservice.exception.BizErrorCode;
import com.xtc.marketing.invoiceservice.exception.BizException;
import com.xtc.marketing.invoiceservice.invoice.dao.BuyerDao;
import com.xtc.marketing.invoiceservice.invoice.dataobject.BuyerDO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 购买方查询执行器
 */
@RequiredArgsConstructor
@Component
public class BuyerGetQryExe {

    private final BuyerDao buyerDao;

    /**
     * 查询购买方，忽略脱敏
     *
     * @param buyerCode 购买方代码
     * @return 购买方
     */
    public BuyerDO byBuyerCodeWithoutMask(String buyerCode) {
        return buyerDao.getByBuyerCodeWithoutMask(buyerCode)
                .orElseThrow(() -> BizException.of(BizErrorCode.B_BUYER_BuyerNotExists));
    }

}
