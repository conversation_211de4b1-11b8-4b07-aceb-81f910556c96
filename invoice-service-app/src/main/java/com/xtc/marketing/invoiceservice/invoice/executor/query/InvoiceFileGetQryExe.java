package com.xtc.marketing.invoiceservice.invoice.executor.query;

import com.xtc.marketing.invoiceservice.exception.BizException;
import com.xtc.marketing.invoiceservice.invoice.dataobject.FileDO;
import com.xtc.marketing.invoiceservice.invoice.domainservice.InvoiceFileService;
import com.xtc.marketing.invoiceservice.invoice.dto.query.InvoiceFileGetQry;
import com.xtc.marketing.invoiceservice.oss.MarketingInvoiceOssClient;
import com.xtc.marketing.invoiceservice.util.FileDownloader;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * 获取发票文件命令执行器
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class InvoiceFileGetQryExe {

    // executor 注入
    private final FileGetQryExe fileGetQryExe;
    // 领域层注入
    private final InvoiceFileService invoiceFileService;
    // 基础设施层注入
    private final MarketingInvoiceOssClient marketingInvoiceOssClient;

    /**
     * 获取发票文件
     *
     * @param qry 参数
     * @return 发票文件
     */
    public ResponseEntity<Resource> execute(InvoiceFileGetQry qry) {
        Optional<String> fileIdOpt = invoiceFileService.getFileIdByToken(qry.getFileToken());
        if (fileIdOpt.isEmpty()) {
            throw BizException.of("文件不存在");
        }
        FileDO file = fileGetQryExe.byFileId(fileIdOpt.get());
        return FileDownloader.buildResourceResponseEntity(
                file.getFileName(),
                file.getFileType(),
                FileDownloader.ContentDisposition.INLINE,
                () -> marketingInvoiceOssClient.getObject(file.getObjectName())
        );
    }

}
