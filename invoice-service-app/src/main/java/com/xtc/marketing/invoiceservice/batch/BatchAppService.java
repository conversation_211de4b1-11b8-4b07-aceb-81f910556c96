package com.xtc.marketing.invoiceservice.batch;

import com.xtc.marketing.invoiceservice.batch.dto.BatchFileDTO;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

/**
 * 批处理模块应用服务
 */
public interface BatchAppService {

    /**
     * 文件列表
     *
     * @param date 日期，默认查询当天的文件列表，格式：yyyyMMdd
     * @return 文件列表
     */
    BatchFileDTO batchFiles(String date);

    /**
     * 下载文件
     *
     * @param objectName 文件对象名称
     * @return 文件
     * @apiNote 只支持 batch/ 路径下的文件
     */
    ResponseEntity<Resource> downloadFile(String objectName);

    /**
     * 上传待处理文件
     *
     * @param file 文件
     * @return 文件对象名称
     */
    String uploadFile(MultipartFile file);

    /**
     * 删除文件
     *
     * @param objectName 文件对象名称
     */
    void removeFile(String objectName);

}
