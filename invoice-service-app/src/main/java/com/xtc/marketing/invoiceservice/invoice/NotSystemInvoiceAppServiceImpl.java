package com.xtc.marketing.invoiceservice.invoice;

import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.command.NormalIssueRedCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.query.NotSystemFileGetQry;
import com.xtc.marketing.invoiceservice.invoice.dto.query.NotSystemRedDetailQry;
import com.xtc.marketing.invoiceservice.invoice.executor.command.NormalIssueRedCmdExe;
import com.xtc.marketing.invoiceservice.invoice.executor.query.NotSystemFileGetQryExe;
import com.xtc.marketing.invoiceservice.invoice.executor.query.NotSystemRedDetailQryExe;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

/**
 * 非系统数据模块应用服务实现类
 * <p>简单逻辑 10 行以内的代码可以直接在 service 方法内实现</p>
 * <p>复杂逻辑（或可复用逻辑）封装在 executor 里实现，调用链路：service > executor</p>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class NotSystemInvoiceAppServiceImpl implements NotSystemInvoiceAppService {

    // executor 注入
    private final NotSystemRedDetailQryExe notSystemRedDetailQryExe;
    private final NormalIssueRedCmdExe normalIssueRedCmdExe;
    private final NotSystemFileGetQryExe notSystemFileGetQryExe;

    @Override
    public ResponseEntity<Resource> invoiceFile(NotSystemFileGetQry qry) {
        return notSystemFileGetQryExe.execute(qry);
    }

    @Override
    public InvoiceDTO notSystemRedDetail(NotSystemRedDetailQry qry) {
        return notSystemRedDetailQryExe.execute(qry);
    }

    @Override
    public InvoiceDTO normalIssueRed(NormalIssueRedCmd cmd) {
        return normalIssueRedCmdExe.execute(cmd);
    }

}
