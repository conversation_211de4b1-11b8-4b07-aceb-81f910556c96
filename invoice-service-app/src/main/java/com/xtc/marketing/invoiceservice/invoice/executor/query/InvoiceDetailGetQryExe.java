package com.xtc.marketing.invoiceservice.invoice.executor.query;

import com.xtc.marketing.invoiceservice.exception.BizException;
import com.xtc.marketing.invoiceservice.invoice.converter.InvoiceConverter;
import com.xtc.marketing.invoiceservice.invoice.dao.InvoiceDao;
import com.xtc.marketing.invoiceservice.invoice.dao.InvoiceItemDao;
import com.xtc.marketing.invoiceservice.invoice.dataobject.InvoiceDO;
import com.xtc.marketing.invoiceservice.invoice.dataobject.InvoiceItemDO;
import com.xtc.marketing.invoiceservice.invoice.domainservice.InvoiceFileService;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceItemDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.query.InvoiceDetailGetQry;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;

/**
 * 发票详情查询执行器
 */
@RequiredArgsConstructor
@Component
public class InvoiceDetailGetQryExe {

    // 基础设施层注入
    private final InvoiceDao invoiceDao;
    private final InvoiceItemDao invoiceItemDao;
    private final InvoiceConverter invoiceConverter;
    private final InvoiceFileService invoiceFileService;

    /**
     * 查询发票详情
     *
     * @param invoiceId 发票id
     * @return 发票详情
     */
    public InvoiceDTO byInvoiceId(String invoiceId) {
        return invoiceDao.getByInvoiceId(invoiceId).map(this::convertToDto).orElse(null);
    }

    /**
     * 查询发票详情
     *
     * @param qry 参数
     * @return 发票详情
     */
    public InvoiceDTO byQry(InvoiceDetailGetQry qry) {
        return invoiceDao.getBy(qry)
                .map(this::convertToDto)
                .orElseThrow(() -> BizException.of("发票不存在"));
    }

    /**
     * 查询发票详情
     *
     * @param invoice 发票
     * @return 发票详情
     */
    public InvoiceDTO convertToDto(InvoiceDO invoice) {
        InvoiceDTO invoiceDTO = invoiceConverter.toInvoiceDTO(invoice);
        // 填充发票项目
        List<InvoiceItemDO> invoiceItems = invoiceItemDao.listByInvoiceId(invoiceDTO.getInvoiceId());
        if (CollectionUtils.isEmpty(invoiceItems)) {
            return invoiceDTO;
        }
        List<InvoiceItemDTO> items = invoiceConverter.toInvoiceItemDTO(invoiceItems);
        invoiceDTO.setItems(items);
        // 填充发票文件
        invoiceFileService.createPdfUrl(invoiceDTO.getInvoiceId())
                .ifPresent(fileUrl -> {
                    UriComponents uriComponents = UriComponentsBuilder.fromUriString(fileUrl).build();
                    String fileToken = uriComponents.getQueryParams().getFirst("fileToken");
                    invoiceDTO.setFileToken(fileToken);
                    invoiceDTO.setFileUrl(fileUrl);
                });
        return invoiceDTO;
    }

}
