package com.xtc.marketing.invoiceservice.invoice.executor.command;

import com.baiwang.ispsdk.entity.response.OutputEinvoiceQueryResponse;
import com.baiwang.ispsdk.entity.response.node.OutputEinvoiceQuery;
import com.baiwang.ispsdk.entity.response.node.OutputEinvoiceQueryInvoiceQueryInvoiceDetail;
import com.google.common.collect.Lists;
import com.xtc.marketing.invoiceservice.exception.BizException;
import com.xtc.marketing.invoiceservice.invoice.bo.InvoiceIssueBO;
import com.xtc.marketing.invoiceservice.invoice.converter.BaiWangInvoiceConverter;
import com.xtc.marketing.invoiceservice.invoice.dao.FileDao;
import com.xtc.marketing.invoiceservice.invoice.dao.InvoiceApplyDao;
import com.xtc.marketing.invoiceservice.invoice.dao.InvoiceDao;
import com.xtc.marketing.invoiceservice.invoice.dao.InvoiceItemDao;
import com.xtc.marketing.invoiceservice.invoice.dataobject.InvoiceApplyDO;
import com.xtc.marketing.invoiceservice.invoice.dataobject.InvoiceDO;
import com.xtc.marketing.invoiceservice.invoice.dataobject.InvoiceItemDO;
import com.xtc.marketing.invoiceservice.invoice.dataobject.jsonobject.ApplyHistoryDO;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.command.InvoiceSyncCmd;
import com.xtc.marketing.invoiceservice.invoice.enums.ApplyStateEnum;
import com.xtc.marketing.invoiceservice.invoice.enums.CreateTypeEnum;
import com.xtc.marketing.invoiceservice.invoice.executor.query.InvoiceDetailGetQryExe;
import com.xtc.marketing.invoiceservice.invoice.executor.query.InvoiceGetQryExe;
import com.xtc.marketing.invoiceservice.rpc.BaiWangInvoiceRpc;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 发票同步命令执行器
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class InvoiceSyncCmdExe {

    // executor 注入
    private final InvoiceGetQryExe invoiceGetQryExe;
    private final InvoiceDetailGetQryExe invoiceDetailGetQryExe;
    // 基础设施层注入
    private final BaiWangInvoiceRpc baiWangInvoiceRpc;
    private final BaiWangInvoiceConverter baiWangInvoiceConverter;
    private final InvoiceDao invoiceDao;
    private final InvoiceItemDao invoiceItemDao;
    private final InvoiceApplyDao invoiceApplyDao;
    private final FileDao fileDao;

    /**
     * 发票详情查询
     *
     * @param cmd 参数
     * @return 发票详情
     */
    public InvoiceDTO execute(InvoiceSyncCmd cmd) {
        InvoiceDO invoice = invoiceGetQryExe.byInvoiceId(cmd.getInvoiceId());
        // 查询开票结果
        OutputEinvoiceQuery rpcData = this.rpcQueryResult(invoice);
        // 转换开票业务数据
        InvoiceIssueBO invoiceIssueBO = this.convertSyncInvoice(invoice, rpcData);
        List<InvoiceItemDO> syncItems = this.convertSyncItems(invoice, rpcData.getElectricInvoiceDetails());
        invoiceIssueBO.setInvoiceItems(syncItems);
        InvoiceApplyDO updateApplyState = this.updateApplyState(invoice);
        invoiceIssueBO.setApply(updateApplyState);
        // 事务保存开票业务数据
        InvoiceSyncCmdExe exe = (InvoiceSyncCmdExe) AopContext.currentProxy();
        exe.saveTransaction(invoiceIssueBO);
        // 查询最新的发票数据返回
        return invoiceDetailGetQryExe.byInvoiceId(invoice.getInvoiceId());
    }

    /**
     * 查询开票结果
     *
     * @param invoice 发票
     * @return 开票结果
     */
    private OutputEinvoiceQuery rpcQueryResult(InvoiceDO invoice) {
        OutputEinvoiceQueryResponse rpcResponse = baiWangInvoiceRpc.queryBySerialNo(invoice.getSellerIdentifyNo(), invoice.getSerialNo());
        // 如果没有查询到开票结果，并且发票类型是红票，则查询红票数据
        if (CollectionUtils.isEmpty(rpcResponse.getModel()) && invoice.getCreateType() == CreateTypeEnum.RED) {
            rpcResponse = baiWangInvoiceRpc.queryRedBySerialNo(invoice.getSellerIdentifyNo(),
                    invoice.getSerialNo(), invoice.getCreateTime().toLocalDate());
        }
        if (rpcResponse == null || CollectionUtils.isEmpty(rpcResponse.getModel())) {
            throw BizException.of("平台未查询到发票");
        }
        return rpcResponse.getModel().getFirst();
    }

    /**
     * 转换开票业务数据
     *
     * @param invoice 发票
     * @param rpcData 开票结果
     * @return 开票业务数据
     */
    private InvoiceIssueBO convertSyncInvoice(InvoiceDO invoice, OutputEinvoiceQuery rpcData) {
        InvoiceIssueBO issueBO = new InvoiceIssueBO();
        InvoiceDO syncInvoice = baiWangInvoiceConverter.toInvoiceDO(rpcData);
        syncInvoice.setInvoiceId(invoice.getInvoiceId());
        issueBO.setInvoice(syncInvoice);
        return issueBO;
    }

    /**
     * 转换发票项目列表
     *
     * @param invoice 发票
     * @param result  开票结果
     * @return 发票项目列表
     */
    private List<InvoiceItemDO> convertSyncItems(InvoiceDO invoice, List<OutputEinvoiceQueryInvoiceQueryInvoiceDetail> result) {
        List<InvoiceItemDO> syncItems = baiWangInvoiceConverter.toInvoiceItemDO(result);
        syncItems.forEach(item -> {
            String invoiceNo = switch (invoice.getCreateType()) {
                case BLUE -> invoice.getBlueInvoiceNo();
                case RED -> invoice.getRedInvoiceNo();
            };
            item.setInvoiceNo(invoiceNo);
            item.setSerialNo(invoice.getSerialNo());
            item.setInvoiceId(invoice.getInvoiceId());
        });
        return syncItems;
    }

    /**
     * 更新发票申请状态
     *
     * @param invoice 发票
     * @return 发票申请
     */
    private InvoiceApplyDO updateApplyState(InvoiceDO invoice) {
        Optional<InvoiceApplyDO> applyOpt = invoiceApplyDao.getByInvoiceIdAndSerialNo(invoice.getInvoiceId(), invoice.getSerialNo());
        if (applyOpt.isEmpty()) {
            return null;
        }
        // 确认发票文件保存成功，才能更新发票申请状态
        boolean fileExists = fileDao.existsByInvoiceId(invoice.getInvoiceId());
        if (BooleanUtils.isFalse(fileExists)) {
            return null;
        }
        InvoiceApplyDO apply = applyOpt.get();
        InvoiceApplyDO updateApply = new InvoiceApplyDO();
        updateApply.setApplyId(apply.getApplyId());
        // 更新发票申请状态
        ApplyStateEnum applyState = switch (invoice.getCreateType()) {
            case BLUE -> ApplyStateEnum.ISSUED_BLUE;
            case RED -> ApplyStateEnum.ISSUED_RED;
        };
        updateApply.setApplyState(applyState);
        // 添加历史记录
        updateApply.setApplyHistory(Lists.newArrayList(apply.getApplyHistory()));
        ApplyHistoryDO.add(updateApply.getApplyHistory(), applyState);
        return updateApply;
    }

    /**
     * 事务保存开票业务数据
     *
     * @param invoiceIssueBO 开票业务数据
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void saveTransaction(InvoiceIssueBO invoiceIssueBO) {
        invoiceDao.updateByInvoiceId(invoiceIssueBO.getInvoice());
        invoiceItemDao.updateBatchByInvoiceIdAndItemNo(invoiceIssueBO.getInvoiceItems());
        invoiceApplyDao.updateByApplyId(invoiceIssueBO.getApply());
    }

}
