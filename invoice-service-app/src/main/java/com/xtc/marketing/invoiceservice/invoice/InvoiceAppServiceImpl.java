package com.xtc.marketing.invoiceservice.invoice;

import com.xtc.marketing.invoiceservice.invoice.converter.InvoiceConverter;
import com.xtc.marketing.invoiceservice.invoice.dao.InvoiceApplyDao;
import com.xtc.marketing.invoiceservice.invoice.dataobject.InvoiceApplyDO;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceApplyDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceApplyDetailDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.command.InvoiceApplySubmitCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.command.InvoiceFileSyncCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.command.InvoiceIssueCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.command.InvoiceSyncCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.query.InvoiceApplyDetailGetQry;
import com.xtc.marketing.invoiceservice.invoice.dto.query.InvoiceApplyListQry;
import com.xtc.marketing.invoiceservice.invoice.dto.query.InvoiceDetailGetQry;
import com.xtc.marketing.invoiceservice.invoice.dto.query.InvoiceFileGetQry;
import com.xtc.marketing.invoiceservice.invoice.executor.command.*;
import com.xtc.marketing.invoiceservice.invoice.executor.query.InvoiceApplyDetailGetQryExe;
import com.xtc.marketing.invoiceservice.invoice.executor.query.InvoiceDetailGetQryExe;
import com.xtc.marketing.invoiceservice.invoice.executor.query.InvoiceFileGetQryExe;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 发票模块应用服务实现类
 * <p>简单逻辑 10 行以内的代码可以直接在 service 方法内实现</p>
 * <p>复杂逻辑（或可复用逻辑）封装在 executor 里实现，调用链路：service > executor</p>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class InvoiceAppServiceImpl implements InvoiceAppService {

    // executor 注入
    private final InvoiceApplyDetailGetQryExe invoiceApplyDetailGetQryExe;
    private final InvoiceApplySubmitCmdExe invoiceApplySubmitCmdExe;
    private final InvoiceIssueBlueCmdExe invoiceIssueBlueCmdExe;
    private final InvoiceIssueRedCmdExe invoiceIssueReCmdExe;
    private final InvoiceSyncCmdExe invoiceSyncCmdExe;
    private final InvoiceFileSyncCmdExe invoiceFileSyncCmdExe;
    private final InvoiceDetailGetQryExe invoiceDetailGetQryExe;
    private final InvoiceFileGetQryExe invoiceFileGetQryExe;
    // 基础设施层注入
    private final InvoiceConverter invoiceConverter;
    private final InvoiceApplyDao invoiceApplyDao;

    @Override
    public InvoiceApplyDetailDTO getApplyDetail(InvoiceApplyDetailGetQry qry) {
        return invoiceApplyDetailGetQryExe.byApplyId(qry.getApplyId());
    }

    @Override
    public List<InvoiceApplyDTO> listApply(InvoiceApplyListQry qry) {
        List<InvoiceApplyDO> listApply = invoiceApplyDao.listBy(qry);
        return invoiceConverter.toInvoiceApplyDTO(listApply);
    }

    @Override
    public InvoiceApplyDTO submitApply(InvoiceApplySubmitCmd cmd) {
        return invoiceApplySubmitCmdExe.execute(cmd);
    }

    @Override
    public InvoiceApplyDetailDTO issueBlue(InvoiceIssueCmd cmd) {
        return invoiceIssueBlueCmdExe.execute(cmd);
    }

    @Override
    public InvoiceApplyDetailDTO issueRed(InvoiceIssueCmd cmd) {
        return invoiceIssueReCmdExe.execute(cmd);
    }

    @Override
    public InvoiceDTO invoiceSync(InvoiceSyncCmd cmd) {
        return invoiceSyncCmdExe.execute(cmd);
    }

    @Override
    public String invoiceFileSync(InvoiceFileSyncCmd cmd) {
        return invoiceFileSyncCmdExe.execute(cmd);
    }

    @Override
    public InvoiceDTO getInvoiceDetail(InvoiceDetailGetQry qry) {
        return invoiceDetailGetQryExe.byQry(qry);
    }

    @Override
    public ResponseEntity<Resource> invoiceFile(InvoiceFileGetQry qry) {
        return invoiceFileGetQryExe.execute(qry);
    }

}
