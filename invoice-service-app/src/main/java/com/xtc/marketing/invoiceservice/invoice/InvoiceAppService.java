package com.xtc.marketing.invoiceservice.invoice;

import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceApplyDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceApplyDetailDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.command.InvoiceApplySubmitCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.command.InvoiceFileSyncCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.command.InvoiceIssueCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.command.InvoiceSyncCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.query.InvoiceApplyDetailGetQry;
import com.xtc.marketing.invoiceservice.invoice.dto.query.InvoiceApplyListQry;
import com.xtc.marketing.invoiceservice.invoice.dto.query.InvoiceDetailGetQry;
import com.xtc.marketing.invoiceservice.invoice.dto.query.InvoiceFileGetQry;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;

import java.util.List;

/**
 * 发票模块应用服务
 */
public interface InvoiceAppService {

    /**
     * 发票申请详情
     *
     * @param qry 参数
     * @return 发票申请详情
     */
    InvoiceApplyDetailDTO getApplyDetail(InvoiceApplyDetailGetQry qry);

    /**
     * 发票申请列表
     *
     * @param qry 参数
     * @return 发票申请列表
     */
    List<InvoiceApplyDTO> listApply(InvoiceApplyListQry qry);

    /**
     * 提交发票申请
     *
     * @param cmd 参数
     * @return 发票申请
     */
    InvoiceApplyDTO submitApply(InvoiceApplySubmitCmd cmd);

    /**
     * 开蓝票
     *
     * @param cmd 参数
     * @return 发票申请详情
     */
    InvoiceApplyDetailDTO issueBlue(InvoiceIssueCmd cmd);

    /**
     * 开红票
     *
     * @param cmd 参数
     * @return 发票申请详情
     */
    InvoiceApplyDetailDTO issueRed(InvoiceIssueCmd cmd);

    /**
     * 发票同步
     *
     * @param cmd 参数
     * @return 发票详情
     */
    InvoiceDTO invoiceSync(InvoiceSyncCmd cmd);

    /**
     * 发票文件同步
     *
     * @param cmd 参数
     * @return 发票文件
     */
    String invoiceFileSync(InvoiceFileSyncCmd cmd);

    /**
     * 发票详情
     *
     * @param qry 参数
     * @return 发票详情
     */
    InvoiceDTO getInvoiceDetail(InvoiceDetailGetQry qry);

    /**
     * 发票文件
     *
     * @param qry 参数
     * @return 发票文件
     */
    ResponseEntity<Resource> invoiceFile(InvoiceFileGetQry qry);

}
