package com.xtc.marketing.invoiceservice.invoice.executor.query;

import com.xtc.marketing.invoiceservice.exception.BizException;
import com.xtc.marketing.invoiceservice.invoice.dao.FileDao;
import com.xtc.marketing.invoiceservice.invoice.dataobject.FileDO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 文件查询执行器
 */
@RequiredArgsConstructor
@Component
public class FileGetQryExe {

    private final FileDao fileDao;

    /**
     * 查询文件
     *
     * @param fileId 文件id
     * @return 文件
     */
    public FileDO byFileId(String fileId) {
        return fileDao.getByFileId(fileId)
                .orElseThrow(() -> BizException.of("文件不存在"));
    }

}
