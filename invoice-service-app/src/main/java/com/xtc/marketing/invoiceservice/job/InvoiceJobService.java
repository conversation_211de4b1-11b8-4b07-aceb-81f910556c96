package com.xtc.marketing.invoiceservice.job;

import com.xtc.marketing.invoiceservice.config.BaseJobBO;

/**
 * 发票任务服务
 */
public interface InvoiceJobService {

    /**
     * 开票任务
     *
     * @param shardIndex 分片索引
     * @param param      任务参数
     */
    void invoiceIssueJob(int shardIndex, BaseJobBO param);

    /**
     * 发票数据同步任务
     *
     * @param shardIndex 分片索引
     * @param param      任务参数
     */
    void invoiceSyncJob(int shardIndex, BaseJobBO param);

    /**
     * 发票文件同步任务
     *
     * @param shardIndex 分片索引
     * @param param      任务参数
     */
    void invoiceFileSyncJob(int shardIndex, BaseJobBO param);

}
