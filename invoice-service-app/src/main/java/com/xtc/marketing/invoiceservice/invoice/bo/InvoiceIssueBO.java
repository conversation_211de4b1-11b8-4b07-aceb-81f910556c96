package com.xtc.marketing.invoiceservice.invoice.bo;

import com.xtc.marketing.invoiceservice.invoice.dataobject.InvoiceApplyDO;
import com.xtc.marketing.invoiceservice.invoice.dataobject.InvoiceDO;
import com.xtc.marketing.invoiceservice.invoice.dataobject.InvoiceItemDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 开票业务数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceIssueBO {

    /**
     * 发票申请
     */
    private InvoiceApplyDO apply;
    /**
     * 发票
     */
    private InvoiceDO invoice;
    /**
     * 发票项目
     */
    private List<InvoiceItemDO> invoiceItems;

}
