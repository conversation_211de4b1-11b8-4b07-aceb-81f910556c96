package com.xtc.marketing.invoiceservice.batch;

import com.xtc.marketing.invoiceservice.batch.dto.BatchFileDTO;
import com.xtc.marketing.invoiceservice.constant.BatchConstant;
import com.xtc.marketing.invoiceservice.exception.BizException;
import com.xtc.marketing.invoiceservice.oss.MarketingInvoiceOssClient;
import com.xtc.marketing.invoiceservice.util.DateUtil;
import com.xtc.marketing.invoiceservice.util.FileDownloader;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDate;
import java.util.List;

/**
 * 批处理模块应用服务实现类
 * <p>简单逻辑 10 行以内的代码可以直接在 service 方法内实现</p>
 * <p>复杂逻辑（或可复用逻辑）封装在 executor 里实现，调用链路：service > executor</p>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BatchAppServiceImpl implements BatchAppService {

    private final MarketingInvoiceOssClient marketingInvoiceOssClient;

    @Override
    public BatchFileDTO batchFiles(String date) {
        BatchFileDTO batchFileDTO = new BatchFileDTO();
        // 校验日期格式
        String nowDate = date;
        if (StringUtils.isNotBlank(date)) {
            try {
                LocalDate localDate = DateUtil.toLocalDateCompact(date);
                nowDate = DateUtil.toString(localDate, DateUtil.FORMAT_DATE_COMPACT);
            } catch (Exception e) {
                throw BizException.of("日期格式不正确，正确格式：%s", DateUtil.FORMAT_DATE_COMPACT);
            }
        }
        // 默认查询当天的文件列表
        String datePath = StringUtils.defaultIfBlank(nowDate, DateUtil.nowDateCompact());
        // 待处理文件列表
        String pendingFolder = BatchConstant.getIssueRedPendingFolder(datePath);
        List<String> pendingObjects = marketingInvoiceOssClient.listFolderObjects(pendingFolder, 20);
        batchFileDTO.setPending(pendingObjects);
        // 已处理文件列表
        String processed = BatchConstant.getIssueRedProcessedFolder(datePath);
        List<String> processedObjects = marketingInvoiceOssClient.listFolderObjects(processed, 20);
        batchFileDTO.setProcessed(processedObjects);
        return batchFileDTO;
    }

    @Override
    public ResponseEntity<Resource> downloadFile(String objectName) {
        if (BatchConstant.notBatchFile(objectName)) {
            throw BizException.of("只支持 %s 路径下的文件", BatchConstant.BATCH_FOLDER);
        }
        // 文件名取 objectName 的最后一部分
        String[] split = objectName.split("/");
        String fileName = split[split.length - 1];
        return FileDownloader.buildResourceResponseEntity(
                fileName,
                MediaType.APPLICATION_OCTET_STREAM,
                FileDownloader.ContentDisposition.ATTACHMENT,
                () -> marketingInvoiceOssClient.getObject(objectName)
        );
    }

    @Override
    public String uploadFile(MultipartFile file) {
        // 文件大小限制：5MB = 5 * 1024 * 1024
        if (file.getSize() > 5242880) {
            throw BizException.of("文件大小超过限制，最大支持 5MB");
        }
        String originalFilename = file.getOriginalFilename();
        if (StringUtils.isBlank(originalFilename)) {
            throw BizException.of("文件名不能为空");
        }
        if (!originalFilename.endsWith(".xlsx") && !originalFilename.endsWith(".xls")) {
            throw BizException.of("文件格式不正确，请上传 Excel 文件");
        }
        String[] split = originalFilename.split("\\.");
        if (split.length != 2) {
            throw BizException.of("完整的文件名只能包含一个 [.] 符号");
        }
        String filename = split[0];
        String suffix = split[1];
        String path = BatchConstant.getIssueRedPendingFolder(DateUtil.nowDateCompact());
        String objectName = "%s%s-%s.%s".formatted(path, filename, DateUtil.nowDateTimeCompact(), suffix);
        try {
            marketingInvoiceOssClient.putObject(objectName, file.getInputStream(), file.getContentType());
        } catch (IOException e) {
            throw BizException.of("文件上传失败", e.getCause());
        }
        return objectName;
    }

    @Override
    public void removeFile(String objectName) {
        if (BatchConstant.notBatchFile(objectName)) {
            throw BizException.of("只支持 %s 路径下的文件", BatchConstant.BATCH_FOLDER);
        }
        marketingInvoiceOssClient.removeObject(objectName);
    }

}
