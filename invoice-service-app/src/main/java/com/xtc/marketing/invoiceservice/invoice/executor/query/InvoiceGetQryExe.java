package com.xtc.marketing.invoiceservice.invoice.executor.query;

import com.xtc.marketing.invoiceservice.exception.BizException;
import com.xtc.marketing.invoiceservice.invoice.dao.InvoiceDao;
import com.xtc.marketing.invoiceservice.invoice.dataobject.InvoiceDO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 发票查询执行器
 */
@RequiredArgsConstructor
@Component
public class InvoiceGetQryExe {

    private final InvoiceDao invoiceDao;

    /**
     * 查询发票
     *
     * @param invoiceId 发票id
     * @return 发票
     */
    public InvoiceDO byInvoiceId(String invoiceId) {
        return invoiceDao.getByInvoiceId(invoiceId)
                .orElseThrow(() -> BizException.of("发票不存在"));
    }

}
