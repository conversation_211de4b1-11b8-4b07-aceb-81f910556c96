package com.xtc.marketing.invoiceservice.invoice.executor.query;

import com.xtc.marketing.invoiceservice.exception.BizException;
import com.xtc.marketing.invoiceservice.invoice.dao.BusinessDao;
import com.xtc.marketing.invoiceservice.invoice.dataobject.BusinessDO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 业务接入查询执行器
 */
@RequiredArgsConstructor
@Component
public class BusinessGetQryExe {

    private final BusinessDao businessDao;

    /**
     * 查询业务接入
     *
     * @param bizCode 业务代码
     * @return 业务接入
     */
    public BusinessDO byBizCode(String bizCode) {
        return businessDao.getByBizCode(bizCode)
                .orElseThrow(() -> BizException.of("业务不存在"));
    }

}
