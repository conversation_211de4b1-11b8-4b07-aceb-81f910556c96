package com.xtc.marketing.invoiceservice.invoice.executor.query;

import com.xtc.marketing.invoiceservice.invoice.dto.query.NotSystemFileGetQry;
import com.xtc.marketing.invoiceservice.oss.MarketingInvoiceOssClient;
import com.xtc.marketing.invoiceservice.util.FileDownloader;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

/**
 * 非系统文件获取命令执行器
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class NotSystemFileGetQryExe {

    // 基础设施层注入
    private final MarketingInvoiceOssClient marketingInvoiceOssClient;

    /**
     * 获取发票文件
     *
     * @param qry 参数
     * @return 发票文件
     */
    public ResponseEntity<Resource> execute(NotSystemFileGetQry qry) {
        // 截取文件名，原字符串：20250221/blue/b1_765791417774873.pdf
        String fileName = qry.getObjectName().substring(qry.getObjectName().lastIndexOf("/") + 1);
        // 截取文件后缀
        String fileSuffix = fileName.substring(fileName.lastIndexOf("."));
        // 拼接最终文件路径，对象名开头没有 "/" 则拼接上
        String filePrefix = qry.getObjectName().startsWith("/") ? "" : "/";
        String objectName = "invoice/retailers%s%s".formatted(filePrefix, qry.getObjectName());
        return FileDownloader.buildResourceResponseEntity(
                fileName,
                fileSuffix,
                FileDownloader.ContentDisposition.INLINE,
                () -> marketingInvoiceOssClient.getObject(objectName)
        );
    }

}
