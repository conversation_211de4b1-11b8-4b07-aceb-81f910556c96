package com.xtc.marketing.invoiceservice.invoice.executor.query;

import com.xtc.marketing.invoiceservice.exception.BizException;
import com.xtc.marketing.invoiceservice.invoice.dao.InvoiceApplyDao;
import com.xtc.marketing.invoiceservice.invoice.dataobject.InvoiceApplyDO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 发票申请查询执行器
 */
@RequiredArgsConstructor
@Component
public class InvoiceApplyGetQryExe {

    private final InvoiceApplyDao invoiceApplyDao;

    /**
     * 查询发票申请
     *
     * @param applyId 申请id
     * @return 发票申请
     */
    public InvoiceApplyDO byApplyId(String applyId) {
        return invoiceApplyDao.getByApplyId(applyId)
                .orElseThrow(() -> new BizException("发票申请不存在"));
    }

    /**
     * 查询发票申请
     *
     * @param applyId 申请id
     * @return 发票申请
     */
    public InvoiceApplyDO byApplyIdWithoutMask(String applyId) {
        return invoiceApplyDao.getByApplyIdWithoutMask(applyId)
                .orElseThrow(() -> new BizException("发票申请不存在"));
    }

}
