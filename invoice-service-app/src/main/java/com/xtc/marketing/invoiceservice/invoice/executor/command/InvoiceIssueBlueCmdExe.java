package com.xtc.marketing.invoiceservice.invoice.executor.command;

import com.baiwang.ispsdk.entity.request.OutputInvoiceIssueRequest;
import com.baiwang.ispsdk.entity.request.node.OutputInvoiceIssueInvoiceDetail;
import com.baiwang.ispsdk.entity.response.OutputInvoiceIssueResponse;
import com.baiwang.ispsdk.entity.response.node.OutputInvoiceIssueInvoice;
import com.baiwang.ispsdk.entity.response.node.OutputInvoiceIssueInvoiceResult;
import com.google.common.collect.Lists;
import com.xtc.marketing.invoiceservice.cache.BizOrderOperateLockCacheClient;
import com.xtc.marketing.invoiceservice.exception.BizException;
import com.xtc.marketing.invoiceservice.invoice.bo.InvoiceIssueBO;
import com.xtc.marketing.invoiceservice.invoice.converter.BaiWangInvoiceConverter;
import com.xtc.marketing.invoiceservice.invoice.converter.InvoiceConverter;
import com.xtc.marketing.invoiceservice.invoice.dao.GoodsDao;
import com.xtc.marketing.invoiceservice.invoice.dao.InvoiceApplyDao;
import com.xtc.marketing.invoiceservice.invoice.dao.InvoiceDao;
import com.xtc.marketing.invoiceservice.invoice.dao.InvoiceItemDao;
import com.xtc.marketing.invoiceservice.invoice.dataobject.*;
import com.xtc.marketing.invoiceservice.invoice.dataobject.jsonobject.ApplyHistoryDO;
import com.xtc.marketing.invoiceservice.invoice.dataobject.jsonobject.ApplyItemDO;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceApplyDetailDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.command.InvoiceIssueCmd;
import com.xtc.marketing.invoiceservice.invoice.enums.ApplyStateEnum;
import com.xtc.marketing.invoiceservice.invoice.enums.CreateTypeEnum;
import com.xtc.marketing.invoiceservice.invoice.enums.InvoiceItemTypeEnum;
import com.xtc.marketing.invoiceservice.invoice.executor.query.InvoiceApplyDetailGetQryExe;
import com.xtc.marketing.invoiceservice.invoice.executor.query.InvoiceApplyGetQryExe;
import com.xtc.marketing.invoiceservice.invoice.executor.query.SellerGetQryExe;
import com.xtc.marketing.invoiceservice.rpc.BaiWangInvoiceRpc;
import com.xtc.marketing.invoiceservice.util.MoneyUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 开蓝票命令执行器
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class InvoiceIssueBlueCmdExe {

    // executor 注入
    private final InvoiceApplyGetQryExe invoiceApplyGetQryExe;
    private final SellerGetQryExe sellerGetQryExe;
    private final InvoiceApplyDetailGetQryExe invoiceApplyDetailGetQryExe;
    // 基础设施层注入
    private final BizOrderOperateLockCacheClient bizOrderOperateLockCacheClient;
    private final InvoiceApplyDao invoiceApplyDao;
    private final GoodsDao goodsDao;
    private final InvoiceDao invoiceDao;
    private final InvoiceItemDao invoiceItemDao;
    private final InvoiceConverter invoiceConverter;
    private final BaiWangInvoiceConverter baiWangInvoiceConverter;
    private final BaiWangInvoiceRpc baiWangInvoiceRpc;

    /**
     * 开蓝票
     *
     * @param cmd 参数
     * @return 发票申请详情
     */
    public InvoiceApplyDetailDTO execute(InvoiceIssueCmd cmd) {
        InvoiceApplyDO apply = invoiceApplyGetQryExe.byApplyIdWithoutMask(cmd.getApplyId());
        SellerDO seller = sellerGetQryExe.bySellerCode(apply.getSellerCode());
        // 检查开票业务
        this.checkIssue(apply);
        // 构建开票业务数据，开票逻辑计算
        InvoiceIssueBO invoiceIssueBO = this.buildInvoiceIssueBO(apply, seller);
        // 订单号加锁
        bizOrderOperateLockCacheClient.execute(apply.getBizOrderId(), () -> {
            boolean existsApplyAccepted = invoiceApplyDao.existsAcceptedByBizOrderId(apply.getBizOrderId());
            if (existsApplyAccepted) {
                throw BizException.of("业务订单正在开票中，请稍后重试");
            }
            // 调用平台开票接口
            this.rpcCallAndUpdateInvoice(invoiceIssueBO);
            // 保存发票数据
            InvoiceIssueBlueCmdExe exe = (InvoiceIssueBlueCmdExe) AopContext.currentProxy();
            exe.saveInvoiceData(invoiceIssueBO);
        });
        // 返回最新的发票申请详情
        return invoiceApplyDetailGetQryExe.byApplyId(apply.getApplyId());
    }

    /**
     * 检查开票业务
     *
     * @param apply 发票申请
     */
    private void checkIssue(InvoiceApplyDO apply) {
        if (apply.getCreateType() != CreateTypeEnum.BLUE) {
            throw BizException.of("申请的开票类型不是蓝票");
        }
        if (apply.getApplyState().notAllowIssue()) {
            throw BizException.of("发票状态不允许开票 [%s]", apply.getApplyState().getDesc());
        }
    }

    /**
     * 构建开票业务数据，开票逻辑计算
     *
     * @param apply  发票申请
     * @param seller 销售方
     * @return 开票业务数据
     */
    private InvoiceIssueBO buildInvoiceIssueBO(InvoiceApplyDO apply, SellerDO seller) {
        InvoiceIssueBO invoiceIssueBO = new InvoiceIssueBO();
        // 构建发票申请，填充申请id和申请记录
        InvoiceApplyDO updateApply = InvoiceApplyDO.builder()
                .applyId(apply.getApplyId()).applyHistory(apply.getApplyHistory()).build();
        invoiceIssueBO.setApply(updateApply);
        // 构建发票
        InvoiceDO invoice = invoiceConverter.toInvoiceDO(apply, seller);
        invoiceIssueBO.setInvoice(invoice);
        // 计算分摊总折扣（负） = 总折扣（负） - 被折扣总金额（负）
        int totalSharingDiscount = this.calculateTotalSharingDiscount(apply.getApplyItems());
        // 获取商品列表
        Map<String, GoodsDO> goodsMap = this.buildGoodsMap(apply.getApplyItems());
        // 构建正常行，分摊折扣（分摊折扣时，正常行设置为被折扣行）
        List<ApplyItemDO> normalApplyItems = apply.getApplyItems().stream()
                .filter(item -> item.getItemType() == InvoiceItemTypeEnum.NORMAL).toList();
        this.buildNormalInvoiceItems(invoiceIssueBO, normalApplyItems, goodsMap, totalSharingDiscount);
        // 构建被折扣行
        List<ApplyItemDO> discountedApplyItems = apply.getApplyItems().stream()
                .filter(item -> item.getItemType() == InvoiceItemTypeEnum.DISCOUNTED).toList();
        this.buildDiscountedInvoiceItems(invoiceIssueBO, discountedApplyItems, goodsMap);
        return invoiceIssueBO;
    }

    /**
     * 计算分摊总折扣（负） = 总折扣（负） - 被折扣总金额（负）
     *
     * @param applyItems 发票申请项目列表
     * @return 分摊总折扣（负）
     */
    private int calculateTotalSharingDiscount(List<ApplyItemDO> applyItems) {
        // 计算总折扣（负）
        int totalDiscount = applyItems.stream()
                .filter(item -> item.getItemType() == InvoiceItemTypeEnum.DISCOUNT)
                .map(ApplyItemDO::getTotalPrice)
                .reduce(0, Integer::sum);
        log.info("总折扣金额 {}", MoneyUtil.centToYuan(totalDiscount));
        // 计算被折扣总金额（负）
        int totalDiscounted = applyItems.stream()
                .filter(item -> item.getItemType() == InvoiceItemTypeEnum.DISCOUNTED)
                .map(ApplyItemDO::getTotalPrice)
                .map(Math::negateExact)
                .reduce(0, Integer::sum);
        log.info("被折扣总金额 {}", MoneyUtil.centToYuan(totalDiscounted));
        // 计算分摊总折扣（负） = 总折扣（负） - 被折扣总金额（负）
        int totalSharingDiscount = totalDiscount - totalDiscounted;
        if (totalSharingDiscount > 0) {
            throw BizException.of("不允许开蓝票，被折扣总金额超过总折扣 [总折扣 %s 被折扣总金额 %s]",
                    MoneyUtil.centToYuan(totalDiscount), MoneyUtil.centToYuan(totalDiscounted));
        }
        log.info("分摊总折扣金额 {}", MoneyUtil.centToYuan(totalSharingDiscount));
        return totalSharingDiscount;
    }

    /**
     * 构建商品集合
     *
     * @param applyItems 发票申请项目列表
     * @return 商品集合
     */
    private Map<String, GoodsDO> buildGoodsMap(List<ApplyItemDO> applyItems) {
        // 汇总物料代码集合，过滤掉折扣行的 null 数据
        Set<String> erpCodes = applyItems.stream()
                .filter(item -> item.getItemType() != InvoiceItemTypeEnum.DISCOUNT)
                .map(ApplyItemDO::getErpCode)
                .collect(Collectors.toSet());
        // 获取商品数据集合
        Map<String, GoodsDO> goodsMap = goodsDao.mapByErpCode(erpCodes);
        // 校验物料代码全部存在
        erpCodes.stream()
                .filter(code -> !goodsMap.containsKey(code))
                .findAny()
                .ifPresent(code -> {
                    throw BizException.of("商品不存在 [%s]", code);
                });
        return goodsMap;
    }

    /**
     * 构建正常行，并分摊折扣
     * <p>分摊折扣时，正常行设置为被折扣行</p>
     *
     * @param invoiceIssueBO       发票业务数据
     * @param normalApplyItems     正常行列表
     * @param goodsMap             商品集合
     * @param totalSharingDiscount 分摊总折扣（负）
     */
    private void buildNormalInvoiceItems(InvoiceIssueBO invoiceIssueBO,
                                         List<ApplyItemDO> normalApplyItems,
                                         Map<String, GoodsDO> goodsMap,
                                         int totalSharingDiscount) {
        // 标记需要分摊折扣
        boolean isSharing = totalSharingDiscount < 0;
        // 汇总发票项目总金额（正）
        int normalTotalPrice = normalApplyItems.stream().map(ApplyItemDO::getTotalPrice).reduce(0, Integer::sum);
        // 正常行折扣分摊
        List<InvoiceItemDO> invoiceItems = Lists.newArrayList();
        normalApplyItems.forEach(applyItem -> {
            GoodsDO goods = goodsMap.get(applyItem.getErpCode());
            // 生成发票项目
            InvoiceItemDO invoiceItem = invoiceConverter.toInvoiceItemDO(applyItem, goods);
            invoiceItem.setItemNo(invoiceItems.size() + 1);
            // 分摊折扣时，正常行设置为被折扣行
            invoiceItem.setItemType(isSharing ? InvoiceItemTypeEnum.DISCOUNTED : InvoiceItemTypeEnum.NORMAL);
            invoiceItems.add(invoiceItem);
            // 生成折扣行
            if (isSharing) {
                InvoiceItemDO discountItem = new InvoiceItemDO();
                // 折扣行行号为被折扣行行号加 1
                discountItem.setItemNo(invoiceItem.getItemNo() + 1);
                discountItem.setItemType(InvoiceItemTypeEnum.DISCOUNT);
                discountItem.setGoodsName(goods.getGoodsName());
                discountItem.setTaxClassificationCode(goods.getTaxClassificationCode());
                discountItem.setTaxRate(goods.getTaxRate());
                // 分摊折扣金额（负）
                int sharingDiscount = this.calculateSharingDiscount(applyItem, totalSharingDiscount, normalTotalPrice);
                discountItem.setTotalPrice(sharingDiscount);
                invoiceItems.add(discountItem);
            }
        });
        // 分摊折扣时，计算折扣全部分摊完毕，未被分摊的差异金额会补充到折扣金额最小行
        if (isSharing) {
            this.calculateDiffDiscount(invoiceItems, totalSharingDiscount);
        }
        invoiceIssueBO.setInvoiceItems(invoiceItems);
    }

    /**
     * 计算分摊折扣金额（负）
     * <p>分摊折扣金额（负） = 分摊总折扣（负） * 当前行金额（正） / 总金额（正）</p>
     * <p>结果取两位小数</p>
     * <p>分摊折扣金额计算少于 0.01 则抛出异常</p>
     *
     * @param applyItem            开票申请项目
     * @param totalSharingDiscount 分摊总折扣（负）
     * @param totalPrice           总金额
     * @return 分摊折扣金额（负）
     */
    private int calculateSharingDiscount(ApplyItemDO applyItem, int totalSharingDiscount, int totalPrice) {
        // 当前行金额（正）
        BigDecimal itemYuan = MoneyUtil.centToYuan(applyItem.getTotalPrice());
        // 分摊总折扣（负）
        BigDecimal totalSharingDiscountYuan = MoneyUtil.centToYuan(totalSharingDiscount);
        // 总金额（正）
        BigDecimal totalPriceYuan = MoneyUtil.centToYuan(totalPrice);
        // 分摊折扣金额（负） = 总折扣金额（负） * 当前行金额（正） / 总金额（正）
        BigDecimal discountYuan = totalSharingDiscountYuan.multiply(itemYuan).divide(totalPriceYuan, 2, RoundingMode.DOWN);
        int discount = MoneyUtil.yuanToCent(discountYuan);
        if (discount > -0.01) {
            log.info("分摊折扣金额计算少于 0.01 [{}] 总折扣金额: {} 总金额: {} 商品金额: {} 分摊折扣金额: {}",
                    applyItem.getErpCode(), totalSharingDiscountYuan.toPlainString(), totalPriceYuan.toPlainString(),
                    itemYuan.toPlainString(), discountYuan.toPlainString());
            throw BizException.of("分摊折扣金额计算少于 0.01 [%s]", applyItem.getErpCode());
        }
        return discount;
    }

    /**
     * 计算折扣差异，补充到折扣金额最小行
     * <p>最终折扣金额（负） = 折扣最小行折扣金额（负） + 待补折扣金额（负）</p>
     *
     * @param invoiceItems         发票项目列表
     * @param totalSharingDiscount 分摊总折扣（负）
     */
    private void calculateDiffDiscount(List<InvoiceItemDO> invoiceItems, int totalSharingDiscount) {
        // 当前折扣金额合计（负）
        List<InvoiceItemDO> discountItems = invoiceItems.stream()
                .filter(item -> item.getItemType() == InvoiceItemTypeEnum.DISCOUNT).toList();
        int sumDiscount = discountItems.stream().map(InvoiceItemDO::getTotalPrice).reduce(0, Integer::sum);
        // 待补折扣金额 = 分摊总折扣（负） - 当前折扣金额合计（负）
        int diffDiscount = totalSharingDiscount - sumDiscount;
        log.info("待补折扣金额 {}", MoneyUtil.centToYuan(diffDiscount));
        // 待补折扣金额为 0 不需要补充，大于 0 抛出异常，小于 0 补充到折扣金额最小行
        if (diffDiscount == 0) {
            return;
        }
        if (diffDiscount > 0) {
            throw BizException.of("折扣计算异常，需人工检查");
        }
        // 待补折扣金额补充到折扣金额最小行
        discountItems.stream()
                .max(Comparator.comparing(InvoiceItemDO::getTotalPrice))
                .ifPresent(discountItem -> {
                    // 被折扣行金额（正），被折扣行是折扣行的前一行
                    int discountedPrice = invoiceItems.stream()
                            .filter(item -> item.getItemNo() == discountItem.getItemNo() - 1)
                            .findAny().map(InvoiceItemDO::getTotalPrice).orElse(0);
                    // 被折扣行剩余可被折扣金额（正） = 被折扣行金额（正） + 折扣行金额（负）
                    int remainingDiscount = discountedPrice + discountItem.getTotalPrice();
                    if (remainingDiscount <= 0 || remainingDiscount + diffDiscount < 0) {
                        throw BizException.of("“待补折扣金额”不足以补充到“折扣金额最小行” [待补折扣金额 %s 被折扣行剩余金额 %s]",
                                MoneyUtil.centToYuan(diffDiscount), MoneyUtil.centToYuan(remainingDiscount));
                    }
                    discountItem.setTotalPrice(discountItem.getTotalPrice() + diffDiscount);
                });
    }

    /**
     * 构建被折扣行
     *
     * @param invoiceIssueBO       发票业务数据
     * @param discountedApplyItems 被折扣行列表
     * @param goodsMap             商品集合
     */
    private void buildDiscountedInvoiceItems(InvoiceIssueBO invoiceIssueBO,
                                             List<ApplyItemDO> discountedApplyItems,
                                             Map<String, GoodsDO> goodsMap) {
        List<InvoiceItemDO> invoiceItems = invoiceIssueBO.getInvoiceItems();
        discountedApplyItems.forEach(applyItem -> {
            GoodsDO goods = goodsMap.get(applyItem.getErpCode());
            // 生成发票项目
            InvoiceItemDO invoiceItem = invoiceConverter.toInvoiceItemDO(applyItem, goods);
            invoiceItem.setItemNo(invoiceItems.size() + 1);
            invoiceItem.setItemType(InvoiceItemTypeEnum.DISCOUNTED);
            invoiceItems.add(invoiceItem);
            // 生成折扣行
            InvoiceItemDO discountItem = new InvoiceItemDO();
            discountItem.setItemNo(invoiceItem.getItemNo() + 1);
            discountItem.setItemType(InvoiceItemTypeEnum.DISCOUNT);
            discountItem.setGoodsName(goods.getGoodsName());
            discountItem.setTaxClassificationCode(goods.getTaxClassificationCode());
            discountItem.setTaxRate(goods.getTaxRate());
            // 被折扣行金额全部扣掉（负）
            discountItem.setTotalPrice(Math.negateExact(applyItem.getTotalPrice()));
            invoiceItems.add(discountItem);
        });
    }

    /**
     * 调用平台开蓝票，并更新开票业务数据
     *
     * @param invoiceIssueBO 开票业务数据
     */
    private void rpcCallAndUpdateInvoice(InvoiceIssueBO invoiceIssueBO) {
        // 初始化请求参数
        OutputInvoiceIssueRequest request = baiWangInvoiceConverter.toOutputInvoiceIssueRequest(invoiceIssueBO.getInvoice());
        // 生成发票项目列表
        List<OutputInvoiceIssueInvoiceDetail> requestItems = baiWangInvoiceConverter.toOutputInvoiceIssueInvoiceDetail(invoiceIssueBO.getInvoiceItems());
        request.getData().setInvoiceDetailsList(requestItems);
        // 调用平台开蓝票接口
        OutputInvoiceIssueResponse rpcResponse = baiWangInvoiceRpc.issueBlue(request);
        // 更新开票业务数据
        this.updateInvoiceIssueBO(invoiceIssueBO, rpcResponse);
    }

    /**
     * 更新开票业务数据
     *
     * @param invoiceIssueBO 开票业务数据
     * @param rpcResponse    平台开票结果
     */
    private void updateInvoiceIssueBO(InvoiceIssueBO invoiceIssueBO, OutputInvoiceIssueResponse rpcResponse) {
        InvoiceApplyDO apply = invoiceIssueBO.getApply();
        // 处理开票成功数据
        List<OutputInvoiceIssueInvoiceResult> successList = rpcResponse.getModel().getSuccess();
        if (CollectionUtils.isNotEmpty(successList)) {
            // 转换开票数据
            OutputInvoiceIssueInvoiceResult invoiceResult = successList.getFirst();
            baiWangInvoiceConverter.updateInvoiceDO(invoiceIssueBO.getInvoice(), invoiceResult);
            // 转换开票项目数据，通过行号匹配
            Map<Integer, InvoiceItemDO> invoiceItemsMap = invoiceIssueBO.getInvoiceItems().stream()
                    .collect(Collectors.toMap(InvoiceItemDO::getItemNo, Function.identity()));
            invoiceResult.getInvoiceDetailsList().forEach(itemResult -> {
                InvoiceItemDO invoiceItem = invoiceItemsMap.get(itemResult.getGoodsLineNo());
                if (invoiceItem == null) {
                    throw BizException.of("更新开票业务数据异常，发票项目行号不匹配 [%s]", itemResult.getGoodsLineNo());
                }
                // 非折扣行，更新发票项目单价
                if (itemResult.getGoodsPrice() != null && invoiceItem.getItemType() != InvoiceItemTypeEnum.DISCOUNT) {
                    invoiceItem.setUnitPrice(MoneyUtil.yuanToCent(itemResult.getGoodsPrice()));
                }
                // 更新发票项目税额
                if (itemResult.getGoodsTotalTax() != null) {
                    invoiceItem.setTaxAmount(MoneyUtil.yuanToCent(itemResult.getGoodsTotalTax()));
                }
            });
            // 更新申请状态，生成开票成功申请记录
            ApplyStateEnum newApplyState = ApplyStateEnum.ACCEPTED;
            ApplyHistoryDO.add(apply.getApplyHistory(), newApplyState);
            apply.setApplyState(newApplyState);
        } else {
            // 处理开票失败数据
            List<OutputInvoiceIssueInvoice> failList = rpcResponse.getModel().getFail();
            if (CollectionUtils.isNotEmpty(failList)) {
                // 生成开票失败申请记录
                ApplyHistoryDO.add(apply.getApplyHistory(), ApplyStateEnum.FAILED, "平台开票失败");
            }
        }
    }

    /**
     * 保存发票数据
     *
     * @param invoiceIssueBO 发票业务数据
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void saveInvoiceData(InvoiceIssueBO invoiceIssueBO) {
        // 新增发票
        InvoiceDO invoice = invoiceIssueBO.getInvoice();
        invoiceDao.save(invoice);
        // 新增发票项目
        List<InvoiceItemDO> invoiceItems = invoiceIssueBO.getInvoiceItems();
        invoiceItems.forEach(item -> {
            item.setInvoiceId(invoice.getInvoiceId());
            item.setSerialNo(invoice.getSerialNo());
            item.setInvoiceNo(invoice.getBlueInvoiceNo());
        });
        invoiceItemDao.saveBatch(invoiceItems);
        // 更新发票申请
        InvoiceApplyDO apply = invoiceIssueBO.getApply();
        apply.setInvoiceId(invoice.getInvoiceId());
        invoiceApplyDao.updateByApplyId(apply);
    }

}
