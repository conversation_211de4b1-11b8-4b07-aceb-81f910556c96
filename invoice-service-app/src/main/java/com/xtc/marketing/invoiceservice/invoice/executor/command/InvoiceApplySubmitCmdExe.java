package com.xtc.marketing.invoiceservice.invoice.executor.command;

import com.xtc.marketing.invoiceservice.cache.BizOrderOperateLockCacheClient;
import com.xtc.marketing.invoiceservice.exception.BizException;
import com.xtc.marketing.invoiceservice.invoice.converter.InvoiceConverter;
import com.xtc.marketing.invoiceservice.invoice.dao.InvoiceApplyDao;
import com.xtc.marketing.invoiceservice.invoice.dataobject.BusinessDO;
import com.xtc.marketing.invoiceservice.invoice.dataobject.BuyerDO;
import com.xtc.marketing.invoiceservice.invoice.dataobject.InvoiceApplyDO;
import com.xtc.marketing.invoiceservice.invoice.dataobject.SellerDO;
import com.xtc.marketing.invoiceservice.invoice.dataobject.jsonobject.ApplyHistoryDO;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceApplyDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.command.ApplyItemCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.command.InvoiceApplySubmitCmd;
import com.xtc.marketing.invoiceservice.invoice.enums.ApplyStateEnum;
import com.xtc.marketing.invoiceservice.invoice.enums.CreateTypeEnum;
import com.xtc.marketing.invoiceservice.invoice.enums.InvoiceItemTypeEnum;
import com.xtc.marketing.invoiceservice.invoice.executor.query.BusinessGetQryExe;
import com.xtc.marketing.invoiceservice.invoice.executor.query.BuyerGetQryExe;
import com.xtc.marketing.invoiceservice.invoice.executor.query.SellerGetQryExe;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Comparator;

/**
 * 发票申请提交命令执行器
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class InvoiceApplySubmitCmdExe {

    // executor 注入
    private final BusinessGetQryExe businessGetQryExe;
    private final BuyerGetQryExe buyerGetQryExe;
    private final SellerGetQryExe sellerGetQryExe;
    // 基础设施层注入
    private final InvoiceConverter invoiceConverter;
    private final InvoiceApplyDao invoiceApplyDao;
    private final BizOrderOperateLockCacheClient bizOrderOperateLockCacheClient;

    /**
     * 提交发票申请
     *
     * @param cmd 参数
     * @return 发票申请
     */
    public InvoiceApplyDTO execute(InvoiceApplySubmitCmd cmd) {
        // 校验参数
        this.checkCmdAndInitCmd(cmd);
        // 发票项目数据清洗
        cmd.getApplyItems().removeIf(this::applyItemRemove);
        // 发票项目数据金额从大到小排序
        cmd.getApplyItems().sort(Comparator.comparing(ApplyItemCmd::getTotalPrice).reversed());
        // 订单号加锁
        return bizOrderOperateLockCacheClient.execute(cmd.getBizOrderId(), () -> {
            // 校验业务数据
            this.checkBizData(cmd);
            // 取消订单号所有的待开票申请
            invoiceApplyDao.cancelPendingByBizOrderId(cmd.getBizOrderId(), ApplyHistoryDO.of(ApplyStateEnum.CANCEL));
            // 初始化发票申请
            InvoiceApplyDO invoiceApplyDO = invoiceConverter.toInvoiceApplyDO(cmd);
            // 新增发票申请
            invoiceApplyDao.save(invoiceApplyDO);
            if (StringUtils.isBlank(invoiceApplyDO.getApplyId())) {
                throw BizException.of("提交发票申请失败");
            }
            return invoiceConverter.toInvoiceApplyDTO(invoiceApplyDO);
        });
    }

    /**
     * 校验参数并初始化参数
     *
     * @param cmd 参数
     */
    private void checkCmdAndInitCmd(InvoiceApplySubmitCmd cmd) {
        // 校验购买方并初始化
        this.checkBuyerAndInit(cmd);
        // 开蓝票必填校验
        if (cmd.getCreateType() == CreateTypeEnum.BLUE) {
            if (CollectionUtils.isEmpty(cmd.getApplyItems())) {
                throw BizException.of("开蓝票必填发票项目");
            }
            cmd.getApplyItems().stream()
                    .filter(item -> item.getItemType() == InvoiceItemTypeEnum.NORMAL)
                    .filter(item -> StringUtils.isBlank(item.getErpCode()) || item.getNum() == null)
                    .findAny()
                    .ifPresent(item -> {
                        throw BizException.of("发票项目的正常行，必填物料代码、数量");
                    });
        }
        // 开红票必填校验
        if (cmd.getCreateType() == CreateTypeEnum.RED && StringUtils.isBlank(cmd.getBlueInvoiceId())) {
            throw BizException.of("开红票必填蓝票的发票id");
        }
        // 折扣行不需要填物料代码和数量
        cmd.getApplyItems().stream()
                .filter(item -> item.getItemType() == InvoiceItemTypeEnum.DISCOUNT)
                .filter(item -> StringUtils.isNotBlank(item.getErpCode()) || item.getNum() != null)
                .findAny()
                .ifPresent(item -> {
                    throw BizException.of("折扣行不需要填物料代码和数量");
                });
        // 参数校验：bizCode、sellerCode
        BusinessDO businessDO = businessGetQryExe.byBizCode(cmd.getBizCode());
        if (BooleanUtils.isNotTrue(businessDO.getEnabled())) {
            throw BizException.of("业务未启用");
        }
        SellerDO sellerDO = sellerGetQryExe.bySellerCode(cmd.getSellerCode());
        if (BooleanUtils.isNotTrue(sellerDO.getEnabled())) {
            throw BizException.of("销售方未启用");
        }
    }

    /**
     * 校验购买方并初始化
     *
     * @param cmd 参数
     */
    private void checkBuyerAndInit(InvoiceApplySubmitCmd cmd) {
        if (StringUtils.isBlank(cmd.getBuyerCode())) {
            return;
        }
        BuyerDO buyerDO = buyerGetQryExe.byBuyerCodeWithoutMask(cmd.getBuyerCode());
        if (BooleanUtils.isNotTrue(buyerDO.getEnabled())) {
            throw BizException.of("购买方未启用");
        }
        // 初始化购买方信息
        cmd.setInvoiceTitle(buyerDO.getBuyerName());
        cmd.setBuyerIdentifyNo(buyerDO.getBuyerIdentifyNo());
        cmd.setBuyerPhone(buyerDO.getBuyerPhone());
        cmd.setBuyerAddress(buyerDO.getBuyerAddress());
        cmd.setBuyerBankName(buyerDO.getBuyerBankName());
        cmd.setBuyerBankAccount(buyerDO.getBuyerBankAccount());
        if (StringUtils.isBlank(cmd.getInvoiceTitle())) {
            throw BizException.of("发票抬头不能为空");
        }
    }

    /**
     * 校验业务数据
     *
     * @param cmd 参数
     */
    private void checkBizData(InvoiceApplySubmitCmd cmd) {
        boolean existsApplyAccepted = invoiceApplyDao.existsAcceptedByBizOrderId(cmd.getBizOrderId());
        if (existsApplyAccepted) {
            throw BizException.of("业务订单正在开票中，请稍后重试");
        }
        boolean alreadyIssue = invoiceApplyDao.alreadyIssueByBizOrderIdAndSerialNo(cmd.getBizOrderId(), cmd.getSerialNo());
        if (alreadyIssue) {
            throw BizException.of("开票流水号已成功开票，请勿重复提交 [%s]", cmd.getSerialNo());
        }
    }

    /**
     * 清洗发票项目
     * <p>数据清洗规则：<p/>
     * <ul>
     * <li>金额为 0 的非折扣行项目</li>
     * </ul>
     *
     * @param item 发票项目
     * @return 执行结果
     */
    private boolean applyItemRemove(ApplyItemCmd item) {
        return item.getItemType() != InvoiceItemTypeEnum.DISCOUNT && item.getTotalPrice() == 0;
    }

}
