package com.xtc.marketing.invoiceservice.job;

import com.xtc.marketing.invoiceservice.invoice.dao.InvoiceApplyDao;
import com.xtc.marketing.invoiceservice.invoice.dataobject.InvoiceApplyDO;
import com.xtc.marketing.invoiceservice.invoice.dto.command.InvoiceFileSyncCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.command.InvoiceSyncCmd;
import com.xtc.marketing.invoiceservice.invoice.executor.command.InvoiceFileSyncCmdExe;
import com.xtc.marketing.invoiceservice.invoice.executor.command.InvoiceSyncCmdExe;
import com.xtc.marketing.invoiceservice.config.BaseJobBO;
import com.xtc.marketing.invoiceservice.config.BaseJobExe;
import com.xtc.marketing.invoiceservice.job.executor.InvoiceIssueJobExe;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 发票任务服务实现类
 * <p>简单逻辑 10 行以内的代码可以直接在 service 方法内实现</p>
 * <p>复杂逻辑（或可复用逻辑）封装在 executor 里实现，调用链路：service > executor</p>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class InvoiceJobServiceImpl implements InvoiceJobService {

    // executor 注入
    private final InvoiceIssueJobExe invoiceIssueJobExe;
    private final InvoiceSyncCmdExe invoiceSyncCmdExe;
    private final InvoiceFileSyncCmdExe invoiceFileSyncCmdExe;
    // 基础设施层注入
    private final InvoiceApplyDao invoiceApplyDao;

    @Override
    public void invoiceIssueJob(int shardIndex, BaseJobBO param) {
        invoiceIssueJobExe.execute(shardIndex, param);
    }

    @Override
    public void invoiceSyncJob(int shardIndex, BaseJobBO param) {
        BaseJobExe.execute(
                param,
                InvoiceApplyDO::getApplyId,
                () -> invoiceApplyDao.listAcceptedByUpdateTimeAsc(param.getStartTime(), param.getEndTime(), shardIndex, param.getLimit()),
                apply -> {
                    InvoiceSyncCmd invoiceSyncCmd = InvoiceSyncCmd.builder().invoiceId(apply.getInvoiceId()).build();
                    invoiceSyncCmdExe.execute(invoiceSyncCmd);
                }
        );
    }

    @Override
    public void invoiceFileSyncJob(int shardIndex, BaseJobBO param) {
        BaseJobExe.execute(
                param,
                InvoiceApplyDO::getApplyId,
                () -> invoiceApplyDao.listAcceptedByUpdateTimeAsc(param.getStartTime(), param.getEndTime(), shardIndex, param.getLimit()),
                apply -> {
                    InvoiceFileSyncCmd invoiceFileSyncCmd = InvoiceFileSyncCmd.builder().invoiceId(apply.getInvoiceId()).build();
                    invoiceFileSyncCmdExe.execute(invoiceFileSyncCmd);
                }
        );
    }

}
