package com.xtc.marketing.invoiceservice.job;

import com.xtc.marketing.invoiceservice.job.executor.BatchIssueRedJobExe;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 批处理任务服务实现类
 * <p>简单逻辑 10 行以内的代码可以直接在 service 方法内实现</p>
 * <p>复杂逻辑（或可复用逻辑）封装在 executor 里实现，调用链路：service > executor</p>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BatchJobServiceImpl implements BatchJobService {

    private final BatchIssueRedJobExe batchIssueRedJobExe;

    @Override
    public void notSystemIssueRedBatchJob(int shardIndex, int shardTotal) {
        batchIssueRedJobExe.execute(shardIndex, shardTotal);
    }

}
