package com.xtc.marketing.invoiceservice.invoice.executor.command;

import com.baiwang.ispsdk.entity.response.OutputFormatCreateResponse;
import com.baiwang.ispsdk.entity.response.OutputFormatQueryQdInvoiceResponse;
import com.baiwang.ispsdk.entity.response.node.OutputFormatCreate;
import com.baiwang.ispsdk.entity.response.node.OutputFormatCreateUrlMap;
import com.baiwang.ispsdk.entity.response.node.OutputFormatQueryQdInvoice;
import com.xtc.marketing.invoiceservice.exception.BizException;
import com.xtc.marketing.invoiceservice.exception.SysErrorCode;
import com.xtc.marketing.invoiceservice.exception.SysException;
import com.xtc.marketing.invoiceservice.invoice.bo.InvoiceFileBO;
import com.xtc.marketing.invoiceservice.invoice.dao.FileDao;
import com.xtc.marketing.invoiceservice.invoice.dataobject.FileDO;
import com.xtc.marketing.invoiceservice.invoice.dataobject.InvoiceDO;
import com.xtc.marketing.invoiceservice.invoice.domainservice.InvoiceFileService;
import com.xtc.marketing.invoiceservice.invoice.dto.command.InvoiceFileSyncCmd;
import com.xtc.marketing.invoiceservice.invoice.enums.CreateTypeEnum;
import com.xtc.marketing.invoiceservice.invoice.executor.query.InvoiceGetQryExe;
import com.xtc.marketing.invoiceservice.oss.MarketingInvoiceOssClient;
import com.xtc.marketing.invoiceservice.rpc.BaiWangInvoiceRpc;
import com.xtc.marketing.invoiceservice.util.DateUtil;
import com.xtc.marketing.invoiceservice.util.GsonUtil;
import com.xtc.marketing.invoiceservice.util.HttpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * 发票文件同步命令执行器
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class InvoiceFileSyncCmdExe {

    // executor 注入
    private final InvoiceGetQryExe invoiceGetQryExe;
    // 基础设施层注入
    private final BaiWangInvoiceRpc baiWangInvoiceRpc;
    private final MarketingInvoiceOssClient marketingInvoiceOssClient;
    private final FileDao fileDao;
    private final InvoiceFileService invoiceFileService;

    /**
     * 发票文件根路径
     */
    private static final String INVOICE_PATH = "invoice/%s/%s/%s/%s";

    /**
     * 发票文件同步
     *
     * @param cmd 参数
     * @return 发票文件
     */
    public String execute(InvoiceFileSyncCmd cmd) {
        InvoiceDO invoice = invoiceGetQryExe.byInvoiceId(cmd.getInvoiceId());
        // 查询发票文件，未查到则生成发票文件
        List<InvoiceFileBO> invoiceFiles = this.queryFile(invoice);
        if (invoiceFiles.size() < 3) {
            invoiceFiles = this.createFile(invoice);
        }
        // 保存发票文件到 OSS
        List<FileDO> files = invoiceFiles.stream()
                .map(invoiceFile -> this.saveOss(invoice, invoiceFile))
                .toList();
        // 批量保存文件数据
        fileDao.insertOrUpdateBatchByInvoiceIdAndFileType(files);
        // 生成发票文件的 pdf 地址
        return invoiceFileService.createPdfUrl(invoice.getInvoiceId()).orElse(null);
    }

    /**
     * 查询发票文件
     *
     * @param invoice 发票
     * @return 发票文件集合
     */
    private List<InvoiceFileBO> queryFile(InvoiceDO invoice) {
        String invoiceNo = invoice.getCreateType() == CreateTypeEnum.BLUE ? invoice.getBlueInvoiceNo() : invoice.getRedInvoiceNo();
        OutputFormatQueryQdInvoice rpcData;
        try {
            OutputFormatQueryQdInvoiceResponse rpcResponse = baiWangInvoiceRpc.queryFile(invoice.getSellerIdentifyNo(), invoiceNo);
            rpcData = rpcResponse.getModel();
            if (rpcData == null || StringUtils.isBlank(rpcData.getPdfUrl())) {
                throw SysException.of(SysErrorCode.S_RPC_ERROR, GsonUtil.objectToJson(rpcResponse));
            }
        } catch (Exception e) {
            log.info("未查询到发票文件 message: {}", e.getMessage());
            return List.of();
        }
        return List.of(
                InvoiceFileBO.builder().url(rpcData.getPdfUrl()).fileType("pdf").contentType(MediaType.APPLICATION_PDF_VALUE).build(),
                InvoiceFileBO.builder().url(rpcData.getXmlUrl()).fileType("xml").contentType(MediaType.APPLICATION_XML_VALUE).build(),
                InvoiceFileBO.builder().url(rpcData.getOfdUrl()).fileType("ofd").build()
        );
    }

    /**
     * 保存发票文件到 OSS
     *
     * @param invoice     发票
     * @param invoiceFile 发票文件
     * @return 文件
     */
    private FileDO saveOss(InvoiceDO invoice, InvoiceFileBO invoiceFile) {
        // 存储对象：202501/20250101/BLUE/b1_1111111.pdf
        LocalDate now = LocalDate.now();
        String yearMonth = DateUtil.toString(now, DateUtil.FORMAT_YEAR_MONTH_COMPACT);
        String date = DateUtil.toString(now, DateUtil.FORMAT_DATE_COMPACT);
        String fileName = "%s.%s".formatted(invoice.getSerialNo(), invoiceFile.getFileType());
        String objectName = INVOICE_PATH.formatted(yearMonth, date, invoice.getCreateType(), fileName);
        try {
            byte[] pdfBytes = HttpUtil.getWithoutEncode(invoiceFile.getUrl(), byte[].class);
            ByteArrayInputStream stream = new ByteArrayInputStream(pdfBytes);
            marketingInvoiceOssClient.putObject(objectName, stream, invoiceFile.getContentType());
        } catch (Exception e) {
            // 如果保存失败，重新生成发票文件，下次再同步
            this.retryCreateFile(invoice);
            String message = "保存发票文件失败，已重新生成发票文件，请稍后重试 %s: %s"
                    .formatted(invoiceFile.getFileType(), invoiceFile.getUrl());
            throw BizException.of(message, e.getCause());
        }
        return FileDO.builder()
                .invoiceId(invoice.getInvoiceId())
                .fileName(fileName)
                .objectName(objectName)
                .fileType(invoiceFile.getFileType())
                .contentType(invoiceFile.getContentType())
                .build();
    }

    /**
     * 生成发票文件
     *
     * @param invoice 发票
     * @return 发票文件集合
     */
    private List<InvoiceFileBO> createFile(InvoiceDO invoice) {
        OutputFormatCreateResponse rpcResponse = baiWangInvoiceRpc.createFile(invoice.getSellerIdentifyNo(), invoice.getSerialNo());
        OutputFormatCreateUrlMap rpcData = Optional.ofNullable(rpcResponse.getModel())
                .map(OutputFormatCreate::getUrlMap)
                .orElseThrow(() -> BizException.of("生成发票文件失败"));
        return List.of(
                InvoiceFileBO.builder().url(rpcData.getPdfUrl()).fileType("pdf").contentType(MediaType.APPLICATION_PDF_VALUE).build(),
                InvoiceFileBO.builder().url(rpcData.getXmlUrl()).fileType("xml").contentType(MediaType.APPLICATION_XML_VALUE).build(),
                InvoiceFileBO.builder().url(rpcData.getOfdUrl()).fileType("ofd").contentType(MediaType.APPLICATION_OCTET_STREAM_VALUE).build()
        );
    }

    /**
     * 重新生成发票文件
     *
     * @param invoice 发票
     */
    private void retryCreateFile(InvoiceDO invoice) {
        try {
            baiWangInvoiceRpc.createFile(invoice.getSellerIdentifyNo(), invoice.getSerialNo());
        } catch (Exception e) {
            log.warn("重新生成发票文件失败 {} message: {}", invoice.getInvoiceId(), e.getMessage());
        }
    }

}
