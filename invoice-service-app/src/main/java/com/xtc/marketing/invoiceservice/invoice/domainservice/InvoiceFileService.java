package com.xtc.marketing.invoiceservice.invoice.domainservice;

import com.xtc.marketing.invoiceservice.constant.SystemConstant;
import com.xtc.marketing.invoiceservice.exception.BizException;
import com.xtc.marketing.invoiceservice.invoice.dao.FileDao;
import com.xtc.marketing.invoiceservice.invoice.dataobject.FileDO;
import com.xtc.marketing.invoiceservice.util.DateUtil;
import com.xtc.marketing.invoiceservice.util.JwtUtil;
import io.jsonwebtoken.ExpiredJwtException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;

/**
 * 发票文件领域服务
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class InvoiceFileService {

    private final FileDao fileDao;

    /**
     * API：获取发票文件
     */
    private static final String API_GET_FILE = "%s/api/invoice/file?expire=%s&fileToken=%s";
    /**
     * JWT：密钥
     */
    private static final String JWT_SECRET_KEY = "V1NJOVVRR0ZSQ2pUWi0tdFRiWXN1S0ZoWlEyeDJoclBiRHk4ZFZUQ01oQ2llN01JUERCR2owZThzMElhYXc1eQ==";
    /**
     * JWT：过期时间，时间戳（毫秒级）
     */
    private static final Duration JWT_EXPIRATION = Duration.ofHours(12);

    @Value("${spring.profiles.active}")
    private String activeProfile;

    /**
     * 生成发票文件的 pdf 地址
     *
     * @param invoiceId 发票id
     * @return 发票文件
     */
    public Optional<String> createPdfUrl(String invoiceId) {
        Optional<FileDO> pdfOpt = fileDao.getByInvoiceIdAndFileType(invoiceId, "pdf");
        if (pdfOpt.isEmpty()) {
            return Optional.empty();
        }
        FileDO pdf = pdfOpt.get();
        // 域名
        String domain = SystemConstant.getSystemDomain(activeProfile);
        // 过期时间
        String expire = DateUtil.toString(LocalDateTime.now().plus(JWT_EXPIRATION), DateUtil.FORMAT_DATE_TIME_COMPACT);
        // 生成发票文件凭证
        Map<String, Object> claims = Map.of("fileName", pdf.getFileName());
        String fileToken = JwtUtil.generateJwt(JWT_SECRET_KEY, JWT_EXPIRATION, pdf.getFileId(), claims);
        // 生成获取发票文件地址
        return Optional.of(API_GET_FILE.formatted(domain, expire, fileToken));
    }

    /**
     * 获取文件id
     *
     * @param fileToken 发票文件凭证
     * @return 文件id
     */
    public Optional<String> getFileIdByToken(String fileToken) {
        try {
            String fileId = JwtUtil.parseJwtSubject(fileToken, JWT_SECRET_KEY);
            return Optional.ofNullable(fileId);
        } catch (ExpiredJwtException e1) {
            throw BizException.of("文件凭证已过期");
        } catch (Exception e) {
            throw BizException.of("无效的文件凭证");
        }
    }

}
