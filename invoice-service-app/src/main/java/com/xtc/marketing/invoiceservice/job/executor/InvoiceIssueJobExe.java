package com.xtc.marketing.invoiceservice.job.executor;

import com.google.common.collect.Lists;
import com.xtc.marketing.invoiceservice.config.BaseJobExe;
import com.xtc.marketing.invoiceservice.exception.BizException;
import com.xtc.marketing.invoiceservice.invoice.dao.InvoiceApplyDao;
import com.xtc.marketing.invoiceservice.invoice.dataobject.InvoiceApplyDO;
import com.xtc.marketing.invoiceservice.invoice.dataobject.jsonobject.ApplyHistoryDO;
import com.xtc.marketing.invoiceservice.invoice.dto.command.InvoiceIssueCmd;
import com.xtc.marketing.invoiceservice.invoice.enums.ApplyStateEnum;
import com.xtc.marketing.invoiceservice.invoice.executor.command.InvoiceIssueBlueCmdExe;
import com.xtc.marketing.invoiceservice.invoice.executor.command.InvoiceIssueRedCmdExe;
import com.xtc.marketing.invoiceservice.invoice.executor.query.InvoiceApplyGetQryExe;
import com.xtc.marketing.invoiceservice.config.BaseJobBO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 开票任务执行器
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class InvoiceIssueJobExe {

    // executor 注入
    private final InvoiceApplyGetQryExe invoiceApplyGetQryExe;
    private final InvoiceIssueBlueCmdExe invoiceIssueBlueCmdExe;
    private final InvoiceIssueRedCmdExe invoiceIssueRedCmdExe;
    // 基础设施层注入
    private final InvoiceApplyDao invoiceApplyDao;

    /**
     * 开票任务
     *
     * @param shardIndex 分片索引
     * @param param      任务参数
     */
    public void execute(int shardIndex, BaseJobBO param) {
        BaseJobExe.execute(
                param,
                InvoiceApplyDO::getApplyId,
                () -> invoiceApplyDao.listPendingByUpdateTimeAsc(param.getStartTime(), param.getEndTime(), shardIndex, param.getLimit()),
                apply -> {
                    // 查询最新的发票申请数据
                    InvoiceApplyDO applyRuntime = invoiceApplyGetQryExe.byApplyIdWithoutMask(apply.getApplyId());
                    if (applyRuntime.getApplyState().notAllowIssue()) {
                        log.info("当前状态无需开票 [{}]", applyRuntime.getApplyState().getDesc());
                        return;
                    }
                    // 开蓝票或开红票，根据开票类型选择
                    InvoiceIssueCmd cmd = InvoiceIssueCmd.builder().applyId(apply.getApplyId()).build();
                    switch (apply.getCreateType()) {
                        case BLUE:
                            invoiceIssueBlueCmdExe.execute(cmd);
                            break;
                        case RED:
                            invoiceIssueRedCmdExe.execute(cmd);
                            break;
                        default:
                            throw BizException.of("不支持的开票类型 [%s]", apply.getCreateType());
                    }
                },
                (apply, e) -> this.updateApplyIssueFail(apply, e.getMessage())
        );
    }

    /**
     * 更新发票申请数据
     *
     * @param apply         发票申请
     * @param historyRemark 申请记录备注
     */
    private void updateApplyIssueFail(InvoiceApplyDO apply, String historyRemark) {
        InvoiceApplyDO updateApply = new InvoiceApplyDO();
        updateApply.setApplyId(apply.getApplyId());
        // 更新发票申请状态
        if (this.isIssueFailed(apply)) {
            updateApply.setApplyState(ApplyStateEnum.FAILED);
        }
        // 添加申请记录
        updateApply.setApplyHistory(Lists.newArrayList(apply.getApplyHistory()));
        ApplyHistoryDO.add(updateApply.getApplyHistory(), ApplyStateEnum.FAILED, historyRemark);
        invoiceApplyDao.updateByApplyId(updateApply);
        log.info("更新发票申请状态 [{}]", updateApply.getApplyState());
    }

    /**
     * 判断开票失败
     * <p>连续失败次数不超过 3 次</p>
     *
     * @param apply 发票申请
     * @return 执行结果
     */
    private boolean isIssueFailed(InvoiceApplyDO apply) {
        if (CollectionUtils.isEmpty(apply.getApplyHistory())) {
            return true;
        }
        // 截取前面 2 条申请记录
        List<ApplyHistoryDO> historyHead = apply.getApplyHistory().size() > 2
                ? apply.getApplyHistory().subList(0, 2) : apply.getApplyHistory();
        // 统计失败次数，因为当前失败的申请记录也算一次，所以从 1 开始累计
        int failedCount = 1;
        for (ApplyHistoryDO history : historyHead) {
            if (ApplyStateEnum.FAILED.getDesc().equals(history.getHandleDesc())) {
                failedCount++;
            } else {
                // 如果有其他状态的申请记录，则不算连续失败
                return false;
            }
        }
        return failedCount >= 3;
    }

}
