package com.xtc.marketing.invoiceservice.invoice.executor.query;

import com.xtc.marketing.invoiceservice.exception.BizException;
import com.xtc.marketing.invoiceservice.invoice.dao.SellerDao;
import com.xtc.marketing.invoiceservice.invoice.dataobject.SellerDO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 销售方查询执行器
 */
@RequiredArgsConstructor
@Component
public class SellerGetQryExe {

    private final SellerDao sellerDao;

    /**
     * 查询销售方
     *
     * @param sellerCode 销售方代码
     * @return 销售方
     */
    public SellerDO bySellerCode(String sellerCode) {
        return sellerDao.getBySellerCode(sellerCode).orElseThrow(this::notFoundException);
    }

    /**
     * 查询销售方
     *
     * @param sellerIdentifyNo 销售方税号
     * @return 销售方
     */
    public SellerDO bySellerIdentifyNo(String sellerIdentifyNo) {
        return sellerDao.getBySellerIdentifyNo(sellerIdentifyNo).orElseThrow(this::notFoundException);
    }

    /**
     * 不存在异常
     *
     * @return BizException 业务异常
     */
    private BizException notFoundException() {
        return BizException.of("销售方不存在");
    }

}
