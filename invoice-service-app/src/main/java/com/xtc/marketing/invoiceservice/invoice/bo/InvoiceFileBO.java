package com.xtc.marketing.invoiceservice.invoice.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 发票文件数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceFileBO {

    /**
     * 文件地址
     */
    private String url;
    /**
     * 文件类型
     */
    private String fileType;
    /**
     * 文件内容类型
     * <p><a href="https://www.iana.org/assignments/media-types/media-types.xhtml">CONTENT-TYPE with the desired content type (also called a media type)</a></p>
     */
    private String contentType;

}
