package com.xtc.marketing.invoiceservice.invoice;

import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.command.NormalIssueRedCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.query.NotSystemFileGetQry;
import com.xtc.marketing.invoiceservice.invoice.dto.query.NotSystemRedDetailQry;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;

/**
 * 非系统数据模块应用服务
 */
public interface NotSystemInvoiceAppService {

    /**
     * 非系统文件获取
     *
     * @param qry 参数
     * @return 文件
     */
    ResponseEntity<Resource> invoiceFile(NotSystemFileGetQry qry);

    /**
     * 非系统红票详情
     *
     * @param qry 参数
     * @return 发票详情
     */
    InvoiceDTO notSystemRedDetail(NotSystemRedDetailQry qry);

    /**
     * 税控发票冲红
     *
     * @param cmd 参数
     * @return 发票详情
     */
    InvoiceDTO normalIssueRed(NormalIssueRedCmd cmd);

}
