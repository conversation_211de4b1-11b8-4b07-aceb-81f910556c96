package com.xtc.marketing.invoiceservice.invoice.executor.command;

import com.baiwang.ispsdk.entity.request.OutputRedinvoiceAddRequest;
import com.baiwang.ispsdk.entity.request.node.OutputRedinvoiceAddObjectType;
import com.baiwang.ispsdk.entity.response.OutputRedinvoiceRedforminfoResponse;
import com.baiwang.ispsdk.entity.response.node.OutputRedinvoiceRedforminfo;
import com.xtc.marketing.invoiceservice.cache.BizOrderOperateLockCacheClient;
import com.xtc.marketing.invoiceservice.exception.BizException;
import com.xtc.marketing.invoiceservice.invoice.bo.InvoiceIssueBO;
import com.xtc.marketing.invoiceservice.invoice.converter.BaiWangInvoiceConverter;
import com.xtc.marketing.invoiceservice.invoice.converter.InvoiceConverter;
import com.xtc.marketing.invoiceservice.invoice.dao.InvoiceApplyDao;
import com.xtc.marketing.invoiceservice.invoice.dao.InvoiceDao;
import com.xtc.marketing.invoiceservice.invoice.dao.InvoiceItemDao;
import com.xtc.marketing.invoiceservice.invoice.dataobject.InvoiceApplyDO;
import com.xtc.marketing.invoiceservice.invoice.dataobject.InvoiceDO;
import com.xtc.marketing.invoiceservice.invoice.dataobject.InvoiceItemDO;
import com.xtc.marketing.invoiceservice.invoice.dataobject.SellerDO;
import com.xtc.marketing.invoiceservice.invoice.dataobject.jsonobject.ApplyHistoryDO;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceApplyDetailDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.command.InvoiceIssueCmd;
import com.xtc.marketing.invoiceservice.invoice.enums.ApplyStateEnum;
import com.xtc.marketing.invoiceservice.invoice.enums.CreateTypeEnum;
import com.xtc.marketing.invoiceservice.invoice.enums.InvoiceItemTypeEnum;
import com.xtc.marketing.invoiceservice.invoice.executor.query.InvoiceApplyDetailGetQryExe;
import com.xtc.marketing.invoiceservice.invoice.executor.query.InvoiceApplyGetQryExe;
import com.xtc.marketing.invoiceservice.invoice.executor.query.InvoiceGetQryExe;
import com.xtc.marketing.invoiceservice.invoice.executor.query.SellerGetQryExe;
import com.xtc.marketing.invoiceservice.rpc.BaiWangInvoiceRpc;
import com.xtc.marketing.invoiceservice.util.DateUtil;
import com.xtc.marketing.invoiceservice.util.MoneyUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 开红票命令执行器
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class InvoiceIssueRedCmdExe {

    // executor 注入
    private final InvoiceApplyGetQryExe invoiceApplyGetQryExe;
    private final SellerGetQryExe sellerGetQryExe;
    private final InvoiceGetQryExe invoiceGetQryExe;
    private final InvoiceApplyDetailGetQryExe invoiceApplyDetailGetQryExe;
    // 基础设施层注入
    private final BizOrderOperateLockCacheClient bizOrderOperateLockCacheClient;
    private final InvoiceApplyDao invoiceApplyDao;
    private final InvoiceDao invoiceDao;
    private final InvoiceItemDao invoiceItemDao;
    private final InvoiceConverter invoiceConverter;
    private final BaiWangInvoiceConverter baiWangInvoiceConverter;
    private final BaiWangInvoiceRpc baiWangInvoiceRpc;

    /**
     * 开红票
     *
     * @param cmd 参数
     * @return 发票申请详情
     */
    public InvoiceApplyDetailDTO execute(InvoiceIssueCmd cmd) {
        InvoiceApplyDO apply = invoiceApplyGetQryExe.byApplyIdWithoutMask(cmd.getApplyId());
        SellerDO seller = sellerGetQryExe.bySellerCode(apply.getSellerCode());
        InvoiceDO blueInvoice = invoiceGetQryExe.byInvoiceId(apply.getBlueInvoiceId());
        // 检查开票业务
        this.checkIssue(apply);
        // 构建开票业务数据，开票逻辑计算
        InvoiceIssueBO invoiceIssueBO = this.buildInvoiceIssueBO(apply, seller, blueInvoice);
        // 订单号加锁
        bizOrderOperateLockCacheClient.execute(apply.getBizOrderId(), () -> {
            boolean existsApplyAccepted = invoiceApplyDao.existsAcceptedByBizOrderId(apply.getBizOrderId());
            if (existsApplyAccepted) {
                throw BizException.of("业务订单正在开票中，请稍后重试");
            }
            // 调用平台开票接口
            this.rpcCallAndUpdateInvoice(invoiceIssueBO, blueInvoice);
            // 保存发票数据
            InvoiceIssueRedCmdExe exe = (InvoiceIssueRedCmdExe) AopContext.currentProxy();
            exe.saveInvoiceData(invoiceIssueBO);
        });
        // 返回最新的发票申请详情
        return invoiceApplyDetailGetQryExe.byApplyId(apply.getApplyId());
    }

    /**
     * 检查开票业务
     *
     * @param apply 发票申请
     */
    private void checkIssue(InvoiceApplyDO apply) {
        if (apply.getCreateType() != CreateTypeEnum.RED) {
            throw BizException.of("申请的开票类型不是红票");
        }
        if (apply.getApplyState().notAllowIssue()) {
            throw BizException.of("发票状态不允许开票 [%s]", apply.getApplyState().getDesc());
        }
    }

    /**
     * 生成发票项目列表
     *
     * @param apply       发票申请
     * @param seller      销售方
     * @param blueInvoice 蓝票
     * @return 发票项目列表
     */
    private InvoiceIssueBO buildInvoiceIssueBO(InvoiceApplyDO apply, SellerDO seller, InvoiceDO blueInvoice) {
        InvoiceIssueBO invoiceIssueBO = new InvoiceIssueBO();
        // 构建发票申请，填充申请id和申请记录
        InvoiceApplyDO updateApply = InvoiceApplyDO.builder()
                .applyId(apply.getApplyId()).applyHistory(apply.getApplyHistory()).build();
        invoiceIssueBO.setApply(updateApply);
        // 构建发票
        InvoiceDO invoice = invoiceConverter.toInvoiceDO(apply, seller);
        invoice.setBlueInvoiceNo(blueInvoice.getBlueInvoiceNo());
        invoice.setPriceAmount(blueInvoice.getPriceAmount());
        invoice.setTaxAmount(blueInvoice.getTaxAmount());
        invoiceIssueBO.setInvoice(invoice);
        // 查询蓝票项目列表，按 itemNo 顺序排序
        List<InvoiceItemDO> blueItems = invoiceItemDao.listByInvoiceId(blueInvoice.getInvoiceId());
        blueItems.sort(Comparator.comparing(InvoiceItemDO::getItemNo));
        // 构建红票项目列表，使用非折扣行的蓝票项目构建
        List<InvoiceItemDO> redItems = blueItems.stream()
                .filter(item -> item.getItemType() != InvoiceItemTypeEnum.DISCOUNT)
                .map(blueItem -> this.buildRedItem(blueItems, blueItem))
                .flatMap(Optional::stream)
                .toList();
        invoiceIssueBO.setInvoiceItems(redItems);
        return invoiceIssueBO;
    }

    /**
     * 构建红票项目
     *
     * @param blueItems 蓝票项目列表
     * @param blueItem  当前处理的蓝票项目
     */
    private Optional<InvoiceItemDO> buildRedItem(List<InvoiceItemDO> blueItems, InvoiceItemDO blueItem) {
        InvoiceItemDO redItem = InvoiceItemDO.builder()
                .itemNo(blueItem.getItemNo())
                .itemType(InvoiceItemTypeEnum.DISCOUNTED)
                .goodsName(blueItem.getGoodsName())
                .taxClassificationCode(blueItem.getTaxClassificationCode())
                .specification(blueItem.getSpecification())
                .unit(blueItem.getUnit())
                .num(Math.negateExact(blueItem.getNum()))
                .taxRate(blueItem.getTaxRate())
                .build();
        // 计算冲红金额
        int totalPrice = blueItem.getTotalPrice();
        int taxAmount = blueItem.getTaxAmount();
        // 蓝票项目为被折扣行，则需要汇总折扣行的金额
        if (blueItem.getItemType() == InvoiceItemTypeEnum.DISCOUNTED) {
            // 每个被折扣行的下一行为折扣行，集合下标从 0 开始无需 +1
            int discountIndex = blueItem.getItemNo();
            if (discountIndex < blueItems.size()) {
                InvoiceItemDO discountItem = blueItems.get(discountIndex);
                if (discountItem.getItemType() == InvoiceItemTypeEnum.DISCOUNT) {
                    // 被折扣行（正） + 折扣行（负）
                    totalPrice = blueItem.getTotalPrice() + discountItem.getTotalPrice();
                    taxAmount = blueItem.getTaxAmount() + discountItem.getTaxAmount();
                }
            }
        }
        if (totalPrice == 0) {
            return Optional.empty();
        }
        if (totalPrice < 0) {
            throw BizException.of("红票项目金额不允许大于 0 [行号 %s]", blueItem.getItemNo());
        }
        // 金额取反
        redItem.setTotalPrice(Math.negateExact(totalPrice));
        redItem.setTaxAmount(Math.negateExact(taxAmount));
        return Optional.of(redItem);
    }

    /**
     * 调用平台开红票接口
     *
     * @param invoiceIssueBO 开票业务数据
     * @param blueInvoice    蓝票
     */
    private void rpcCallAndUpdateInvoice(InvoiceIssueBO invoiceIssueBO, InvoiceDO blueInvoice) {
        // 初始化请求参数
        OutputRedinvoiceAddRequest request = baiWangInvoiceConverter.toOutputRedinvoiceAddRequest(invoiceIssueBO.getInvoice());
        request.setOriginInvoiceDate(DateUtil.toString(blueInvoice.getInvoiceTime()));
        // 生成发票项目列表
        List<OutputRedinvoiceAddObjectType> requestItems = baiWangInvoiceConverter.toOutputRedinvoiceAddObjectType(invoiceIssueBO.getInvoiceItems());
        request.setRedConfirmDetailReqEntityList(requestItems);
        // 调用平台开红票接口
        OutputRedinvoiceRedforminfoResponse rpcResponse = baiWangInvoiceRpc.issueRed(request);
        // 更新开票业务数据
        this.updateInvoiceIssueBO(invoiceIssueBO, rpcResponse);
    }

    /**
     * 更新开票业务数据
     *
     * @param invoiceIssueBO 开票业务数据
     * @param rpcResponse    平台开票结果
     */
    private void updateInvoiceIssueBO(InvoiceIssueBO invoiceIssueBO, OutputRedinvoiceRedforminfoResponse rpcResponse) {
        InvoiceApplyDO apply = invoiceIssueBO.getApply();
        // 转换开票数据
        OutputRedinvoiceRedforminfo invoiceResult = rpcResponse.getModel().getFirst();
        baiWangInvoiceConverter.updateInvoiceDO(invoiceIssueBO.getInvoice(), invoiceResult);
        // 转换开票项目数据
        Map<Integer, InvoiceItemDO> invoiceItemsMap = invoiceIssueBO.getInvoiceItems().stream()
                .collect(Collectors.toMap(InvoiceItemDO::getItemNo, Function.identity()));
        invoiceResult.getElectricInvoiceDetails().forEach(itemResult -> {
            // 通过蓝票的行号匹配
            InvoiceItemDO invoiceItem = invoiceItemsMap.get(itemResult.getOriginalInvoiceDetailNo());
            if (invoiceItem == null) {
                throw BizException.of("更新开票业务数据异常，发票项目行号不匹配 [%s]", itemResult.getOriginalInvoiceDetailNo());
            }
            // 更新发票项目单价
            if (itemResult.getGoodsPrice() != null) {
                invoiceItem.setUnitPrice(MoneyUtil.yuanToCent(itemResult.getGoodsPrice()));
            }
        });
        // 更新申请状态，生成开票成功申请记录
        ApplyStateEnum newApplyState = ApplyStateEnum.ACCEPTED;
        ApplyHistoryDO.add(apply.getApplyHistory(), newApplyState);
        apply.setApplyState(newApplyState);
    }

    /**
     * 保存发票数据
     *
     * @param invoiceIssueBO 发票业务数据
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void saveInvoiceData(InvoiceIssueBO invoiceIssueBO) {
        // 新增发票
        InvoiceDO invoice = invoiceIssueBO.getInvoice();
        invoiceDao.save(invoice);
        // 新增发票项目
        List<InvoiceItemDO> invoiceItems = invoiceIssueBO.getInvoiceItems();
        invoiceItems.forEach(item -> {
            item.setInvoiceId(invoice.getInvoiceId());
            item.setSerialNo(invoice.getSerialNo());
            item.setInvoiceNo(invoice.getRedInvoiceNo());
        });
        invoiceItemDao.saveBatch(invoiceItems);
        // 更新发票申请
        InvoiceApplyDO apply = invoiceIssueBO.getApply();
        apply.setInvoiceId(invoice.getInvoiceId());
        invoiceApplyDao.updateByApplyId(apply);
    }

}
