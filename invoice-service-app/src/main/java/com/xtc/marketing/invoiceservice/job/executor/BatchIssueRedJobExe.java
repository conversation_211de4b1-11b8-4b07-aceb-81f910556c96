package com.xtc.marketing.invoiceservice.job.executor;

import com.baiwang.ispsdk.entity.request.OutputRedinvoiceAddRequest;
import com.baiwang.ispsdk.entity.response.OutputEinvoiceQueryResponse;
import com.baiwang.ispsdk.entity.response.node.OutputEinvoiceQuery;
import com.xtc.marketing.invoiceservice.constant.BatchConstant;
import com.xtc.marketing.invoiceservice.constant.SystemConstant;
import com.xtc.marketing.invoiceservice.exception.BizException;
import com.xtc.marketing.invoiceservice.invoice.converter.BaiWangInvoiceConverter;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.command.NormalIssueRedCmd;
import com.xtc.marketing.invoiceservice.invoice.executor.command.NormalIssueRedCmdExe;
import com.xtc.marketing.invoiceservice.job.dto.BatchIssueRedExcelDTO;
import com.xtc.marketing.invoiceservice.oss.MarketingInvoiceOssClient;
import com.xtc.marketing.invoiceservice.rpc.BaiWangInvoiceRpc;
import com.xtc.marketing.invoiceservice.util.DateUtil;
import com.xtc.marketing.invoiceservice.util.EasyExcelUtil;
import com.xtc.marketing.invoiceservice.util.GsonUtil;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 非系统数据发票冲红任务执行器
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class BatchIssueRedJobExe {

    // 基础设施层注入
    private final MarketingInvoiceOssClient marketingInvoiceOssClient;
    private final PlatformTransactionManager transactionManager;
    private final BaiWangInvoiceRpc baiWangInvoiceRpc;
    private final BaiWangInvoiceConverter baiWangInvoiceConverter;
    private final NormalIssueRedCmdExe normalIssueRedCmdExe;

    /**
     * 支持的发票类型
     */
    private static final String ALL_INVOICE_TYPE = Arrays.stream(InvoiceTypeEnum.values())
            .map(InvoiceTypeEnum::getType).collect(Collectors.joining("、"));

    /**
     * 非系统数据发票冲红
     *
     * @param shardIndex 分片索引
     * @param shardTotal 分片总数
     */
    public void execute(int shardIndex, int shardTotal) {
        // 待处理文件列表
        String nowDate = DateUtil.nowDateCompact();
        String pendingFolder = BatchConstant.getIssueRedPendingFolder(nowDate);
        List<String> pendingObjects = marketingInvoiceOssClient.listFolderObjects(pendingFolder, shardTotal);
        if (CollectionUtils.isEmpty(pendingObjects) || pendingObjects.size() < shardIndex + 1) {
            log.info("当前没有待处理的文件 分片索引 {} 分片总数 {} 文件列表 {}", shardIndex, shardTotal, pendingObjects);
            return;
        }
        // 确认当前分片需要处理的文件，使用分片索引下标获取
        String processObjectName = pendingObjects.get(shardIndex);
        log.info("当前处理的文件 {}", processObjectName);
        // 存储处理结果的文件
        byte[] processedResult = this.processObject(processObjectName);
        // 已处理文件夹保存处理过后的文件
        this.saveProcessedObject(processObjectName, nowDate, processedResult);
        // 从待处理文件夹删除文件
        marketingInvoiceOssClient.removeObject(processObjectName);
        log.info("从待处理文件夹删除文件 {}", processObjectName);
    }

    /**
     * 处理文件
     *
     * @param processObjectName 当前处理的文件名
     * @return 处理结果
     */
    private byte[] processObject(String processObjectName) {
        byte[] processedResult;
        try (InputStream fileStream = marketingInvoiceOssClient.getObject(processObjectName)) {
            processedResult = EasyExcelUtil.readExcelWithTransactionThenReturnByte(
                    fileStream,
                    transactionManager,
                    BatchIssueRedExcelDTO.class,
                    new EasyExcelUtil.BizConsumer<>("batchProcess", this::batchProcess)
            );
        } catch (IOException e) {
            throw BizException.of("读取文件异常 %s", processObjectName);
        }
        return processedResult;
    }

    /**
     * 批量处理发票冲红
     *
     * @param issueRedDTO 待冲红数据
     */
    private void batchProcess(BatchIssueRedExcelDTO issueRedDTO) {
        InvoiceTypeEnum invoiceType = InvoiceTypeEnum.of(issueRedDTO.getInvoiceType())
                .orElseThrow(() -> BizException.of("发票类型不正确 [%s] 请填写“%s”", issueRedDTO.getInvoiceType(), ALL_INVOICE_TYPE));
        if (invoiceType == InvoiceTypeEnum.NORMAL) {
            // 税控发票冲红
            NormalIssueRedCmd cmd = NormalIssueRedCmd.builder()
                    .sellerTaxNo(issueRedDTO.getSellerTaxNo())
                    .invoiceCode(issueRedDTO.getInvoiceCode())
                    .invoiceNo(issueRedDTO.getInvoiceNo())
                    .build();
            InvoiceDTO invoiceDTO = normalIssueRedCmdExe.execute(cmd);
            log.info("税控发票冲红成功 {}", GsonUtil.objectToJson(invoiceDTO));
        } else {
            // 数电票冲红
            OutputEinvoiceQueryResponse queryResponse = baiWangInvoiceRpc.queryByInvoiceNo(issueRedDTO.getSellerTaxNo(), issueRedDTO.getInvoiceNo());
            if (CollectionUtils.isEmpty(queryResponse.getModel())) {
                throw BizException.of("未查到蓝票数据，请到平台确认");
            }
            OutputEinvoiceQuery invoice = queryResponse.getModel().getFirst();
            // 只保留正常行（0）删除其他类型的行
            invoice.getElectricInvoiceDetails().removeIf(item -> !"0".equals(item.getInvoiceLineNature()));
            OutputRedinvoiceAddRequest request = baiWangInvoiceConverter.toOutputRedinvoiceAddRequest(invoice);
            baiWangInvoiceRpc.issueRed(request);
        }
    }

    /**
     * 已处理文件夹保存处理过后的文件
     *
     * @param processObjectName 当前处理的文件名
     * @param nowDate           当前日期
     * @param processedResult   处理结果
     */
    private void saveProcessedObject(String processObjectName, String nowDate, byte[] processedResult) {
        // 生成已处理文件名：已处理文件夹/原文件名-MDC.get("trace.id").原后缀
        String processedObjectName = this.buildProcessedObjectName(processObjectName, nowDate);
        String contentType = processedObjectName.endsWith(".xlsx") ? EasyExcelUtil.CONTENT_TYPE_XLSX : EasyExcelUtil.CONTENT_TYPE_XLS;
        // 保存文件到已处理文件夹
        try (InputStream fileStream = new ByteArrayInputStream(processedResult)) {
            marketingInvoiceOssClient.putObject(processedObjectName, fileStream, contentType);
            log.info("已处理文件保存成功 {}", processedObjectName);
        } catch (IOException e) {
            String message = "保存已处理文件异常 %s".formatted(processedObjectName);
            throw BizException.of(message, e.getCause());
        }
    }

    /**
     * 生成已处理文件名
     * <p>已处理文件夹/原文件名-MDC.get("trace.id").原后缀</p>
     *
     * @param processObjectName 当前处理的文件名
     * @param nowDate           当前日期
     * @return 已处理文件名
     */
    private String buildProcessedObjectName(String processObjectName, String nowDate) {
        // 解析原文件名
        String processedFolder = BatchConstant.getIssueRedProcessedFolder(nowDate);
        String fullFileName = processObjectName.substring(processObjectName.lastIndexOf("/") + 1);
        // 获取当前线程的 traceId 不存在则不处理
        String traceId = MDC.get(SystemConstant.MDC_TRACE_ID);
        if (StringUtils.isBlank(traceId)) {
            return processedFolder + fullFileName;
        }
        // 生成格式：已处理文件夹/原文件名-MDC.get("trace.id").原后缀
        String[] fullFileNameSplit = fullFileName.split("\\.");
        String fileName = fullFileNameSplit[0];
        String fileSuffix = fullFileNameSplit[1];
        return "%s%s-%s.%s".formatted(processedFolder, fileName, traceId, fileSuffix);
    }

    /**
     * 发票类型枚举
     */
    @Getter
    private enum InvoiceTypeEnum {
        /**
         * 数电票
         */
        DIGITAL("数电"),
        /**
         * 普通发票
         */
        NORMAL("税控"),
        ;

        /**
         * 发票类型
         */
        private final String type;

        InvoiceTypeEnum(String type) {
            this.type = type;
        }

        /**
         * 获取枚举
         *
         * @param type 发票类型
         * @return 枚举
         */
        private static Optional<InvoiceTypeEnum> of(String type) {
            if (type == null) {
                return Optional.empty();
            }
            for (InvoiceTypeEnum value : InvoiceTypeEnum.values()) {
                if (value.type.equals(type)) {
                    return Optional.of(value);
                }
            }
            return Optional.empty();
        }
    }

}
