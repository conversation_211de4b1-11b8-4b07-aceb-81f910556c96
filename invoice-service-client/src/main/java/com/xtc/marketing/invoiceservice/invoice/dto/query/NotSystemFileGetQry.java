package com.xtc.marketing.invoiceservice.invoice.dto.query;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * 非系统文件获取参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotSystemFileGetQry {

    /**
     * 对象名称
     */
    @NotBlank
    @Length(max = 500)
    private String objectName;

}
