package com.xtc.marketing.invoiceservice.invoice.dto.query;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * 发票文件查询参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceFileGetQry {

    /**
     * 发票文件凭证
     */
    @NotBlank
    @Length(max = 500)
    private String fileToken;

}
