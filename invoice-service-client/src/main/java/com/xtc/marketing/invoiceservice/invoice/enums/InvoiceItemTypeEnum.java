package com.xtc.marketing.invoiceservice.invoice.enums;

import java.util.Optional;

/**
 * 发票项目类型枚举
 */
public enum InvoiceItemTypeEnum {
    /**
     * 正常行
     */
    NORMAL,
    /**
     * 被折扣行
     */
    DISCOUNTED,
    /**
     * 折扣行
     */
    DISCOUNT,
    ;

    /**
     * 获取枚举
     *
     * @param value 枚举值
     * @return 枚举
     */
    public static Optional<InvoiceItemTypeEnum> of(String value) {
        if (value == null) {
            return Optional.empty();
        }
        try {
            return Optional.of(InvoiceItemTypeEnum.valueOf(value.toUpperCase()));
        } catch (IllegalArgumentException e) {
            return Optional.empty();
        }
    }

    /**
     * 判断相等
     *
     * @param name 枚举值
     * @return 执行结果
     */
    public boolean equalsName(String name) {
        return this.name().equalsIgnoreCase(name);
    }
}
