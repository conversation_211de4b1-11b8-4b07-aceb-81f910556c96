package com.xtc.marketing.invoiceservice.invoice.dto.command;

import com.xtc.marketing.invoiceservice.invoice.enums.CreateTypeEnum;
import com.xtc.marketing.invoiceservice.invoice.enums.InvoiceBizTypeEnum;
import com.xtc.marketing.invoiceservice.invoice.enums.InvoiceTypeEnum;
import com.xtc.marketing.invoiceservice.invoice.enums.PlatformCodeEnum;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import java.util.List;

/**
 * 发票申请提交参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceApplySubmitCmd {

    /**
     * 业务代码
     */
    @NotBlank
    @Length(max = 50)
    private String bizCode;
    /**
     * 业务订单编号
     */
    @NotBlank
    @Length(max = 50)
    private String bizOrderId;
    /**
     * 业务订单标签
     */
    @Valid
    @Size(min = 1, max = 10)
    private List<TagCmd> bizOrderTag;
    /**
     * 平台代码
     */
    private PlatformCodeEnum platformCode;
    /**
     * 开票流水号
     */
    @NotBlank
    @Length(max = 50)
    private String serialNo;
    /**
     * 开票类型
     */
    @NotNull
    private CreateTypeEnum createType;
    /**
     * 发票类型
     */
    @NotNull
    private InvoiceTypeEnum invoiceType;
    /**
     * 发票业务类型
     */
    @NotNull
    private InvoiceBizTypeEnum invoiceBizType;
    /**
     * 发票抬头
     */
    @Length(max = 50)
    private String invoiceTitle;
    /**
     * 购买方税号
     */
    @Length(max = 50)
    private String buyerIdentifyNo;
    /**
     * 购买方电话
     */
    @Length(max = 50)
    private String buyerPhone;
    /**
     * 购买方地址
     */
    @Length(max = 200)
    private String buyerAddress;
    /**
     * 购买方银行
     */
    @Length(max = 50)
    private String buyerBankName;
    /**
     * 购买方银行账号
     */
    @Length(max = 50)
    private String buyerBankAccount;
    /**
     * 购买方代码，会查询购买方并替换申请单里的数据
     */
    @Length(max = 50)
    private String buyerCode;
    /**
     * 销售方代码
     */
    @NotBlank
    @Length(max = 50)
    private String sellerCode;
    /**
     * 蓝票的发票id，开红票必填
     */
    @Length(max = 50)
    private String blueInvoiceId;
    /**
     * 冲红原因
     */
    @Length(max = 200)
    private String redReason;
    /**
     * 发票备注
     */
    @Length(max = 200)
    private String invoiceRemark;
    /**
     * 发票项目，开蓝票必填
     * <p>供应商最多支持 2000 个项目<p/>
     * <p>数据清洗规则：<p/>
     * <ul>
     * <li>金额为 0 的非折扣行项目</li>
     * <li>金额从大到小排序</li>
     * </ul>
     */
    @Valid
    @Size(min = 1, max = 1500)
    private List<ApplyItemCmd> applyItems;

}
