package com.xtc.marketing.invoiceservice.invoice.enums;

import java.util.Optional;

/**
 * 平台代码枚举
 */
public enum PlatformCodeEnum {
    /**
     * 官方商城
     */
    XTC_SHOP,
    /**
     * 会员商城
     */
    XTC_MALL,
    /**
     * 内部购机
     */
    XTC_INTERNAL_SHOP,
    /**
     * 天猫
     */
    TMALL,
    /**
     * 京东
     */
    JD,
    /**
     * 抖音
     */
    TIKTOK,
    /**
     * 拼多多
     */
    PDD,
    /**
     * 快手
     */
    KUAISHOU,
    /**
     * 小红书
     */
    XIAOHONGSHU,
    /**
     * 微信视频号小店
     */
    WECHAT_CHANNELS_SHOP,
    /**
     * 其他（可以把平台填到发票备注里面）
     */
    OTHER,
    ;

    /**
     * 获取枚举
     *
     * @param value 枚举值
     * @return 枚举
     */
    public static Optional<PlatformCodeEnum> of(String value) {
        if (value == null) {
            return Optional.empty();
        }
        try {
            return Optional.of(PlatformCodeEnum.valueOf(value.toUpperCase()));
        } catch (IllegalArgumentException e) {
            return Optional.empty();
        }
    }

    /**
     * 判断相等
     *
     * @param name 枚举值
     * @return 执行结果
     */
    public boolean equalsName(String name) {
        return this.name().equalsIgnoreCase(name);
    }
}
