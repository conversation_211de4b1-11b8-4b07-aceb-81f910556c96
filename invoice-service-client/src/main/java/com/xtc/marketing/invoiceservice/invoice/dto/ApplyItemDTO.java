package com.xtc.marketing.invoiceservice.invoice.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 发票申请项目DTO
 */
@Getter
@Setter
@ToString
public class ApplyItemDTO {

    /**
     * 物料代码
     */
    private String erpCode;
    /**
     * 发票项目类型
     * @see com.xtc.marketing.invoiceservice.invoice.enums.InvoiceItemTypeEnum
     */
    private String itemType;
    /**
     * 数量
     */
    private Integer num;
    /**
     * 总价（单位：分）
     */
    private Integer totalPrice;

}
