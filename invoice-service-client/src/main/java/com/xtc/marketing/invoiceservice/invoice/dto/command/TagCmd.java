package com.xtc.marketing.invoiceservice.invoice.dto.command;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * 标签参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TagCmd {

    /**
     * 标签代码
     */
    @NotBlank
    @Length(max = 50)
    private String code;
    /**
     * 标签描述
     */
    @NotBlank
    @Length(max = 50)
    private String desc;

}
