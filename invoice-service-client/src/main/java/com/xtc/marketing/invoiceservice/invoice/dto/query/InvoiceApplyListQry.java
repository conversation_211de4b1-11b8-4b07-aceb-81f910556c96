package com.xtc.marketing.invoiceservice.invoice.dto.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * 发票申请列表查询参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceApplyListQry {

    /**
     * 申请id
     */
    @Length(max = 50)
    private String applyId;
    /**
     * 发票id
     */
    @Length(max = 50)
    private String invoiceId;
    /**
     * 业务订单编号
     */
    @Length(max = 50)
    private String bizOrderId;
    /**
     * 开票流水号
     */
    @Length(max = 50)
    private String serialNo;
    /**
     * 购买方税号
     */
    @Length(max = 50)
    private String buyerIdentifyNo;

}
