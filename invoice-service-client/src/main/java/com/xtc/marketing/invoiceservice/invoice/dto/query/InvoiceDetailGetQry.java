package com.xtc.marketing.invoiceservice.invoice.dto.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * 发票查询参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceDetailGetQry {

    /**
     * 发票id
     */
    @Length(max = 50)
    private String invoiceId;
    /**
     * 开票流水号
     */
    @Length(max = 50)
    private String serialNo;
    /**
     * 蓝票号码
     */
    @Length(max = 50)
    private String blueInvoiceNo;
    /**
     * 红票号码
     */
    @Length(max = 50)
    private String redInvoiceNo;

}
