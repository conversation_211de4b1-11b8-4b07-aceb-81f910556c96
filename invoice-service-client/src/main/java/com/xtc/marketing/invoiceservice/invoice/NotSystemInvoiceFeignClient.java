package com.xtc.marketing.invoiceservice.invoice;

import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.command.NormalIssueRedCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.query.NotSystemFileGetQry;
import com.xtc.marketing.invoiceservice.invoice.dto.query.NotSystemRedDetailQry;
import com.xtc.marketing.marketingcomponentdto.dto.SingleResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 非系统数据接口调用客户端
 */
@FeignClient(
        contextId = "notSystemInvoiceFeignClient",
        name = "not-system-invoice-feign-client",
        url = "${xtc.feign.client.invoice-service.url}",
        path = "/api"
)
public interface NotSystemInvoiceFeignClient {

    /**
     * 非系统文件获取
     *
     * @param qry 参数
     * @return 文件
     */
    @GetMapping("/not-system/invoice/file")
    ResponseEntity<Resource> invoiceFile(@SpringQueryMap NotSystemFileGetQry qry);

    /**
     * 非系统红票详情
     *
     * @param qry 参数
     * @return 发票详情
     */
    @GetMapping("/not-system/invoice/red-detail")
    SingleResponse<InvoiceDTO> notSystemRedDetail(@SpringQueryMap NotSystemRedDetailQry qry);

    /**
     * 税控发票冲红
     *
     * @param cmd 参数
     * @return 发票详情
     */
    @PostMapping("/not-system/invoice/normal-issue-red")
    SingleResponse<InvoiceDTO> normalIssueRed(@RequestBody NormalIssueRedCmd cmd);

}
