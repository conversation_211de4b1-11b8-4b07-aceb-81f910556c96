package com.xtc.marketing.invoiceservice.invoice;

import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceApplyDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceApplyDetailDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.command.InvoiceApplySubmitCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.command.InvoiceFileSyncCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.command.InvoiceIssueCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.command.InvoiceSyncCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.query.InvoiceApplyDetailGetQry;
import com.xtc.marketing.invoiceservice.invoice.dto.query.InvoiceApplyListQry;
import com.xtc.marketing.invoiceservice.invoice.dto.query.InvoiceDetailGetQry;
import com.xtc.marketing.invoiceservice.invoice.dto.query.InvoiceFileGetQry;
import com.xtc.marketing.marketingcomponentdto.dto.MultiResponse;
import com.xtc.marketing.marketingcomponentdto.dto.SingleResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 发票接口调用客户端
 */
@FeignClient(
        contextId = "invoiceFeignClient",
        name = "invoice-feign",
        url = "${xtc.feign.client.invoice-service.url}",
        path = "/api"
)
public interface InvoiceFeignClient {

    /**
     * 发票申请列表
     *
     * @param qry 参数
     * @return 发票申请列表
     */
    @GetMapping("/invoice-apply/list")
    MultiResponse<InvoiceApplyDTO> listApply(@SpringQueryMap InvoiceApplyListQry qry);

    /**
     * 发票申请详情
     *
     * @param qry 参数
     * @return 发票申请详情
     */
    @GetMapping("/invoice-apply/detail")
    SingleResponse<InvoiceApplyDetailDTO> applyDetail(@SpringQueryMap InvoiceApplyDetailGetQry qry);

    /**
     * 提交发票申请
     *
     * @param cmd 参数
     * @return 发票申请
     */
    @PostMapping("/invoice-apply/submit")
    SingleResponse<InvoiceApplyDTO> submitApply(@RequestBody InvoiceApplySubmitCmd cmd);

    /**
     * 开蓝票
     *
     * @param cmd 参数
     * @return 发票申请详情
     */
    @PostMapping("/invoice-apply/issue-blue")
    SingleResponse<InvoiceApplyDetailDTO> issueBlue(@RequestBody InvoiceIssueCmd cmd);

    /**
     * 开红票
     *
     * @param cmd 参数
     * @return 发票申请详情
     */
    @PostMapping("/invoice-apply/issue-red")
    SingleResponse<InvoiceApplyDetailDTO> issueRed(@RequestBody InvoiceIssueCmd cmd);

    /**
     * 发票同步
     *
     * @param cmd 参数
     * @return 发票详情
     * @apiNote 重复执行覆盖原有数据，发票文件已保存则会更新发票申请状态
     */
    @PostMapping("/invoice/sync")
    SingleResponse<InvoiceDTO> invoiceSync(@RequestBody InvoiceSyncCmd cmd);

    /**
     * 发票文件同步
     *
     * @param cmd 参数
     * @return 发票文件
     * @apiNote 重复执行覆盖原有数据
     */
    @PostMapping("/invoice/file-sync")
    SingleResponse<String> invoiceFileSync(@RequestBody InvoiceFileSyncCmd cmd);

    /**
     * 发票详情
     *
     * @param qry 参数
     * @return 发票详情
     * @apiNote 默认生成发票文件地址
     */
    @GetMapping("/invoice/detail")
    SingleResponse<InvoiceDTO> invoiceDetail(@SpringQueryMap InvoiceDetailGetQry qry);

    /**
     * 发票文件
     *
     * @param qry 参数
     * @return 发票文件
     */
    @GetMapping("/invoice/file")
    ResponseEntity<Resource> invoiceFile(@SpringQueryMap InvoiceFileGetQry qry);

}
