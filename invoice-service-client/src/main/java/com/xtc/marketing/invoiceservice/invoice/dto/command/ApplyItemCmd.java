package com.xtc.marketing.invoiceservice.invoice.dto.command;

import com.xtc.marketing.invoiceservice.invoice.enums.InvoiceItemTypeEnum;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

/**
 * 发票申请项目参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApplyItemCmd {

    /**
     * 发票项目类型
     */
    @NotNull
    private InvoiceItemTypeEnum itemType;
    /**
     * 总价（单位：分，最多提交 200w 金额）
     */
    @NotNull
    @Range(min = -200000000, max = 200000000)
    private Integer totalPrice;
    /**
     * 物料代码，正常行必填
     */
    @Length(max = 50)
    private String erpCode;
    /**
     * 数量，正常行必填（最多提交 150w 数量）
     */
    @Range(min = 1, max = 1500000)
    private Integer num;

}
