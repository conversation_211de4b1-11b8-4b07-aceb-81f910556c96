package com.xtc.marketing.invoiceservice.invoice.enums;

import lombok.Getter;

import java.util.Optional;

/**
 * 申请状态枚举
 */
@Getter
public enum ApplyStateEnum {
    /**
     * 待开票
     */
    PENDING("待开票"),
    /**
     * 已申请，等待结果
     */
    ACCEPTED("已申请"),
    /**
     * 已开票（蓝票）
     */
    ISSUED_BLUE("已开票"),
    /**
     * 已冲红（红票）
     */
    ISSUED_RED("已冲红"),
    /**
     * 开票失败
     */
    FAILED("开票失败"),
    /**
     * 取消申请
     */
    CANCEL("取消申请"),
    ;

    /**
     * 描述
     */
    private final String desc;

    ApplyStateEnum(String desc) {
        this.desc = desc;
    }

    /**
     * 获取枚举
     *
     * @param value 枚举值
     * @return 枚举
     */
    public static Optional<ApplyStateEnum> of(String value) {
        if (value == null) {
            return Optional.empty();
        }
        try {
            return Optional.of(ApplyStateEnum.valueOf(value.toUpperCase()));
        } catch (IllegalArgumentException e) {
            return Optional.empty();
        }
    }

    /**
     * 判断相等
     *
     * @param name 枚举值
     * @return 执行结果
     */
    public boolean equalsName(String name) {
        return this.name().equalsIgnoreCase(name);
    }

    /**
     * 判断开票成功
     *
     * @return 执行结果
     */
    public boolean success() {
        return this == ISSUED_BLUE || this == ISSUED_RED;
    }

    /**
     * 允许开票
     *
     * @return 执行结果
     */
    public boolean allowIssue() {
        return this == PENDING || this == FAILED;
    }

    /**
     * 不允许开票
     *
     * @return 执行结果
     */
    public boolean notAllowIssue() {
        return !allowIssue();
    }
}
