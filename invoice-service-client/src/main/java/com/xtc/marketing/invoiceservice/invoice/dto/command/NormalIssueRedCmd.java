package com.xtc.marketing.invoiceservice.invoice.dto.command;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * 税控发票冲红参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NormalIssueRedCmd {

    /**
     * 销售方税号
     */
    @NotBlank
    @Length(max = 50)
    private String sellerTaxNo;
    /**
     * 发票号码
     */
    @NotBlank
    @Length(max = 50)
    private String invoiceNo;
    /**
     * 发票代码
     */
    @NotBlank
    @Length(max = 50)
    private String invoiceCode;

}
