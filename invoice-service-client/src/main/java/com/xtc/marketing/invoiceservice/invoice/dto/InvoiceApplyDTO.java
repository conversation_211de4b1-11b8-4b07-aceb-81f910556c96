package com.xtc.marketing.invoiceservice.invoice.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 发票申请DTO
 */
@Getter
@Setter
@ToString
public class InvoiceApplyDTO {

    /**
     * 申请id
     */
    private String applyId;
    /**
     * 申请状态
     *
     * @see com.xtc.marketing.invoiceservice.invoice.enums.ApplyStateEnum
     */
    private String applyState;
    /**
     * 申请记录
     */
    private List<ApplyHistoryDTO> applyHistory;
    /**
     * 发票id
     */
    private String invoiceId;
    /**
     * 业务代码
     */
    private String bizCode;
    /**
     * 业务订单编号
     */
    private String bizOrderId;
    /**
     * 业务订单标签
     */
    private List<TagDTO> bizOrderTag;
    /**
     * 平台代码
     *
     * @see com.xtc.marketing.invoiceservice.invoice.enums.PlatformCodeEnum
     */
    private String platformCode;
    /**
     * 开票流水号
     */
    private String serialNo;
    /**
     * 开票类型
     *
     * @see com.xtc.marketing.invoiceservice.invoice.enums.CreateTypeEnum
     */
    private String createType;
    /**
     * 发票类型
     *
     * @see com.xtc.marketing.invoiceservice.invoice.enums.InvoiceTypeEnum
     */
    private String invoiceType;
    /**
     * 发票业务类型
     *
     * @see com.xtc.marketing.invoiceservice.invoice.enums.InvoiceBizTypeEnum
     */
    private String invoiceBizType;
    /**
     * 发票抬头
     */
    private String invoiceTitle;
    /**
     * 购买方税号
     */
    private String buyerIdentifyNo;
    /**
     * 购买方电话
     */
    private String buyerPhone;
    /**
     * 购买方地址
     */
    private String buyerAddress;
    /**
     * 购买方银行
     */
    private String buyerBankName;
    /**
     * 购买方银行账号
     */
    private String buyerBankAccount;
    /**
     * 购买方代码
     */
    private String buyerCode;
    /**
     * 销售方代码
     */
    private String sellerCode;
    /**
     * 蓝票的发票id
     */
    private String blueInvoiceId;
    /**
     * 冲红原因
     */
    private String redReason;
    /**
     * 发票备注
     */
    private String invoiceRemark;
    /**
     * 发票项目
     */
    private List<ApplyItemDTO> applyItems;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
