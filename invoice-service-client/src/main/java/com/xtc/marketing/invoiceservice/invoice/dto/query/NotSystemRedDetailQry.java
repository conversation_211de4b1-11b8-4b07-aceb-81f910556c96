package com.xtc.marketing.invoiceservice.invoice.dto.query;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDate;

/**
 * 非系统红票详情查询参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotSystemRedDetailQry {

    /**
     * 销售方税号
     */
    @NotBlank
    @Length(max = 50)
    private String sellerTaxNo;
    /**
     * 开票流水号
     */
    @NotBlank
    @Length(max = 50)
    private String serialNo;
    /**
     * 开票日期
     */
    @NotNull
    private LocalDate invoiceDate;

}
