package com.xtc.marketing.invoiceservice.invoice.dto.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * 发票申请查询参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceApplyDetailGetQry {

    /**
     * 申请id
     */
    @Length(max = 50)
    private String applyId;

}
