package com.xtc.marketing.invoiceservice.util;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.crypto.SecretKey;
import java.time.Duration;
import java.util.Date;
import java.util.Enumeration;
import java.util.Map;

/**
 * JWT 工具
 */
public class JwtUtil {

    /**
     * JWT存储在请求头headers里的名称
     */
    public static final String JWT_HEADER = "Authorization";
    /**
     * JWT存储在请求头headers里的值的前缀
     */
    public static final String JWT_PREFIX = "Bearer ";
    /**
     * JWT密钥，每个应用都要使用不同的密钥
     */
    public static final String JWT_SECRET_KEY = "N3FPaU9QMkJlRFk3U3UzZUpVNTN0MlBvRmxlYkktTEJoRDJSeEN2eU13YzJic1MwaWEwc3BHaUIyWDF5OUdmWg==";
    /**
     * JWT的签发主体，存入issuer
     */
    public static final String JWT_ISSUER = "xtc";
    /**
     * JWT的过期时间
     */
    public static final Duration JWT_EXPIRATION = Duration.ofDays(1);

    private JwtUtil() {
    }

    /**
     * 生成jwt字符串
     *
     * @param claims 公共声明（勿存储敏感信息）
     * @return jwt字符串
     */
    public static String generateJwt(Map<String, Object> claims) {
        return generateJwt(JWT_SECRET_KEY, JWT_EXPIRATION, null, claims);
    }

    /**
     * 生成jwt字符串
     *
     * @param subject 主题（存储id）
     * @return jwt字符串
     */
    public static String generateJwt(String subject) {
        return generateJwt(JWT_SECRET_KEY, JWT_EXPIRATION, subject, null);
    }

    /**
     * 生成jwt字符串
     *
     * @param secretKey  用于生成密钥的字符串
     * @param expiration 过期时间
     * @param subject    主题（存储id）
     * @param claims     公共声明（勿存储敏感信息）
     * @return jwt字符串
     */
    public static String generateJwt(String secretKey, Duration expiration, String subject, Map<String, Object> claims) {
        // 生成密钥
        SecretKey key = generateKey(secretKey);
        // 生成过期时间戳，单位：毫秒
        long exp = System.currentTimeMillis() + expiration.toMillis();
        Date expirationDate = new Date(exp);
        // 配置JWT生成器，先设置 setClaims 再设置其他，否则会被覆盖不生效
        return Jwts.builder()
                .claims(claims)
                .issuer(JWT_ISSUER)
                .subject(subject)
                .expiration(expirationDate)
                .signWith(key)
                .compact();
    }

    /**
     * 解析jwt，获取公共声明的信息
     *
     * @param claim 希望获取的公共声明
     * @return 公共声明的信息
     */
    public static String parseJwt(String claim) {
        String jwt = getJwt();
        return parseJwt(jwt, JWT_SECRET_KEY, claim);
    }

    /**
     * 解析jwt，获取公共声明的信息
     *
     * @param jwt   jwt字符串
     * @param claim 希望获取的公共声明
     * @return 公共声明的信息
     */
    public static String parseJwt(String jwt, String claim) {
        return parseJwt(jwt, JWT_SECRET_KEY, claim);
    }

    /**
     * 解析jwt，获取公共声明的信息
     *
     * @param jwt       jwt字符串
     * @param secretKey 用于生成密钥的字符串
     * @param claim     希望获取的公共声明
     * @return 公共声明的信息
     */
    public static String parseJwt(String jwt, String secretKey, String claim) {
        Claims claims = parseJwtClaims(jwt, secretKey);
        return claims.get(claim).toString();
    }

    /**
     * 解析jwt，获取主题（存储id）
     *
     * @param jwt       jwt字符串
     * @param secretKey 用于生成密钥的字符串
     * @return 主题（存储id）
     */
    public static String parseJwtSubject(String jwt, String secretKey) {
        return parseJwtClaims(jwt, secretKey).getSubject();
    }

    /**
     * 解析jwt，获取公共声明的信息
     *
     * @return 公共声明的信息
     */
    public static Claims parseJwtClaims() {
        String jwt = getJwt();
        return parseJwtClaims(jwt, JWT_SECRET_KEY);
    }

    /**
     * 解析jwt，获取公共声明的信息
     *
     * @param jwt jwt字符串
     * @return 公共声明的信息
     */
    public static Claims parseJwtClaims(String jwt) {
        return parseJwtClaims(jwt, JWT_SECRET_KEY);
    }

    /**
     * 解析jwt，获取公共声明的信息
     *
     * @param jwt       jwt字符串
     * @param secretKey 用于生成密钥的字符串
     * @return 公共声明的信息
     */
    public static Claims parseJwtClaims(String jwt, String secretKey) {
        // 生成密钥
        SecretKey key = generateKey(secretKey);
        return Jwts.parser()
                .requireIssuer(JWT_ISSUER)
                .verifyWith(key)
                .build()
                .parseSignedClaims(jwt)
                .getPayload();
    }

    /**
     * 获取请求头headers里的jwt字符串
     *
     * @return jwt字符串
     */
    public static String getJwt() {
        // 获取request
        ServletRequestAttributes servletRequestAttributes =
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (servletRequestAttributes == null) {
            return null;
        }
        HttpServletRequest request = servletRequestAttributes.getRequest();
        // 获取jwt
        Enumeration<String> requestHeader = request.getHeaderNames();
        while (requestHeader.hasMoreElements()) {
            String headerKey = requestHeader.nextElement();
            if (JWT_HEADER.equalsIgnoreCase(headerKey)) {
                String jwt = request.getHeader(headerKey);
                if (jwt != null) {
                    return jwt.replace(JWT_PREFIX, "");
                }
            }
        }
        return null;
    }

    /**
     * 生成密钥
     *
     * @return 密钥
     */
    public static SecretKey generateKey(String secretKey) {
        return Keys.hmacShaKeyFor(Decoders.BASE64.decode(secretKey));
    }

}
