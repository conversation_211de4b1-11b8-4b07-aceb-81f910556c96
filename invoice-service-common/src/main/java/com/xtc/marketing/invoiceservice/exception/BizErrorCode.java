package com.xtc.marketing.invoiceservice.exception;

import lombok.Getter;

/**
 * 错误码主要有3部分组成：类型+场景+自定义标识
 */
@Getter
public enum BizErrorCode {
    // 参数异常
//    P_USER_UserIdNotNull("P_USER_UserIdNotNull", "用户id不能为空"),

    // 业务异常 - 购买方
    B_BUYER_BuyerNotExists("B_BUYER_BuyerNotExists", "购买方不存在"),
    ;

    private final String errCode;
    private final String errDesc;

    BizErrorCode(String errCode, String errDesc) {
        this.errCode = errCode;
        this.errDesc = errDesc;
    }
}
