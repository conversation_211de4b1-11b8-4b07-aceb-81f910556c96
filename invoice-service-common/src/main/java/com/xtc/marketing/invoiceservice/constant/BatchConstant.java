package com.xtc.marketing.invoiceservice.constant;

/**
 * 批处理常量
 */
public class BatchConstant {

    private BatchConstant() {
    }

    /**
     * 批处理文件夹根目录
     */
    public static final String BATCH_FOLDER = "batch/";
    /**
     * 非系统数据发票冲红，文件夹：待处理
     */
    public static final String ISSUE_RED_PENDING_FOLDER = "batch/issue-red/%s/pending/";
    /**
     * 非系统数据发票冲红，文件夹：已处理
     */
    public static final String ISSUE_RED_PROCESSED_FOLDER = "batch/issue-red/%s/processed/";

    /**
     * 判断不是批处理文件
     *
     * @param objectName 文件对象名称
     * @return 执行结果
     */
    public static boolean notBatchFile(String objectName) {
        return !isBatchFile(objectName);
    }

    /**
     * 判断是批处理文件
     *
     * @param objectName 文件对象名称
     * @return 执行结果
     */
    public static boolean isBatchFile(String objectName) {
        return objectName.startsWith(BATCH_FOLDER);
    }

    /**
     * 非系统数据发票冲红，文件夹：待处理
     *
     * @param date 日期
     * @return 文件夹路径
     */
    public static String getIssueRedPendingFolder(String date) {
        return String.format(ISSUE_RED_PENDING_FOLDER, date);
    }

    /**
     * 非系统数据发票冲红，文件夹：已处理
     *
     * @param date 日期
     * @return 文件夹路径
     */
    public static String getIssueRedProcessedFolder(String date) {
        return String.format(ISSUE_RED_PROCESSED_FOLDER, date);
    }

}
