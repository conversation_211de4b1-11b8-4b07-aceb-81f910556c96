package com.xtc.marketing.invoiceservice.exception;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;

@Setter
@Getter
@ToString
public class ExceptionResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 成功标识
     */
    private boolean success;
    /**
     * 响应代码
     */
    private String errCode;
    /**
     * 响应消息
     */
    private String errMessage;

    public ExceptionResponse() {
    }

    public static ExceptionResponse buildSuccess() {
        ExceptionResponse exceptionResponse = new ExceptionResponse();
        exceptionResponse.setSuccess(true);
        return exceptionResponse;
    }

    public static ExceptionResponse buildFailure(String errCode, String errMessage) {
        ExceptionResponse exceptionResponse = new ExceptionResponse();
        exceptionResponse.setSuccess(false);
        exceptionResponse.setErrCode(errCode);
        exceptionResponse.setErrMessage(errMessage);
        return exceptionResponse;
    }

}
