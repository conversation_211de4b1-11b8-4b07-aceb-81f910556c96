package com.xtc.marketing.invoiceservice.invoice.converter;

import com.xtc.marketing.invoiceservice.invoice.dataobject.*;
import com.xtc.marketing.invoiceservice.invoice.dataobject.jsonobject.ApplyHistoryDO;
import com.xtc.marketing.invoiceservice.invoice.dataobject.jsonobject.ApplyItemDO;
import com.xtc.marketing.invoiceservice.invoice.dataobject.jsonobject.OperatorDO;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceApplyDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceApplyDetailDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceItemDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.command.InvoiceApplySubmitCmd;
import org.mapstruct.*;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * 发票数据转换器
 */
@Mapper(
        componentModel = "spring",
        // 忽略未设置映射的字段的告警
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        // mapstruct 和 lombok 一起使用时，需要关闭 @Builder 注解，否则 @AfterMapping 方法不会生效
        builder = @Builder(disableBuilder = true),
        // source 每个属性都进行 null 值校验
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        // target 映射遇到 null 值时忽略映射
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface InvoiceConverter {

    List<InvoiceApplyDTO> toInvoiceApplyDTO(List<InvoiceApplyDO> source);

    InvoiceApplyDTO toInvoiceApplyDTO(InvoiceApplyDO source);

    InvoiceApplyDetailDTO toInvoiceApplyDetailDTO(InvoiceApplyDO source);

    InvoiceDTO toInvoiceDTO(InvoiceDO source);

    List<InvoiceItemDTO> toInvoiceItemDTO(List<InvoiceItemDO> source);

    InvoiceItemDTO toInvoiceItemDTO(InvoiceItemDO source);

    @Mapping(target = "applyState", constant = "PENDING")
    @Mapping(target = "applyHistory", expression = "java(initHistory())")
    @Mapping(target = "createTime", expression = "java(now())")
    @Mapping(target = "updateTime", expression = "java(now())")
    InvoiceApplyDO toInvoiceApplyDO(InvoiceApplySubmitCmd source);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    InvoiceDO toInvoiceDO(InvoiceApplyDO invoiceApplyDO, SellerDO sellerDO);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "itemType", ignore = true)
    InvoiceItemDO toInvoiceItemDO(ApplyItemDO applyItemDO, GoodsDO goodsDO);

    /**
     * 初始化申请记录
     *
     * @return 发票申请记录
     */
    default List<ApplyHistoryDO> initHistory() {
        return Collections.singletonList(ApplyHistoryDO.init());
    }

    /**
     * 当前时间
     *
     * @return LocalDateTime
     */
    default LocalDateTime now() {
        return LocalDateTime.now();
    }

}
