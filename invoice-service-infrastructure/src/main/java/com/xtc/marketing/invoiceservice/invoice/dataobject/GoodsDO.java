package com.xtc.marketing.invoiceservice.invoice.dataobject;

import com.mybatisflex.annotation.Table;
import com.xtc.marketing.invoiceservice.config.BaseDO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;

/**
 * 商品表
 */
@Getter
@Setter
@ToString
@SuperBuilder
@NoArgsConstructor
@Table("t_goods")
public class GoodsDO extends BaseDO {

    /**
     * 物料代码
     */
    private String erpCode;
    /**
     * 物料名称
     */
    private String erpName;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 税收分类编码
     */
    private String taxClassificationCode;
    /**
     * 税务名称
     */
    private String taxItemName;
    /**
     * 税率（13%=0.13）
     */
    private BigDecimal taxRate;
    /**
     * 规格型号
     * <p>开票限制 40 个字符</p>
     */
    private String specification;
    /**
     * 单位
     */
    private String unit;
    /**
     * 备注
     */
    private String remark;

}
