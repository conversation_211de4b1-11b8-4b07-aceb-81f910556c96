package com.xtc.marketing.invoiceservice.invoice.converter;

import com.baiwang.ispsdk.entity.request.OutputInvoiceIssueRequest;
import com.baiwang.ispsdk.entity.request.OutputRedinvoiceAddRequest;
import com.baiwang.ispsdk.entity.request.node.OutputInvoiceIssueInvoiceDetail;
import com.baiwang.ispsdk.entity.request.node.OutputRedinvoiceAddObjectType;
import com.baiwang.ispsdk.entity.response.node.*;
import com.xtc.marketing.invoiceservice.invoice.dataobject.InvoiceDO;
import com.xtc.marketing.invoiceservice.invoice.dataobject.InvoiceItemDO;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceItemDTO;
import com.xtc.marketing.invoiceservice.invoice.enums.CreateTypeEnum;
import com.xtc.marketing.invoiceservice.invoice.enums.InvoiceItemTypeEnum;
import com.xtc.marketing.invoiceservice.invoice.enums.InvoiceTypeEnum;
import com.xtc.marketing.invoiceservice.util.DateUtil;
import com.xtc.marketing.invoiceservice.util.MoneyUtil;
import org.mapstruct.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 百望发票数据转换器
 */
@Mapper(
        componentModel = "spring",
        // 忽略未设置映射的字段的告警
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        // mapstruct 和 lombok 一起使用时，需要关闭 @Builder 注解，否则 @AfterMapping 方法不会生效
        builder = @Builder(disableBuilder = true),
        // source 每个属性都进行 null 值校验
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        // target 映射遇到 null 值时忽略映射
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface BaiWangInvoiceConverter {

    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "taxNo", source = "sellerIdentifyNo")
    // 标记所有金额都含税
    @Mapping(target = "data.priceTaxMark", constant = "1")
    @Mapping(target = "data.invoiceTypeCode", source = "invoiceType", qualifiedByName = "toInvoiceTypeCode")
    @Mapping(target = "data.invoiceType", source = "createType", qualifiedByName = "toInvoiceType")
    @Mapping(target = "data.serialNo", source = "serialNo")
    @Mapping(target = "data.remarks", source = "invoiceRemark")
    @Mapping(target = "data.buyerTaxNo", source = "buyerIdentifyNo")
    @Mapping(target = "data.buyerName", source = "invoiceTitle")
    @Mapping(target = "data.buyerTelphone", source = "buyerPhone")
    @Mapping(target = "data.buyerAddress", source = "buyerAddress")
    @Mapping(target = "data.buyerBankName", source = "buyerBankName")
    @Mapping(target = "data.buyerBankNumber", source = "buyerBankAccount")
    @Mapping(target = "data.redIssueReason", source = "redReason")
    @Mapping(target = "data.sellerTelphone", source = "sellerPhone")
    @Mapping(target = "data.sellerAddress", source = "sellerAddress")
    @Mapping(target = "data.sellerBankName", source = "sellerBankName")
    @Mapping(target = "data.sellerBankNumber", source = "sellerBankAccount")
    @Mapping(target = "data.drawer", source = "operator")
    OutputInvoiceIssueRequest toOutputInvoiceIssueRequest(InvoiceDO source);

    List<OutputInvoiceIssueInvoiceDetail> toOutputInvoiceIssueInvoiceDetail(List<InvoiceItemDO> source);

    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "goodsLineNo", source = "itemNo")
    @Mapping(target = "invoiceLineNature", source = "itemType", qualifiedByName = "toInvoiceLineNature")
    @Mapping(target = "goodsCode", source = "taxClassificationCode")
    @Mapping(target = "goodsName", source = "goodsName")
    @Mapping(target = "goodsSpecification", source = "specification")
    @Mapping(target = "goodsUnit", source = "unit")
    @Mapping(target = "goodsQuantity", source = "num")
    @Mapping(target = "goodsTotalPrice", source = "totalPrice", qualifiedByName = "centToYuan")
    @Mapping(target = "goodsTaxRate", source = "taxRate")
    OutputInvoiceIssueInvoiceDetail toOutputInvoiceIssueInvoiceDetail(InvoiceItemDO source);

    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "entryIdentity", constant = "01")
    @Mapping(target = "originInvoiceIsPaper", constant = "N")
    // 自动开票
    @Mapping(target = "autoIssueSwitch", constant = "Y")
    @Mapping(target = "redInvoiceLabel", constant = "01")
    // 标识数电发票
    @Mapping(target = "invoiceSource", constant = "2")
    // 标记所有金额都不含税
    @Mapping(target = "priceTaxMark", constant = "0")
    @Mapping(target = "redConfirmSerialNo", source = "serialNo")
    @Mapping(target = "buyerTaxNo", source = "buyerIdentifyNo")
    @Mapping(target = "buyerTaxName", source = "invoiceTitle")
    @Mapping(target = "originInvoiceType", source = "invoiceType", qualifiedByName = "toInvoiceTypeCode")
    @Mapping(target = "taxNo", source = "sellerIdentifyNo")
    @Mapping(target = "sellerTaxNo", source = "sellerIdentifyNo")
    @Mapping(target = "sellerTaxName", source = "sellerName")
    @Mapping(target = "drawer", source = "operator")
    @Mapping(target = "originalInvoiceNo", source = "blueInvoiceNo")
    @Mapping(target = "originInvoiceDate", source = "invoiceTime", qualifiedByName = "localDateTimeToString")
    @Mapping(target = "originInvoiceTotalPrice", source = "priceAmount", qualifiedByName = "centToYuan")
    @Mapping(target = "originInvoiceTotalTax", source = "taxAmount", qualifiedByName = "centToYuan")
    @Mapping(target = "invoiceTotalPrice", source = "priceAmount", qualifiedByName = "centToYuanNegate")
    @Mapping(target = "invoiceTotalTax", source = "taxAmount", qualifiedByName = "centToYuanNegate")
    OutputRedinvoiceAddRequest toOutputRedinvoiceAddRequest(InvoiceDO source);

    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "entryIdentity", constant = "01")
    @Mapping(target = "originInvoiceIsPaper", constant = "N")
    // 自动开票
    @Mapping(target = "autoIssueSwitch", constant = "Y")
    @Mapping(target = "redInvoiceLabel", constant = "01")
    // 标识数电发票
    @Mapping(target = "invoiceSource", constant = "2")
    // 标记所有金额都不含税
    @Mapping(target = "priceTaxMark", constant = "0")
    @Mapping(target = "redConfirmSerialNo", source = "invoiceNo", qualifiedByName = "buildRedSerialNo")
    @Mapping(target = "buyerTaxNo", source = "buyerTaxNo")
    @Mapping(target = "buyerTaxName", source = "buyerName")
    @Mapping(target = "originInvoiceType", expression = "java(\"004\".equals(source.getInvoiceTypeCode()) ? \"01\" : \"02\")")
    @Mapping(target = "taxNo", source = "sellerTaxNo")
    @Mapping(target = "sellerTaxNo", source = "sellerTaxNo")
    @Mapping(target = "sellerTaxName", source = "sellerName")
    @Mapping(target = "drawer", source = "drawer")
    @Mapping(target = "originalInvoiceNo", source = "invoiceNo")
    @Mapping(target = "originInvoiceDate", source = "invoiceDate")
    @Mapping(target = "originInvoiceTotalPrice", source = "invoiceTotalPrice")
    @Mapping(target = "originInvoiceTotalTax", source = "invoiceTotalTax")
    @Mapping(target = "invoiceTotalPrice", source = "invoiceTotalPrice", qualifiedByName = "toNegate")
    @Mapping(target = "invoiceTotalTax", source = "invoiceTotalTax", qualifiedByName = "toNegate")
    @Mapping(target = "redConfirmDetailReqEntityList", source = "electricInvoiceDetails")
    OutputRedinvoiceAddRequest toOutputRedinvoiceAddRequest(OutputEinvoiceQuery source);

    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "entryIdentity", constant = "01")
    @Mapping(target = "originInvoiceIsPaper", constant = "N")
    // 自动开票
    @Mapping(target = "autoIssueSwitch", constant = "Y")
    @Mapping(target = "redInvoiceLabel", constant = "01")
    // 标识税控发票
    @Mapping(target = "invoiceSource", constant = "1")
    // 标记所有金额都不含税
    @Mapping(target = "priceTaxMark", constant = "0")
    @Mapping(target = "redConfirmSerialNo", source = "serialNo")
    @Mapping(target = "buyerTaxNo", source = "buyerIdentifyNo")
    @Mapping(target = "buyerTaxName", source = "invoiceTitle", defaultValue = "个人")
    @Mapping(target = "originInvoiceType", source = "invoiceType", qualifiedByName = "toInvoiceTypeCode")
    @Mapping(target = "taxNo", source = "sellerIdentifyNo")
    @Mapping(target = "sellerTaxNo", source = "sellerIdentifyNo")
    @Mapping(target = "sellerTaxName", source = "sellerName")
    @Mapping(target = "drawer", source = "operator")
    @Mapping(target = "originalPaperInvoiceNo", source = "blueInvoiceNo")
    @Mapping(target = "originalPaperInvoiceCode", source = "blueInvoiceCode")
    @Mapping(target = "originInvoiceDate", source = "invoiceTime", qualifiedByName = "localDateTimeToString")
    @Mapping(target = "originInvoiceTotalPrice", source = "priceAmount", qualifiedByName = "centToYuan")
    @Mapping(target = "originInvoiceTotalTax", source = "taxAmount", qualifiedByName = "centToYuan")
    @Mapping(target = "invoiceTotalPrice", source = "priceAmount", qualifiedByName = "centToYuanNegate")
    @Mapping(target = "invoiceTotalTax", source = "taxAmount", qualifiedByName = "centToYuanNegate")
    @Mapping(target = "redConfirmDetailReqEntityList", source = "items")
    OutputRedinvoiceAddRequest toOutputRedinvoiceAddRequest(InvoiceDTO source);

    List<OutputRedinvoiceAddObjectType> toOutputRedinvoiceAddObjectType(List<InvoiceItemDO> source);

    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "originalInvoiceDetailNo", source = "itemNo")
    @Mapping(target = "goodsLineNo", source = "itemNo")
    @Mapping(target = "goodsCode", source = "taxClassificationCode")
    @Mapping(target = "projectName", source = "goodsName")
    @Mapping(target = "goodsSpecification", source = "specification")
    @Mapping(target = "goodsUnit", source = "unit")
    @Mapping(target = "goodsTaxRate", source = "taxRate")
    @Mapping(target = "goodsTotalPrice", source = "totalPrice", qualifiedByName = "centToYuan")
    @Mapping(target = "goodsTotalTax", source = "taxAmount", qualifiedByName = "centToYuan")
    OutputRedinvoiceAddObjectType toOutputRedinvoiceAddObjectType(InvoiceItemDO source);

    List<OutputRedinvoiceAddObjectType> toOutputRedinvoiceAddObjectTypeEinvoice(List<OutputEinvoiceQueryInvoiceQueryInvoiceDetail> source);

    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "originalInvoiceDetailNo", source = "goodsLineNo")
    @Mapping(target = "goodsLineNo", source = "goodsLineNo")
    @Mapping(target = "goodsCode", source = "goodsCode")
    @Mapping(target = "projectName", source = "goodsName")
    @Mapping(target = "goodsSpecification", source = "goodsSpecification")
    @Mapping(target = "goodsUnit", source = "goodsUnit")
    @Mapping(target = "goodsTaxRate", source = "goodsTaxRate")
    @Mapping(target = "goodsTotalPrice", source = "goodsTotalPrice", qualifiedByName = "toNegate")
    @Mapping(target = "goodsTotalTax", source = "goodsTotalTax", qualifiedByName = "toNegate")
    OutputRedinvoiceAddObjectType toOutputRedinvoiceAddObjectType(OutputEinvoiceQueryInvoiceQueryInvoiceDetail source);

    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "originalInvoiceDetailNo", source = "itemNo")
    @Mapping(target = "goodsLineNo", source = "itemNo")
    @Mapping(target = "goodsCode", source = "taxClassificationCode")
    @Mapping(target = "projectName", source = "goodsName")
    @Mapping(target = "goodsSpecification", source = "specification")
    @Mapping(target = "goodsUnit", source = "unit")
    @Mapping(target = "goodsTaxRate", source = "taxRate")
    @Mapping(target = "goodsTotalPrice", source = "totalPrice", qualifiedByName = "centToYuan")
    @Mapping(target = "goodsTotalTax", source = "taxAmount", qualifiedByName = "centToYuan")
    OutputRedinvoiceAddObjectType toOutputRedinvoiceAddObjectType(InvoiceItemDTO source);

    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "blueInvoiceNo", source = "invoiceNo")
    @Mapping(target = "invoiceTime", source = "invoiceDate", qualifiedByName = "toLocalDateTimeCompact")
    @Mapping(target = "fileUrl", expression = "java(source.getEInvoiceUrl())")
    @Mapping(target = "invoiceAmount", source = "invoiceTotalPriceTax", qualifiedByName = "yuanToCent")
    @Mapping(target = "taxAmount", source = "invoiceTotalTax", qualifiedByName = "yuanToCent")
    @Mapping(target = "priceAmount", source = "invoiceTotalPrice", qualifiedByName = "yuanToCent")
    void updateInvoiceDO(@MappingTarget InvoiceDO invoiceDO, OutputInvoiceIssueInvoiceResult source);

    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "redInvoiceNo", source = "redInvoiceNo")
    @Mapping(target = "invoiceTime", source = "redInvoiceDate", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "taxAmount", source = "invoiceTotalTax", qualifiedByName = "yuanToCent")
    @Mapping(target = "priceAmount", source = "invoiceTotalPrice", qualifiedByName = "yuanToCent")
    void updateInvoiceDO(@MappingTarget InvoiceDO invoiceDO, OutputRedinvoiceRedforminfo source);

    @BeanMapping(ignoreByDefault = true)
    // 平台可能会变更红票的流水号，以平台流水号为准
    @Mapping(target = "serialNo", source = "serialNo")
    @Mapping(target = "fileUrl", expression = "java(source.getEInvoiceUrl())")
    @Mapping(target = "invoiceRemark", source = "remarks")
    @Mapping(target = "operator", source = "drawer")
    @Mapping(target = "blueInvoiceNo", source = "invoiceNo", conditionExpression = "java(\"0\".equals(source.getInvoiceType()))")
    @Mapping(target = "redInvoiceNo", source = "invoiceNo", conditionExpression = "java(\"1\".equals(source.getInvoiceType()))")
    @Mapping(target = "invoiceTime", source = "invoiceDate", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "invoiceAmount", source = "invoiceTotalPriceTax", qualifiedByName = "yuanToCent")
    @Mapping(target = "taxAmount", source = "invoiceTotalTax", qualifiedByName = "yuanToCent")
    @Mapping(target = "priceAmount", source = "invoiceTotalPrice", qualifiedByName = "yuanToCent")
    InvoiceDO toInvoiceDO(OutputEinvoiceQuery source);

    List<InvoiceItemDO> toInvoiceItemDO(List<OutputEinvoiceQueryInvoiceQueryInvoiceDetail> source);

    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "itemNo", source = "goodsLineNo")
    @Mapping(target = "itemType", source = "invoiceLineNature", qualifiedByName = "toInvoiceItemType")
    @Mapping(target = "taxClassificationCode", source = "goodsCode")
    @Mapping(target = "goodsName", source = "goodsName")
    @Mapping(target = "specification", source = "goodsSpecification")
    @Mapping(target = "unit", source = "goodsUnit")
    @Mapping(target = "num", source = "goodsQuantity")
    @Mapping(target = "unitPrice", source = "goodsPrice", qualifiedByName = "yuanToCent")
    @Mapping(target = "totalPrice", source = "goodsTotalPrice", qualifiedByName = "yuanToCent")
    @Mapping(target = "taxAmount", source = "goodsTotalTax", qualifiedByName = "yuanToCent")
    @Mapping(target = "taxRate", source = "goodsTaxRate")
    InvoiceItemDO toInvoiceItemDO(OutputEinvoiceQueryInvoiceQueryInvoiceDetail source);

    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "fileUrl", expression = "java(source.getEInvoiceUrl())")
    @Mapping(target = "serialNo", source = "serialNo")
    @Mapping(target = "createType", source = "invoiceType", qualifiedByName = "toCreateType")
    @Mapping(target = "invoiceType", source = "invoiceTypeCode", qualifiedByName = "toInvoiceTypeByCode")
    @Mapping(target = "invoiceTitle", source = "buyerName")
    @Mapping(target = "buyerIdentifyNo", source = "buyerTaxNo")
    @Mapping(target = "buyerPhone", source = "buyerPhone")
    @Mapping(target = "buyerAddress", source = "buyerAddressPhone")
    @Mapping(target = "buyerBankName", source = "buyerBankAccount")
    @Mapping(target = "buyerBankAccount", source = "buyerBankAccount")
    @Mapping(target = "operator", source = "drawer")
    @Mapping(target = "sellerIdentifyNo", source = "sellerTaxNo")
    @Mapping(target = "sellerName", source = "sellerName")
    @Mapping(target = "sellerPhone", source = "sellerAddressPhone")
    @Mapping(target = "sellerAddress", source = "sellerAddressPhone")
    @Mapping(target = "sellerBankName", source = "sellerBankAccount")
    @Mapping(target = "sellerBankAccount", source = "sellerBankAccount")
    @Mapping(target = "invoiceRemark", source = "remarks")
    @Mapping(target = "blueInvoiceNo", expression = "java(\"0\".equals(source.getInvoiceType()) ? source.getInvoiceNo() : source.getOriginalInvoiceNo())")
    @Mapping(target = "blueInvoiceCode", expression = "java(\"0\".equals(source.getInvoiceType()) ? source.getInvoiceCode() : source.getOriginalInvoiceCode())")
    @Mapping(target = "redInvoiceNo", source = "invoiceNo", conditionExpression = "java(\"1\".equals(source.getInvoiceType()))")
    @Mapping(target = "invoiceTime", source = "invoiceDate", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "invoiceAmount", source = "invoiceTotalPriceTax", qualifiedByName = "yuanToCent")
    @Mapping(target = "taxAmount", source = "invoiceTotalTax", qualifiedByName = "yuanToCent")
    @Mapping(target = "priceAmount", source = "invoiceTotalPrice", qualifiedByName = "yuanToCent")
    @Mapping(target = "items", source = "invoiceDetailsList")
    InvoiceDTO toInvoiceDTO(OutputInvoiceQuery source);

    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "fileUrl", expression = "java(source.getEInvoiceUrl())")
    @Mapping(target = "serialNo", source = "serialNo")
    @Mapping(target = "createType", source = "invoiceType", qualifiedByName = "toCreateType")
    @Mapping(target = "invoiceType", source = "invoiceTypeCode", qualifiedByName = "toInvoiceTypeByCode")
    @Mapping(target = "invoiceTitle", source = "buyerName")
    @Mapping(target = "buyerIdentifyNo", source = "buyerTaxNo")
    @Mapping(target = "buyerPhone", source = "buyerTelphone")
    @Mapping(target = "buyerAddress", source = "buyerAddress")
    @Mapping(target = "buyerBankName", source = "buyerBankName")
    @Mapping(target = "buyerBankAccount", source = "buyerBankNumber")
    @Mapping(target = "operator", source = "drawer")
    @Mapping(target = "sellerIdentifyNo", source = "sellerTaxNo")
    @Mapping(target = "sellerName", source = "sellerName")
    @Mapping(target = "sellerPhone", source = "sellerAddress")
    @Mapping(target = "sellerAddress", source = "sellerTelphone")
    @Mapping(target = "sellerBankName", source = "sellerBankName")
    @Mapping(target = "sellerBankAccount", source = "sellerBankNumber")
    @Mapping(target = "invoiceRemark", source = "remarks")
    @Mapping(target = "blueInvoiceNo", expression = "java(\"0\".equals(source.getInvoiceType()) ? source.getInvoiceNo() : source.getOriginalInvoiceNo())")
    @Mapping(target = "redInvoiceNo", source = "invoiceNo", conditionExpression = "java(\"1\".equals(source.getInvoiceType()))")
    @Mapping(target = "invoiceTime", source = "invoiceDate", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "invoiceAmount", source = "invoiceTotalPriceTax", qualifiedByName = "yuanToCent")
    @Mapping(target = "taxAmount", source = "invoiceTotalTax", qualifiedByName = "yuanToCent")
    @Mapping(target = "priceAmount", source = "invoiceTotalPrice", qualifiedByName = "yuanToCent")
    @Mapping(target = "items", source = "electricInvoiceDetails")
    InvoiceDTO toInvoiceDTO(OutputEinvoiceQuery source);

    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "redInvoiceNo", source = "redInvoiceNo")
    @Mapping(target = "invoiceTime", source = "modifyDate", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "taxAmount", source = "invoiceTotalTax", qualifiedByName = "yuanToCent")
    @Mapping(target = "priceAmount", source = "invoiceTotalPrice", qualifiedByName = "yuanToCent")
    void updateInvoiceDTO(@MappingTarget InvoiceDTO invoiceDTO, OutputRedinvoiceRedforminfo source);

    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "itemNo", source = "goodsLineNo")
    @Mapping(target = "itemType", source = "invoiceLineNature", qualifiedByName = "toInvoiceItemType")
    @Mapping(target = "goodsName", source = "goodsName")
    @Mapping(target = "taxClassificationCode", source = "goodsCode")
    @Mapping(target = "specification", source = "goodsSpecification")
    @Mapping(target = "unit", source = "goodsUnit")
    @Mapping(target = "num", source = "goodsQuantity")
    @Mapping(target = "unitPrice", source = "goodsPrice", qualifiedByName = "yuanToCent")
    @Mapping(target = "totalPrice", source = "goodsTotalPrice", qualifiedByName = "yuanToCent")
    @Mapping(target = "taxRate", source = "goodsTaxRate")
    @Mapping(target = "taxAmount", source = "goodsTotalTax", qualifiedByName = "yuanToCent")
    InvoiceItemDTO toInvoiceItemDTO(OutputInvoiceQueryOutputInvoiceQueryInvoiceDetail source);

    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "itemNo", source = "goodsLineNo")
    @Mapping(target = "itemType", source = "invoiceLineNature", qualifiedByName = "toInvoiceItemType")
    @Mapping(target = "goodsName", source = "goodsName")
    @Mapping(target = "taxClassificationCode", source = "goodsCode")
    @Mapping(target = "specification", source = "goodsSpecification")
    @Mapping(target = "unit", source = "goodsUnit")
    @Mapping(target = "num", source = "goodsQuantity")
    @Mapping(target = "unitPrice", source = "goodsPrice", qualifiedByName = "yuanToCent")
    @Mapping(target = "totalPrice", source = "goodsTotalPrice", qualifiedByName = "yuanToCent")
    @Mapping(target = "taxRate", source = "goodsTaxRate")
    @Mapping(target = "taxAmount", source = "goodsTotalTax", qualifiedByName = "yuanToCent")
    InvoiceItemDTO toInvoiceItemDTO(OutputEinvoiceQueryInvoiceQueryInvoiceDetail source);

    @Named("toInvoiceTypeCode")
    default String toInvoiceTypeCode(InvoiceTypeEnum invoiceTypeEnum) {
        return switch (invoiceTypeEnum) {
            case NORMAL -> "02";
            case SPECIAL -> "01";
        };
    }

    @Named("toInvoiceTypeByCode")
    default InvoiceTypeEnum toInvoiceTypeByCode(String invoiceTypeCode) {
        return switch (invoiceTypeCode) {
            case "02", "026" -> InvoiceTypeEnum.NORMAL;
            case "01", "028" -> InvoiceTypeEnum.SPECIAL;
            default -> null;
        };
    }

    @Named("toInvoiceType")
    default String toInvoiceType(CreateTypeEnum createType) {
        return switch (createType) {
            case BLUE -> "0";
            case RED -> "1";
        };
    }

    @Named("toCreateType")
    default CreateTypeEnum toCreateType(String invoiceType) {
        return switch (invoiceType) {
            case "0" -> CreateTypeEnum.BLUE;
            case "1" -> CreateTypeEnum.RED;
            default -> null;
        };
    }

    @Named("toInvoiceItemType")
    default InvoiceItemTypeEnum toInvoiceItemType(String invoiceLineNature) {
        return switch (invoiceLineNature) {
            case "0" -> InvoiceItemTypeEnum.NORMAL;
            case "1" -> InvoiceItemTypeEnum.DISCOUNT;
            case "2" -> InvoiceItemTypeEnum.DISCOUNTED;
            default -> null;
        };
    }

    @Named("toInvoiceLineNature")
    default String toInvoiceLineNature(InvoiceItemTypeEnum invoiceItemType) {
        return switch (invoiceItemType) {
            case NORMAL -> "0";
            case DISCOUNT -> "1";
            case DISCOUNTED -> "2";
        };
    }

    @Named("buildRedSerialNo")
    default String buildRedSerialNo(String blueInvoiceNo) {
        // 生成 r1_xxxxxxxx 使用 blueInvoiceNo 截取后 17 位，避免下标越界
        return "r1_%s".formatted(blueInvoiceNo.substring(Math.max(0, blueInvoiceNo.length() - 17)));
    }

    @Named("toNegate")
    default BigDecimal toNegate(BigDecimal price) {
        return price.negate();
    }

    @Named("centToYuan")
    default BigDecimal centToYuan(Integer cent) {
        return MoneyUtil.centToYuan(cent);
    }

    @Named("centToYuanNegate")
    default BigDecimal centToYuanNegate(Integer cent) {
        return MoneyUtil.centToYuan(Math.negateExact(cent));
    }

    @Named("yuanToCent")
    default int yuanToCent(BigDecimal yuan) {
        return MoneyUtil.yuanToCent(yuan);
    }

    @Named("toLocalDateTime")
    default LocalDateTime toLocalDateTime(String time) {
        return DateUtil.toLocalDateTime(time);
    }

    @Named("toLocalDateTimeCompact")
    default LocalDateTime toLocalDateTimeCompact(String time) {
        return DateUtil.toLocalDateTimeCompact(time);
    }

    @Named("localDateTimeToString")
    default String localDateTimeToString(LocalDateTime time) {
        return DateUtil.toString(time);
    }

}
