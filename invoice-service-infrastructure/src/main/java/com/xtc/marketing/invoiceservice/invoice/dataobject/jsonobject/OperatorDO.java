package com.xtc.marketing.invoiceservice.invoice.dataobject.jsonobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 操作人
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OperatorDO {

    /**
     * 操作人id
     */
    private String id;
    /**
     * 操作人名称
     */
    private String name;

    /**
     * 创建系统操作人
     *
     * @return 系统操作人
     */
    public static OperatorDO ofSystem() {
        return of("system", "系统");
    }

    /**
     * 创建操作人
     *
     * @param id   操作人id
     * @param name 操作人名称
     * @return 操作人
     */
    public static OperatorDO of(String id, String name) {
        return OperatorDO.builder().id(id).name(name).build();
    }

}
