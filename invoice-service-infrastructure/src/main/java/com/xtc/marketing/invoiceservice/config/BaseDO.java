package com.xtc.marketing.invoiceservice.config;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.core.keygen.KeyGenerators;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

/**
 * 基础数据对象
 */
@Getter
@Setter
@ToString
@SuperBuilder
@NoArgsConstructor
public abstract class BaseDO {

    /**
     * 唯一标识，使用 雪花算法 生成ID
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.snowFlakeId)
    private Long id;
    /**
     * 删除标识
     */
    @Column(isLogicDelete = true, onInsertValue = "0")
    private Boolean deleted;
    /**
     * 更新时间
     */
    @Column(onInsertValue = "now()", onUpdateValue = "now()")
    private LocalDateTime updateTime;
    /**
     * 创建时间
     */
    @Column(onInsertValue = "now()")
    private LocalDateTime createTime;

}
