package com.xtc.marketing.invoiceservice.config;

import com.google.gson.*;
import com.google.gson.reflect.TypeToken;
import com.mybatisflex.core.handler.BaseJsonTypeHandler;

import java.text.DateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * MyBatis-Flex Gson 类型处理器
 */
public class MybatisFlexGsonTypeHandler extends BaseJsonTypeHandler<Object> {

    private static final String FORMAT_DATE = "yyyy-MM-dd";
    private static final String FORMAT_DATE_TIME = "yyyy-MM-dd HH:mm:ss";

    private static Gson gson;
    private final Class<?> propertyType;
    private Class<?> genericType;

    public MybatisFlexGsonTypeHandler(Class<?> propertyType) {
        this.propertyType = propertyType;
    }

    public MybatisFlexGsonTypeHandler(Class<?> propertyType, Class<?> genericType) {
        this.propertyType = propertyType;
        this.genericType = genericType;
    }

    @Override
    protected Object parseJson(String json) {
        if (genericType != null) {
            TypeToken<?> typeToken = TypeToken.getParameterized(propertyType, genericType);
            return getGson().fromJson(json, typeToken);
        } else {
            return getGson().fromJson(json, propertyType);
        }
    }

    @Override
    protected String toJson(Object object) {
        return getGson().toJson(object);
    }

    public static Gson getGson() {
        if (null == gson) {
            gson = new GsonBuilder()
                    // 序列化
                    .registerTypeAdapter(LocalDateTime.class, (JsonSerializer<LocalDateTime>) (localDateTime, type, serializationContext) ->
                            new JsonPrimitive(localDateTime.format(DateTimeFormatter.ofPattern(FORMAT_DATE_TIME))))
                    .registerTypeAdapter(LocalDate.class, (JsonSerializer<LocalDate>) (localDate, type, serializationContext) ->
                            new JsonPrimitive(localDate.format(DateTimeFormatter.ofPattern(FORMAT_DATE))))
                    .registerTypeAdapter(DateFormat.class, (JsonSerializer<DateFormat>) (dateFormat, type, serializationContext) -> null)
                    // 反序化
                    .registerTypeAdapter(LocalDateTime.class, (JsonDeserializer<LocalDateTime>) (jsonElement, type, deserializationContext) ->
                            LocalDateTime.parse(jsonElement.getAsJsonPrimitive().getAsString(), DateTimeFormatter.ofPattern(FORMAT_DATE_TIME)))
                    .registerTypeAdapter(LocalDate.class, (JsonDeserializer<LocalDate>) (jsonElement, type, deserializationContext) ->
                            LocalDate.parse(jsonElement.getAsJsonPrimitive().getAsString(), DateTimeFormatter.ofPattern(FORMAT_DATE)))
                    .registerTypeAdapter(DateFormat.class, (JsonDeserializer<DateFormat>) (jsonElement, type, deserializationContext) -> null)
                    // 序列化数值类型时，整数不带小数点
                    .registerTypeAdapter(Integer.class, numberSerializer())
                    .registerTypeAdapter(Long.class, numberSerializer())
                    .registerTypeAdapter(Double.class, numberSerializer())
                    .serializeNulls()
                    .disableHtmlEscaping()
                    .create();
        }
        return gson;
    }

    public static void setGson(Gson gson) {
        MybatisFlexGsonTypeHandler.gson = gson;
    }

    /**
     * 序列化数值类型时，整数不带小数点
     *
     * @param <T> 数值类型
     * @return JsonSerializer
     */
    private static <T extends Number> JsonSerializer<T> numberSerializer() {
        return (number, type, context) -> {
            if (number instanceof Integer) {
                return new JsonPrimitive(number.intValue());
            }
            if (number instanceof Long) {
                return new JsonPrimitive(number.longValue());
            }
            if (number instanceof Double) {
                // 整数部分和原值（包含小数）相等时，返回整数部分，不包含小数
                long longValue = number.longValue();
                double doubleValue = number.doubleValue();
                if (longValue == doubleValue) {
                    return new JsonPrimitive(longValue);
                }
            }
            return new JsonPrimitive(number);
        };
    }

}
