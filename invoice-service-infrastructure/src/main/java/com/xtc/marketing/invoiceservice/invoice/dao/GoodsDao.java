package com.xtc.marketing.invoiceservice.invoice.dao;

import com.xtc.marketing.invoiceservice.config.BaseDao;
import com.xtc.marketing.invoiceservice.constant.BizConstant;
import com.xtc.marketing.invoiceservice.invoice.dao.mapper.GoodsMapper;
import com.xtc.marketing.invoiceservice.invoice.dataobject.GoodsDO;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.xtc.marketing.invoiceservice.invoice.dataobject.table.GoodsDOTableDef.GOODS_DO;

/**
 * 商品数据库访问类
 * <p>原则上不写 sql 语句</p>
 * <p>从模板项目中获取 BaseDao 也可以使用 ServiceImpl</p>
 * <p>参数校验使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.If} 包含常用的：非空、非空集合、非空字符串等</p>
 * <p>SQL 函数使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.QueryMethods}</p>
 */
@Repository
public class GoodsDao extends BaseDao<GoodsMapper, GoodsDO> {

    /**
     * 查询商品集合
     *
     * @param erpCodes 物料代码列表
     * @return {@code Map<erpCode, 商品>} 商品集合
     */
    public Map<String, GoodsDO> mapByErpCode(Set<String> erpCodes) {
        if (erpCodes == null || erpCodes.isEmpty() || erpCodes.size() > BizConstant.LIMIT_INVOICE_ITEM) {
            return Map.of();
        }
        List<GoodsDO> goods = queryChain()
                .where(GOODS_DO.ERP_CODE.in(erpCodes))
                .orderBy(GOODS_DO.ERP_CODE.asc())
                .limit(BizConstant.LIMIT_INVOICE_ITEM)
                .list();
        return goods.stream().collect(Collectors.toMap(GoodsDO::getErpCode, Function.identity()));
    }

}
