package com.xtc.marketing.invoiceservice.invoice.dao;

import com.mybatisflex.core.query.If;
import com.xtc.marketing.invoiceservice.config.BaseDao;
import com.xtc.marketing.invoiceservice.invoice.dao.mapper.SellerMapper;
import com.xtc.marketing.invoiceservice.invoice.dataobject.SellerDO;
import org.springframework.stereotype.Repository;

import java.util.Optional;

import static com.xtc.marketing.invoiceservice.invoice.dataobject.table.SellerDOTableDef.SELLER_DO;

/**
 * 销售方数据库访问类
 * <p>原则上不写 sql 语句</p>
 * <p>从模板项目中获取 BaseDao 也可以使用 ServiceImpl</p>
 * <p>参数校验使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.If} 包含常用的：非空、非空集合、非空字符串等</p>
 * <p>SQL 函数使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.QueryMethods}</p>
 */
@Repository
public class SellerDao extends BaseDao<SellerMapper, SellerDO> {

    /**
     * 查询销售方
     *
     * @param sellerCode 销售方代码
     * @return 销售方
     */
    public Optional<SellerDO> getBySellerCode(String sellerCode) {
        if (If.noText(sellerCode)) {
            return Optional.empty();
        }
        return queryChain()
                .where(SELLER_DO.SELLER_CODE.eq(sellerCode))
                .orderBy(SELLER_DO.ID.desc())
                .limit(LIMIT_ONE)
                .oneOpt();
    }

    /**
     * 查询销售方
     *
     * @param sellerIdentifyNo 销售方税号
     * @return 销售方
     */
    public Optional<SellerDO> getBySellerIdentifyNo(String sellerIdentifyNo) {
        if (If.noText(sellerIdentifyNo)) {
            return Optional.empty();
        }
        return queryChain()
                .where(SELLER_DO.SELLER_IDENTIFY_NO.eq(sellerIdentifyNo))
                .orderBy(SELLER_DO.ID.desc())
                .limit(LIMIT_ONE)
                .oneOpt();
    }

}
