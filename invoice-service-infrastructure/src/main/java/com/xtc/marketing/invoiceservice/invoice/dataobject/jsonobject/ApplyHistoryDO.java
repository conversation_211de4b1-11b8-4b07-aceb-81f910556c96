package com.xtc.marketing.invoiceservice.invoice.dataobject.jsonobject;

import com.xtc.marketing.invoiceservice.invoice.enums.ApplyStateEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 发票申请记录
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApplyHistoryDO {

    /**
     * 处理时间
     */
    private LocalDateTime handleTime;
    /**
     * 处理人
     */
    private String handler;
    /**
     * 处理描述
     */
    private String handleDesc;
    /**
     * 处理备注
     */
    private String handleRemark;

    /**
     * 初始化申请记录
     *
     * @return 申请记录
     */
    public static ApplyHistoryDO init() {
        return of(null, "新建发票申请", null);
    }

    /**
     * 添加申请记录
     *
     * @param history    申请记录
     * @param applyState 申请状态
     */
    public static void add(List<ApplyHistoryDO> history, ApplyStateEnum applyState) {
        add(history, applyState, null);
    }

    /**
     * 添加申请记录
     *
     * @param history    申请记录
     * @param applyState 申请状态
     * @param remark     处理备注
     */
    public static void add(List<ApplyHistoryDO> history, ApplyStateEnum applyState, String remark) {
        ApplyHistoryDO newHistory = of(applyState, remark);
        history.addFirst(newHistory);
    }

    /**
     * 创建申请记录
     *
     * @param applyState 申请状态
     * @return 申请记录
     */
    public static ApplyHistoryDO of(ApplyStateEnum applyState) {
        return of(null, applyState.getDesc(), null);
    }

    /**
     * 创建申请记录
     *
     * @param applyState 申请状态
     * @param remark     处理备注
     * @return 申请记录
     */
    public static ApplyHistoryDO of(ApplyStateEnum applyState, String remark) {
        return of(null, applyState.getDesc(), remark);
    }

    /**
     * 创建申请记录
     *
     * @param handler    处理人
     * @param applyState 申请状态
     * @param remark     处理备注
     * @return 申请记录
     */
    public static ApplyHistoryDO of(String handler, ApplyStateEnum applyState, String remark) {
        return of(handler, applyState.getDesc(), remark);
    }

    /**
     * 创建申请记录
     *
     * @param handler    处理人
     * @param handleDesc 处理描述
     * @param remark     处理备注
     * @return 申请记录
     */
    public static ApplyHistoryDO of(String handler, String handleDesc, String remark) {
        return ApplyHistoryDO.builder()
                .handleTime(LocalDateTime.now())
                .handler(StringUtils.defaultIfBlank(handler, "系统"))
                .handleDesc(handleDesc)
                .handleRemark(remark)
                .build();
    }

}
