package com.xtc.marketing.invoiceservice.invoice.dataobject;

import com.mybatisflex.annotation.*;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.mybatisflex.core.mask.Masks;
import com.xtc.marketing.invoiceservice.config.BaseDO;
import com.xtc.marketing.invoiceservice.config.MybatisFlexGsonTypeHandler;
import com.xtc.marketing.invoiceservice.invoice.dataobject.jsonobject.TagDO;
import com.xtc.marketing.invoiceservice.invoice.enums.CreateTypeEnum;
import com.xtc.marketing.invoiceservice.invoice.enums.InvoiceBizTypeEnum;
import com.xtc.marketing.invoiceservice.invoice.enums.InvoiceTypeEnum;
import com.xtc.marketing.invoiceservice.invoice.enums.PlatformCodeEnum;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 发票表
 */
@Getter
@Setter
@ToString
@SuperBuilder
@NoArgsConstructor
@Table("t_invoice")
public class InvoiceDO extends BaseDO {

    /**
     * 发票id
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.ulid)
    private String invoiceId;
    /**
     * 发票文件链接
     */
    private String fileUrl;
    /**
     * 业务代码
     */
    private String bizCode;
    /**
     * 业务订单编号
     */
    private String bizOrderId;
    /**
     * 业务订单标签
     */
    @Column(typeHandler = MybatisFlexGsonTypeHandler.class)
    private List<TagDO> bizOrderTag;
    /**
     * 平台代码
     */
    private PlatformCodeEnum platformCode;
    /**
     * 开票流水号
     */
    private String serialNo;
    /**
     * 蓝票号码
     */
    private String blueInvoiceNo;
    /**
     * 红票号码
     */
    private String redInvoiceNo;
    /**
     * 开票类型
     */
    private CreateTypeEnum createType;
    /**
     * 发票类型
     */
    private InvoiceTypeEnum invoiceType;
    /**
     * 发票业务类型
     */
    private InvoiceBizTypeEnum invoiceBizType;
    /**
     * 开票时间
     */
    private LocalDateTime invoiceTime;
    /**
     * 开票金额
     */
    private Integer invoiceAmount;
    /**
     * 合计税额
     */
    private Integer taxAmount;
    /**
     * 合计不含税总金额
     */
    private Integer priceAmount;
    /**
     * 发票抬头
     */
    @ColumnMask(Masks.CHINESE_NAME)
    private String invoiceTitle;
    /**
     * 购买方税号
     */
    @ColumnMask(Masks.ID_CARD_NUMBER)
    private String buyerIdentifyNo;
    /**
     * 购买方电话
     */
    @ColumnMask(Masks.FIXED_PHONE)
    private String buyerPhone;
    /**
     * 购买方地址
     */
    @ColumnMask(Masks.ADDRESS)
    private String buyerAddress;
    /**
     * 购买方银行
     */
    private String buyerBankName;
    /**
     * 购买方银行账号
     */
    @ColumnMask(Masks.BANK_CARD_NUMBER)
    private String buyerBankAccount;
    /**
     * 开票人
     */
    private String operator;
    /**
     * 销售方税号
     */
    private String sellerIdentifyNo;
    /**
     * 销售方名称
     */
    private String sellerName;
    /**
     * 销售方电话
     */
    private String sellerPhone;
    /**
     * 销售方地址
     */
    private String sellerAddress;
    /**
     * 销售方银行
     */
    private String sellerBankName;
    /**
     * 销售方银行账号
     */
    @ColumnMask(Masks.BANK_CARD_NUMBER)
    private String sellerBankAccount;
    /**
     * 冲红原因
     */
    private String redReason;
    /**
     * 发票备注
     */
    private String invoiceRemark;

}
