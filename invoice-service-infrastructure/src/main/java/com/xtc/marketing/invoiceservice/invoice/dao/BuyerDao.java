package com.xtc.marketing.invoiceservice.invoice.dao;

import com.mybatisflex.core.mask.MaskManager;
import com.mybatisflex.core.query.If;
import com.xtc.marketing.invoiceservice.config.BaseDao;
import com.xtc.marketing.invoiceservice.invoice.dao.mapper.BuyerMapper;
import com.xtc.marketing.invoiceservice.invoice.dataobject.BuyerDO;
import org.springframework.stereotype.Repository;

import java.util.Optional;

import static com.xtc.marketing.invoiceservice.invoice.dataobject.table.BuyerDOTableDef.BUYER_DO;

/**
 * 购买方数据库访问类
 * <p>原则上不写 sql 语句</p>
 * <p>从模板项目中获取 BaseDao 也可以使用 ServiceImpl</p>
 * <p>参数校验使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.If} 包含常用的：非空、非空集合、非空字符串等</p>
 * <p>SQL 函数使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.QueryMethods}</p>
 */
@Repository
public class BuyerDao extends BaseDao<BuyerMapper, BuyerDO> {

    /**
     * 查询购买方
     *
     * @param buyerCode 购买方代码
     * @return 购买方
     */
    public Optional<BuyerDO> getByBuyerCode(String buyerCode) {
        if (If.noText(buyerCode)) {
            return Optional.empty();
        }
        return queryChain()
                .where(BUYER_DO.BUYER_CODE.eq(buyerCode))
                .orderBy(BUYER_DO.ID.desc())
                .limit(LIMIT_ONE)
                .oneOpt();
    }

    /**
     * 查询购买方，忽略脱敏
     *
     * @param buyerCode 购买方代码
     * @return 购买方
     */
    public Optional<BuyerDO> getByBuyerCodeWithoutMask(String buyerCode) {
        return MaskManager.execWithoutMask(() -> getByBuyerCode(buyerCode));
    }

}
