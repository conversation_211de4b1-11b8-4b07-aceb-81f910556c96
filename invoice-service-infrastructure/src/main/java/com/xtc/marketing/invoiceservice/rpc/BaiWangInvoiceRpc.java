package com.xtc.marketing.invoiceservice.rpc;

import com.baiwang.ispsdk.client.*;
import com.baiwang.ispsdk.entity.AbstractRequest;
import com.baiwang.ispsdk.entity.AbstractResponse;
import com.baiwang.ispsdk.entity.request.*;
import com.baiwang.ispsdk.entity.request.node.OutputEinvoiceQueryInvoiceQueryParam;
import com.baiwang.ispsdk.entity.request.node.OutputFormatCreateFormat;
import com.baiwang.ispsdk.entity.request.node.OutputFormatQueryQdInvoiceData;
import com.baiwang.ispsdk.entity.request.node.OutputInvoiceQueryOutputInvoiceQueryParam;
import com.baiwang.ispsdk.entity.response.*;
import com.baiwang.ispsdk.entity.response.node.OutputRedinvoiceAdd;
import com.xtc.marketing.invoiceservice.constant.SystemConstant;
import com.xtc.marketing.invoiceservice.exception.SysErrorCode;
import com.xtc.marketing.invoiceservice.exception.SysException;
import com.xtc.marketing.invoiceservice.util.DateUtil;
import com.xtc.marketing.invoiceservice.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.rmi.RemoteException;
import java.time.LocalDate;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BiFunction;
import java.util.function.Function;

/**
 * 百望发票RPC
 */
@Slf4j
@Component
public class BaiWangInvoiceRpc {

    /**
     * RPC 名称
     */
    private static final String RPC_NAME = "百望发票RPC";
    /**
     * 缓存 client 对象
     */
    private static final Map<String, IBWClient> CLIENT_CACHE = new ConcurrentHashMap<>();
    /**
     * 正式环境域名
     */
    private static final String DOMAIN = "http://172.28.4.168/post";
    /**
     * 测试环境域名
     */
    private static final String DOMAIN_TEST = "http://172.28.1.172/post";

    /**
     * 启动环境
     */
    @Value("${spring.profiles.active}")
    private String activeProfile;

    /**
     * 查询发票
     *
     * @param taxNo    销售方税号
     * @param serialNo 开票流水号
     * @return 响应
     */
    public OutputEinvoiceQueryResponse queryBySerialNo(String taxNo, String serialNo) {
        OutputEinvoiceQueryRequest request = new OutputEinvoiceQueryRequest();
        request.setTaxNo(taxNo);
        OutputEinvoiceQueryInvoiceQueryParam param = new OutputEinvoiceQueryInvoiceQueryParam();
        param.setSerialNo(serialNo);
        request.setData(param);
        return this.call(request, IBWClient::outputEinvoice, OutputEinvoiceGroup::query);
    }

    /**
     * 查询发票
     *
     * @param taxNo     销售方税号
     * @param invoiceNo 发票号码
     * @return 响应
     */
    public OutputEinvoiceQueryResponse queryByInvoiceNo(String taxNo, String invoiceNo) {
        OutputEinvoiceQueryRequest request = new OutputEinvoiceQueryRequest();
        request.setTaxNo(taxNo);
        OutputEinvoiceQueryInvoiceQueryParam param = new OutputEinvoiceQueryInvoiceQueryParam();
        param.setInvoiceNo(invoiceNo);
        request.setData(param);
        return this.call(request, IBWClient::outputEinvoice, OutputEinvoiceGroup::query);
    }

    /**
     * 查询发票
     *
     * @param taxNo       销售方税号
     * @param invoiceNo   发票号码
     * @param invoiceCode 发票代码
     * @return 响应
     */
    public OutputInvoiceQueryResponse queryByInvoiceNoAndInvoiceCode(String taxNo, String invoiceNo, String invoiceCode) {
        OutputInvoiceQueryRequest request = new OutputInvoiceQueryRequest();
        request.setTaxNo(taxNo);
        OutputInvoiceQueryOutputInvoiceQueryParam param = new OutputInvoiceQueryOutputInvoiceQueryParam();
        param.setInvoiceNo(invoiceNo);
        param.setInvoiceCode(invoiceCode);
        request.setData(param);
        return this.call(request, IBWClient::outputInvoice, OutputInvoiceGroup::query);
    }

    /**
     * 开蓝票
     *
     * @param request 请求参数
     * @return 响应
     */
    public OutputInvoiceIssueResponse issueBlue(OutputInvoiceIssueRequest request) {
        return this.call(request, IBWClient::outputInvoice, OutputInvoiceGroup::issue);
    }

    /**
     * 开红票
     *
     * @param request 请求参数
     * @return 响应
     */
    public OutputRedinvoiceRedforminfoResponse issueRed(OutputRedinvoiceAddRequest request) {
        // 提交红票确认单
        OutputRedinvoiceAddResponse response = this.call(request, IBWClient::outputRedinvoice, OutputRedinvoiceGroup::add);
        if (response.getModel() == null || response.getModel().isEmpty()) {
            throw rpcSysException(response);
        }
        OutputRedinvoiceAdd issuedRed = response.getModel().getFirst();
        String redConfirmUuid = issuedRed.getRedConfirmUuid();
        if (StringUtils.isBlank(issuedRed.getRedInvoiceNo())) {
            // 红票号码为空，调用确认单开具接口
            OutputRedinvoiceIssuedRequest issuedRequest = new OutputRedinvoiceIssuedRequest();
            issuedRequest.setTaxNo(request.getTaxNo());
            issuedRequest.setSerialNo(request.getRedConfirmSerialNo());
            issuedRequest.setRedConfirmUuid(redConfirmUuid);
            issuedRequest.setDrawer(request.getDrawer());
            OutputRedinvoiceIssuedResponse issuedResponse = this.call(issuedRequest,
                    IBWClient::outputRedinvoice, OutputRedinvoiceGroup::issued);
            if (issuedResponse.getModel() == null || StringUtils.isBlank(issuedResponse.getModel().getEinvoiceNo())) {
                throw rpcSysException(issuedResponse);
            }
        }
        // 查询开票结果
        OutputRedinvoiceRedforminfoRequest redRequest = new OutputRedinvoiceRedforminfoRequest();
        redRequest.setTaxNo(request.getTaxNo());
        redRequest.setSellerTaxNo(request.getTaxNo());
        redRequest.setRedConfirmUuid(redConfirmUuid);
        OutputRedinvoiceRedforminfoResponse redResponse =
                this.call(redRequest, IBWClient::outputRedinvoice, OutputRedinvoiceGroup::redforminfo);
        if (redResponse.getModel() == null || redResponse.getModel().isEmpty()) {
            throw rpcSysException(Map.of("response", response, "redResponse", redResponse));
        }
        return redResponse;
    }

    /**
     * 生成发票文件
     *
     * @param taxNo    销售方税号
     * @param serialNo 开票流水号
     * @return 响应
     */
    public OutputFormatCreateResponse createFile(String taxNo, String serialNo) {
        OutputFormatCreateRequest request = new OutputFormatCreateRequest();
        request.setTaxNo(taxNo);
        OutputFormatCreateFormat data = new OutputFormatCreateFormat();
        // 全电票模式，必传
        data.setInvoiceIssueMode("1");
        data.setSerialNo(serialNo);
        request.setData(data);
        return this.call(request, IBWClient::outputFormat, OutputFormatGroup::create);
    }

    /**
     * 查询红票
     *
     * @param taxNo    销售方税号
     * @param serialNo 开票流水号
     * @return 响应
     */
    public OutputEinvoiceQueryResponse queryRedBySerialNo(String taxNo, String serialNo, LocalDate invoiceDate) {
        // 查询开票结果
        OutputRedinvoiceFormlistRequest redRequest = new OutputRedinvoiceFormlistRequest();
        redRequest.setTaxNo(taxNo);
        redRequest.setSellerTaxNo(taxNo);
        redRequest.setRedConfirmSerialNo(serialNo);
        redRequest.setBuySelSelector("0");
        // 查询时间范围为开票日期的前后一天，避免跨天查询不到数据
        redRequest.setInvoiceStartDate(DateUtil.toString(invoiceDate.minusDays(1)));
        redRequest.setInvoiceEndDate(DateUtil.toString(invoiceDate.plusDays(1)));
        OutputRedinvoiceFormlistResponse redResponse = this.call(redRequest, IBWClient::outputRedinvoice, OutputRedinvoiceGroup::formlist);
        if (redResponse.getModel() == null || redResponse.getModel().isEmpty()) {
            throw rpcSysException(redResponse);
        }
        // 红票号码为空，说明还在开票中
        String redInvoiceNo = redResponse.getModel().getFirst().getRedInvoiceNo();
        if (StringUtils.isBlank(redInvoiceNo)) {
            return null;
        }
        return this.queryByInvoiceNo(taxNo, redInvoiceNo);
    }

    /**
     * 查询发票文件
     *
     * @param taxNo     销售方税号
     * @param invoiceNo 发票号
     * @return 响应
     */
    public OutputFormatQueryQdInvoiceResponse queryFile(String taxNo, String invoiceNo) {
        OutputFormatQueryQdInvoiceRequest request = new OutputFormatQueryQdInvoiceRequest();
        request.setTaxNo(taxNo);
        OutputFormatQueryQdInvoiceData data = new OutputFormatQueryQdInvoiceData();
        data.setInvoiceNo(invoiceNo);
        request.setData(data);
        return this.call(request, IBWClient::outputFormat, OutputFormatGroup::queryQdInvoice);
    }

    /**
     * 执行远程调用
     *
     * @param request         请求参数
     * @param invocationGroup 调用组
     * @param method          执行方法
     * @param <R>             请求类型
     * @param <G>             调用组类型
     * @param <T>             响应类型
     * @return 响应
     */
    private <R extends AbstractRequest, G extends InvocationGroup, T extends AbstractResponse> T call(
            R request,
            Function<IBWClient, G> invocationGroup,
            BiFunction<G, R, T> method
    ) {
        log.info("{}参数：{}", RPC_NAME, GsonUtil.objectToJson(request));
        String responseStr = "";
        try {
            IBWClient client = this.getClient();
            G group = invocationGroup.apply(client);
            T response = method.apply(group, request);
            responseStr = GsonUtil.objectToJson(response);
            log.info("{}响应：{}", RPC_NAME, responseStr);
            if (response == null || BooleanUtils.isFalse(response.isSuccess())) {
                throw new RemoteException("RPC响应失败");
            }
            return response;
        } catch (Exception e) {
            throw this.rpcSysException(responseStr, e);
        }
    }

    /**
     * 获取请求客户端
     *
     * @return 客户端
     */
    private IBWClient getClient() {
        if (CLIENT_CACHE.get(activeProfile) == null) {
            String domain = SystemConstant.isTestProfile(activeProfile) ? DOMAIN_TEST : DOMAIN;
            IBWClient client = new BWRestClient(domain);
            CLIENT_CACHE.put(activeProfile, client);
        }
        return CLIENT_CACHE.get(activeProfile);
    }

    /**
     * 远程调用异常
     *
     * @param response 响应结果
     * @return 异常
     */
    private SysException rpcSysException(Object response) {
        String responseStr = GsonUtil.objectToJson(response);
        return this.rpcSysException(responseStr, null);
    }

    /**
     * 远程调用异常
     *
     * @param responseStr 响应结果
     * @param e           异常
     * @return 异常
     */
    private SysException rpcSysException(String responseStr, Exception e) {
        String msg = "%s response: %s".formatted(RPC_NAME, responseStr);
        return SysException.of(SysErrorCode.S_RPC_ERROR, responseStr, msg, e);
    }

    public static void main(String[] args) {
        BaiWangInvoiceRpc rpc = new BaiWangInvoiceRpc();
        rpc.activeProfile = "dev";
//        rpc.queryByInvoiceNo("DB202402201001CS03", "25442000000150374519");
//        rpc.queryByInvoiceNoAndInvoiceCode("DB202402201001CS03", "19195824", "044002311211");
//        rpc.queryRedBySerialNo("91441900324794417T", "r1_10170323057664462", LocalDate.of(2025, 3, 31));
    }

}
