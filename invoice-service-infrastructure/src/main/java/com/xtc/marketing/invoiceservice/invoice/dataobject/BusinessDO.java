package com.xtc.marketing.invoiceservice.invoice.dataobject;

import com.mybatisflex.annotation.Table;
import com.xtc.marketing.invoiceservice.config.BaseDO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

/**
 * 业务接入表
 */
@Getter
@Setter
@ToString
@SuperBuilder
@NoArgsConstructor
@Table("t_business")
public class BusinessDO extends BaseDO {

    /**
     * 业务代码
     */
    private String bizCode;
    /**
     * 业务名称
     */
    private String bizName;
    /**
     * 启用标识
     */
    private Boolean enabled;

}
