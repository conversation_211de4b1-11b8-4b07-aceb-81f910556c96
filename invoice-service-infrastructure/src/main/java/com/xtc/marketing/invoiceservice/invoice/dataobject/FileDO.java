package com.xtc.marketing.invoiceservice.invoice.dataobject;

import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.xtc.marketing.invoiceservice.config.BaseDO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

/**
 * 文件表
 */
@Getter
@Setter
@ToString
@SuperBuilder
@NoArgsConstructor
@Table("t_file")
public class FileDO extends BaseDO {

    /**
     * 文件id
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.ulid)
    private String fileId;
    /**
     * 发票id
     */
    private String invoiceId;
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 存储对象
     */
    private String objectName;
    /**
     * 文件类型
     */
    private String fileType;
    /**
     * 文件内容类型
     * <p><a href="https://www.iana.org/assignments/media-types/media-types.xhtml">CONTENT-TYPE with the desired content type (also called a media type)</a></p>
     */
    private String contentType;

}
