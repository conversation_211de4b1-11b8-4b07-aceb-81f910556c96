package com.xtc.marketing.invoiceservice.invoice.dataobject;

import com.mybatisflex.annotation.*;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.mybatisflex.core.mask.Masks;
import com.xtc.marketing.invoiceservice.config.BaseDO;
import com.xtc.marketing.invoiceservice.config.MybatisFlexGsonTypeHandler;
import com.xtc.marketing.invoiceservice.invoice.dataobject.jsonobject.ApplyHistoryDO;
import com.xtc.marketing.invoiceservice.invoice.dataobject.jsonobject.ApplyItemDO;
import com.xtc.marketing.invoiceservice.invoice.dataobject.jsonobject.TagDO;
import com.xtc.marketing.invoiceservice.invoice.enums.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * 发票申请表
 */
@Getter
@Setter
@ToString
@SuperBuilder
@NoArgsConstructor
@Table("t_invoice_apply")
public class InvoiceApplyDO extends BaseDO {

    /**
     * 申请id
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.ulid)
    private String applyId;
    /**
     * 申请状态
     */
    private ApplyStateEnum applyState;
    /**
     * 申请记录
     */
    @Column(typeHandler = MybatisFlexGsonTypeHandler.class)
    private List<ApplyHistoryDO> applyHistory;
    /**
     * 发票id
     */
    private String invoiceId;
    /**
     * 业务代码
     */
    private String bizCode;
    /**
     * 业务订单编号
     */
    private String bizOrderId;
    /**
     * 业务订单标签
     */
    @Column(typeHandler = MybatisFlexGsonTypeHandler.class)
    private List<TagDO> bizOrderTag;
    /**
     * 平台代码
     */
    private PlatformCodeEnum platformCode;
    /**
     * 开票流水号
     */
    private String serialNo;
    /**
     * 开票类型
     */
    private CreateTypeEnum createType;
    /**
     * 发票类型
     */
    private InvoiceTypeEnum invoiceType;
    /**
     * 发票业务类型
     */
    private InvoiceBizTypeEnum invoiceBizType;
    /**
     * 发票抬头
     */
    @ColumnMask(Masks.CHINESE_NAME)
    private String invoiceTitle;
    /**
     * 购买方税号
     */
    @ColumnMask(Masks.ID_CARD_NUMBER)
    private String buyerIdentifyNo;
    /**
     * 购买方电话
     */
    @ColumnMask(Masks.FIXED_PHONE)
    private String buyerPhone;
    /**
     * 购买方地址
     */
    @ColumnMask(Masks.ADDRESS)
    private String buyerAddress;
    /**
     * 购买方银行
     */
    private String buyerBankName;
    /**
     * 购买方银行账号
     */
    @ColumnMask(Masks.BANK_CARD_NUMBER)
    private String buyerBankAccount;
    /**
     * 购买方代码
     */
    private String buyerCode;
    /**
     * 销售方代码
     */
    private String sellerCode;
    /**
     * 蓝票的发票id
     */
    private String blueInvoiceId;
    /**
     * 冲红原因
     */
    private String redReason;
    /**
     * 发票备注
     */
    private String invoiceRemark;
    /**
     * 发票申请项目
     */
    @Column(typeHandler = MybatisFlexGsonTypeHandler.class)
    private List<ApplyItemDO> applyItems;

}