package com.xtc.marketing.invoiceservice.invoice.dao;

import com.mybatisflex.core.query.If;
import com.xtc.marketing.invoiceservice.config.BaseDao;
import com.xtc.marketing.invoiceservice.invoice.dao.mapper.InvoiceMapper;
import com.xtc.marketing.invoiceservice.invoice.dataobject.InvoiceDO;
import com.xtc.marketing.invoiceservice.invoice.dto.query.InvoiceDetailGetQry;
import com.xtc.marketing.invoiceservice.invoice.enums.CreateTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.Optional;

import static com.xtc.marketing.invoiceservice.invoice.dataobject.table.InvoiceDOTableDef.INVOICE_DO;

/**
 * 发票数据库访问类
 * <p>原则上不写 sql 语句</p>
 * <p>从模板项目中获取 BaseDao 也可以使用 ServiceImpl</p>
 * <p>参数校验使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.If} 包含常用的：非空、非空集合、非空字符串等</p>
 * <p>SQL 函数使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.QueryMethods}</p>
 */
@Repository
public class InvoiceDao extends BaseDao<InvoiceMapper, InvoiceDO> {

    /**
     * 查询发票
     *
     * @param invoiceId 发票id
     * @return 发票
     */
    public Optional<InvoiceDO> getByInvoiceId(String invoiceId) {
        if (StringUtils.isBlank(invoiceId)) {
            return Optional.empty();
        }
        return queryChain()
                .where(INVOICE_DO.INVOICE_ID.eq(invoiceId))
                .orderBy(INVOICE_DO.ID.desc())
                .limit(LIMIT_ONE)
                .oneOpt();
    }

    /**
     * 查询发票
     *
     * @param qry 参数
     * @return 发票
     */
    public Optional<InvoiceDO> getBy(InvoiceDetailGetQry qry) {
        if (qry == null || noCondition(qry)) {
            return Optional.empty();
        }
        return queryChain()
                .where(INVOICE_DO.INVOICE_ID.eq(qry.getInvoiceId(), If::hasText))
                .and(INVOICE_DO.SERIAL_NO.eq(qry.getSerialNo(), If::hasText))
                .and(
                        INVOICE_DO.BLUE_INVOICE_NO.eq(qry.getBlueInvoiceNo())
                                .and(INVOICE_DO.CREATE_TYPE.eq(CreateTypeEnum.BLUE))
                                .when(StringUtils.isNotBlank(qry.getBlueInvoiceNo()))
                )
                .and(
                        INVOICE_DO.RED_INVOICE_NO.eq(qry.getRedInvoiceNo())
                                .and(INVOICE_DO.CREATE_TYPE.eq(CreateTypeEnum.RED))
                                .when(StringUtils.isNotBlank(qry.getRedInvoiceNo()))
                )
                .orderBy(INVOICE_DO.ID.desc())
                .limit(LIMIT_ONE)
                .oneOpt();
    }

    /**
     * 更新发票
     *
     * @param invoice 发票
     */
    public void updateByInvoiceId(InvoiceDO invoice) {
        if (invoice == null || StringUtils.isBlank(invoice.getInvoiceId())) {
            return;
        }
        update(invoice, INVOICE_DO.INVOICE_ID.eq(invoice.getInvoiceId()));
    }

}
