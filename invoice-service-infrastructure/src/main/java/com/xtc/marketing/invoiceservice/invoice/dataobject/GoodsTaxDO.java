package com.xtc.marketing.invoiceservice.invoice.dataobject;

import com.mybatisflex.annotation.Table;
import com.xtc.marketing.invoiceservice.config.BaseDO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

/**
 * 商品税务表
 */
@Getter
@Setter
@ToString
@SuperBuilder
@NoArgsConstructor
@Table("t_goods_tax")
public class GoodsTaxDO extends BaseDO {

    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 税收分类编码
     */
    private String taxClassificationCode;
    /**
     * 税务名称
     */
    private String taxItemName;

}
