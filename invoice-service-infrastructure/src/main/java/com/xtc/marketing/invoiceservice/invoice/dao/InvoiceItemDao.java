package com.xtc.marketing.invoiceservice.invoice.dao;

import com.xtc.marketing.invoiceservice.config.BaseDao;
import com.xtc.marketing.invoiceservice.constant.BizConstant;
import com.xtc.marketing.invoiceservice.invoice.dao.mapper.InvoiceItemMapper;
import com.xtc.marketing.invoiceservice.invoice.dataobject.InvoiceItemDO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

import static com.xtc.marketing.invoiceservice.invoice.dataobject.table.InvoiceItemDOTableDef.INVOICE_ITEM_DO;

/**
 * 发票项目数据库访问类
 * <p>原则上不写 sql 语句</p>
 * <p>从模板项目中获取 BaseDao 也可以使用 ServiceImpl</p>
 * <p>参数校验使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.If} 包含常用的：非空、非空集合、非空字符串等</p>
 * <p>SQL 函数使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.QueryMethods}</p>
 */
@Repository
public class InvoiceItemDao extends BaseDao<InvoiceItemMapper, InvoiceItemDO> {

    /**
     * 查询发票项目列表
     *
     * @param invoiceId 发票id
     * @return 发票项目列表
     */
    public List<InvoiceItemDO> listByInvoiceId(String invoiceId) {
        if (StringUtils.isBlank(invoiceId)) {
            return List.of();
        }
        return queryChain()
                .where(INVOICE_ITEM_DO.INVOICE_ID.eq(invoiceId))
                .orderBy(INVOICE_ITEM_DO.ITEM_NO.asc())
                .orderBy(INVOICE_ITEM_DO.ID.asc())
                .limit(BizConstant.LIMIT_INVOICE_ITEM)
                .list();
    }

    /**
     * 批量更新发票项目
     *
     * @param invoiceItems 发票项目列表
     */
    public void updateBatchByInvoiceIdAndItemNo(List<InvoiceItemDO> invoiceItems) {
        updateBatchByQuery(
                invoiceItems,
                item -> StringUtils.isBlank(item.getInvoiceId()) || item.getItemNo() == null,
                item -> queryChain()
                        .where(INVOICE_ITEM_DO.INVOICE_ID.eq(item.getInvoiceId()))
                        .and(INVOICE_ITEM_DO.ITEM_NO.eq(item.getItemNo()))
        );
    }

}
