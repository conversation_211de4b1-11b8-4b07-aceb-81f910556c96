package com.xtc.marketing.invoiceservice.invoice.dao;

import com.xtc.marketing.invoiceservice.config.BaseDao;
import com.xtc.marketing.invoiceservice.invoice.dao.mapper.BusinessMapper;
import com.xtc.marketing.invoiceservice.invoice.dataobject.BusinessDO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.Optional;

import static com.xtc.marketing.invoiceservice.invoice.dataobject.table.BusinessDOTableDef.BUSINESS_DO;

/**
 * 业务接入数据库访问类
 * <p>原则上不写 sql 语句</p>
 * <p>从模板项目中获取 BaseDao 也可以使用 ServiceImpl</p>
 * <p>参数校验使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.If} 包含常用的：非空、非空集合、非空字符串等</p>
 * <p>SQL 函数使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.QueryMethods}</p>
 */
@Repository
public class BusinessDao extends BaseDao<BusinessMapper, BusinessDO> {

    /**
     * 查询业务接入
     *
     * @param bizCode 业务代码
     * @return 业务接入
     */
    public Optional<BusinessDO> getByBizCode(String bizCode) {
        if (StringUtils.isBlank(bizCode)) {
            return Optional.empty();
        }
        return queryChain()
                .where(BUSINESS_DO.BIZ_CODE.eq(bizCode))
                .orderBy(BUSINESS_DO.ID.desc())
                .limit(LIMIT_ONE)
                .oneOpt();
    }

}