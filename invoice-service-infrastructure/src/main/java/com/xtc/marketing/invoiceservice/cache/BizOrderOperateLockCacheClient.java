package com.xtc.marketing.invoiceservice.cache;

import com.xtc.marketing.invoiceservice.exception.BizException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.function.Supplier;

/**
 * 业务订单操作锁客户端
 */
@RequiredArgsConstructor
@Component
public class BizOrderOperateLockCacheClient {

    /**
     * 业务名称
     */
    private static final String BIZ_NAME = "biz-order-operate";

    private final RedissonUtil redissonUtil;

    /**
     * 业务订单操作
     *
     * @param bizOrderId 业务订单编号
     * @param onSuccess  加锁成功时执行
     */
    public void execute(String bizOrderId, Runnable onSuccess) {
        redissonUtil.tryLock(BIZ_NAME, bizOrderId, onSuccess, this.lockException());
    }

    /**
     * 业务订单操作
     *
     * @param bizOrderId 业务订单编号
     * @param onSuccess  加锁成功时执行
     * @param <T>        业务返回值类型
     * @return T 业务返回值
     */
    public <T> T execute(String bizOrderId, Supplier<T> onSuccess) {
        return redissonUtil.tryLock(BIZ_NAME, bizOrderId, onSuccess, this.lockException());
    }

    /**
     * 锁异常
     *
     * @return 锁异常
     */
    private BizException lockException() {
        return BizException.of("业务订单正在处理中，请稍后重试");
    }

}
