package com.xtc.marketing.invoiceservice.invoice.dao;

import com.mybatisflex.core.mask.MaskManager;
import com.mybatisflex.core.query.If;
import com.xtc.marketing.invoiceservice.config.BaseDao;
import com.xtc.marketing.invoiceservice.invoice.dao.mapper.InvoiceApplyMapper;
import com.xtc.marketing.invoiceservice.invoice.dataobject.InvoiceApplyDO;
import com.xtc.marketing.invoiceservice.invoice.dataobject.jsonobject.ApplyHistoryDO;
import com.xtc.marketing.invoiceservice.invoice.dto.query.InvoiceApplyListQry;
import com.xtc.marketing.invoiceservice.invoice.enums.ApplyStateEnum;
import com.xtc.marketing.invoiceservice.util.GsonUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static com.xtc.marketing.invoiceservice.invoice.dataobject.table.InvoiceApplyDOTableDef.INVOICE_APPLY_DO;

/**
 * 发票申请数据库访问类
 * <p>原则上不写 sql 语句</p>
 * <p>从模板项目中获取 BaseDao 也可以使用 ServiceImpl</p>
 * <p>参数校验使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.If} 包含常用的：非空、非空集合、非空字符串等</p>
 * <p>SQL 函数使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.QueryMethods}</p>
 */
@Repository
public class InvoiceApplyDao extends BaseDao<InvoiceApplyMapper, InvoiceApplyDO> {

    /**
     * 查询发票申请
     *
     * @param applyId 申请id
     * @return 发票申请
     */
    public Optional<InvoiceApplyDO> getByApplyId(String applyId) {
        if (StringUtils.isBlank(applyId)) {
            return Optional.empty();
        }
        return queryChain()
                .where(INVOICE_APPLY_DO.APPLY_ID.eq(applyId))
                .orderBy(INVOICE_APPLY_DO.ID.desc())
                .limit(LIMIT_ONE)
                .oneOpt();
    }

    /**
     * 查询发票申请，忽略脱敏
     *
     * @param applyId 申请id
     * @return 发票申请
     */
    public Optional<InvoiceApplyDO> getByApplyIdWithoutMask(String applyId) {
        return MaskManager.execWithoutMask(() -> getByApplyId(applyId));
    }

    /**
     * 查询发票申请
     *
     * @param invoiceId 发票id
     * @param serialNo  开票流水号
     * @return 发票申请
     */
    public Optional<InvoiceApplyDO> getByInvoiceIdAndSerialNo(String invoiceId, String serialNo) {
        if (StringUtils.isAnyBlank(invoiceId, serialNo)) {
            return Optional.empty();
        }
        return queryChain()
                .where(INVOICE_APPLY_DO.INVOICE_ID.eq(invoiceId))
                .and(INVOICE_APPLY_DO.SERIAL_NO.eq(serialNo))
                .orderBy(INVOICE_APPLY_DO.ID.desc())
                .limit(LIMIT_ONE)
                .oneOpt();
    }

    /**
     * 查询发票申请列表
     *
     * @param qry 参数
     * @return 发票申请列表
     */
    public List<InvoiceApplyDO> listBy(InvoiceApplyListQry qry) {
        if (qry == null || noCondition(qry)) {
            return List.of();
        }
        return queryChain()
                .where(INVOICE_APPLY_DO.APPLY_ID.eq(qry.getApplyId(), If::hasText))
                .and(INVOICE_APPLY_DO.INVOICE_ID.eq(qry.getInvoiceId(), If::hasText))
                .and(INVOICE_APPLY_DO.BIZ_ORDER_ID.eq(qry.getBizOrderId(), If::hasText))
                .and(INVOICE_APPLY_DO.SERIAL_NO.eq(qry.getSerialNo(), If::hasText))
                .and(INVOICE_APPLY_DO.BUYER_IDENTIFY_NO.eq(qry.getBuyerIdentifyNo(), If::hasText))
                .orderBy(INVOICE_APPLY_DO.ID.desc())
                .limit(LIMIT_LIST)
                .list();
    }

    /**
     * 判断存在开票中的申请
     *
     * @param bizOrderId 业务订单编号
     * @return 执行结果
     */
    public boolean existsAcceptedByBizOrderId(String bizOrderId) {
        if (StringUtils.isBlank(bizOrderId)) {
            return false;
        }
        return queryChain()
                .where(INVOICE_APPLY_DO.BIZ_ORDER_ID.eq(bizOrderId))
                .and(INVOICE_APPLY_DO.APPLY_STATE.eq(ApplyStateEnum.ACCEPTED))
                .exists();
    }

    /**
     * 判断开票流水号已存在
     *
     * @param bizOrderId 业务订单编号
     * @param serialNo   开票流水号
     * @return 执行结果
     */
    public boolean alreadyIssueByBizOrderIdAndSerialNo(String bizOrderId, String serialNo) {
        if (StringUtils.isAnyBlank(bizOrderId, serialNo)) {
            return false;
        }
        return queryChain()
                .where(INVOICE_APPLY_DO.BIZ_ORDER_ID.eq(bizOrderId))
                .and(INVOICE_APPLY_DO.SERIAL_NO.eq(serialNo))
                .and(INVOICE_APPLY_DO.APPLY_STATE.in(ApplyStateEnum.ACCEPTED, ApplyStateEnum.ISSUED_BLUE, ApplyStateEnum.ISSUED_RED))
                .exists();
    }

    /**
     * 取消订单号所有的待开票申请
     *
     * @param bizOrderId    业务订单编号
     * @param cancelHistory 取消记录
     */
    public void cancelPendingByBizOrderId(String bizOrderId, ApplyHistoryDO cancelHistory) {
        if (StringUtils.isBlank(bizOrderId)) {
            return;
        }
        updateChain()
                .set(INVOICE_APPLY_DO.APPLY_STATE, ApplyStateEnum.CANCEL)
                // JSON_ARRAY_INSERT 用于向 JSON 数组插入 第一位 元素
                .setRaw(INVOICE_APPLY_DO.APPLY_HISTORY,
                        "JSON_ARRAY_INSERT(`apply_history`, '$[0]', CAST('" + GsonUtil.objectToJson(cancelHistory) + "' AS JSON))")
                .where(INVOICE_APPLY_DO.BIZ_ORDER_ID.eq(bizOrderId))
                .and(INVOICE_APPLY_DO.APPLY_STATE.eq(ApplyStateEnum.PENDING))
                .update();
    }

    /**
     * 更新发票申请
     *
     * @param invoiceApplyDO 发票申请
     */
    public void updateByApplyId(InvoiceApplyDO invoiceApplyDO) {
        if (invoiceApplyDO == null || StringUtils.isBlank(invoiceApplyDO.getApplyId())) {
            return;
        }
        update(invoiceApplyDO, INVOICE_APPLY_DO.APPLY_ID.eq(invoiceApplyDO.getApplyId()));
    }

    /**
     * 查询待开票状态的发票申请，按更新时间升序排列
     *
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param shardIndex 分片索引
     * @param limit      数据条数
     * @return 发票申请列表
     */
    public List<InvoiceApplyDO> listPendingByUpdateTimeAsc(LocalDateTime startTime, LocalDateTime endTime,
                                                           int shardIndex, int limit) {
        if (ObjectUtils.anyNull(startTime, endTime)) {
            return List.of();
        }
        return queryChain()
                .where(INVOICE_APPLY_DO.APPLY_STATE.eq(ApplyStateEnum.PENDING))
                .and(INVOICE_APPLY_DO.UPDATE_TIME.between(startTime, endTime))
                .orderBy(INVOICE_APPLY_DO.UPDATE_TIME.asc())
                .limit(shardIndex * limit, limit)
                .list();
    }

    /**
     * 查询已申请状态的发票申请，按更新时间升序排列
     *
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param shardIndex 分片索引
     * @param limit      数据条数
     * @return 发票申请列表
     */
    public List<InvoiceApplyDO> listAcceptedByUpdateTimeAsc(LocalDateTime startTime, LocalDateTime endTime,
                                                            int shardIndex, int limit) {
        if (ObjectUtils.anyNull(startTime, endTime)) {
            return List.of();
        }
        return queryChain()
                .where(INVOICE_APPLY_DO.APPLY_STATE.eq(ApplyStateEnum.ACCEPTED))
                .and(INVOICE_APPLY_DO.UPDATE_TIME.between(startTime, endTime))
                .orderBy(INVOICE_APPLY_DO.UPDATE_TIME.asc())
                .limit(shardIndex * limit, limit)
                .list();
    }

}
