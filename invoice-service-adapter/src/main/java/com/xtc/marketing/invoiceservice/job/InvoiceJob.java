package com.xtc.marketing.invoiceservice.job;

import com.xtc.marketing.invoiceservice.config.BaseJob;
import com.xtc.marketing.invoiceservice.config.BaseJobBO;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 发票任务
 */
@RequiredArgsConstructor
@Component
public class InvoiceJob extends BaseJob {

    private final InvoiceJobService invoiceJobService;

    /**
     * 开票任务
     */
    @XxlJob("invoiceIssueJob")
    public void invoiceIssueJob() {
        executeShardWithParam(BaseJobBO.class, invoiceJobService::invoiceIssueJob);
    }

    /**
     * 发票数据同步任务
     */
    @XxlJob("invoiceSyncJob")
    public void invoiceSyncJob() {
        executeShardWithParam(BaseJobBO.class, invoiceJobService::invoiceSyncJob);
    }

    /**
     * 发票文件同步任务
     */
    @XxlJob("invoiceFileSyncJob")
    public void invoiceFileSyncJob() {
        executeShardWithParam(BaseJobBO.class, invoiceJobService::invoiceFileSyncJob);
    }

}
