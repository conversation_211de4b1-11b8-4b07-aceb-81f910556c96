package com.xtc.marketing.invoiceservice.api;

import com.xtc.marketing.invoiceservice.invoice.InvoiceAppService;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceApplyDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceApplyDetailDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.command.InvoiceApplySubmitCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.command.InvoiceFileSyncCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.command.InvoiceIssueCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.command.InvoiceSyncCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.query.InvoiceApplyDetailGetQry;
import com.xtc.marketing.invoiceservice.invoice.dto.query.InvoiceApplyListQry;
import com.xtc.marketing.invoiceservice.invoice.dto.query.InvoiceDetailGetQry;
import com.xtc.marketing.invoiceservice.invoice.dto.query.InvoiceFileGetQry;
import com.xtc.marketing.marketingcomponentdto.dto.MultiResponse;
import com.xtc.marketing.marketingcomponentdto.dto.SingleResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 发票接口
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api")
public class InvoiceApiController {

    private final InvoiceAppService invoiceAppService;

    /**
     * 发票申请列表
     *
     * @param qry 参数
     * @return 发票申请列表
     */
    @GetMapping("/invoice-apply/list")
    public MultiResponse<InvoiceApplyDTO> listApply(@Valid InvoiceApplyListQry qry) {
        List<InvoiceApplyDTO> list = invoiceAppService.listApply(qry);
        return MultiResponse.of(list);
    }

    /**
     * 发票申请详情
     *
     * @param qry 参数
     * @return 发票申请详情
     */
    @GetMapping("/invoice-apply/detail")
    public SingleResponse<InvoiceApplyDetailDTO> applyDetail(@Valid InvoiceApplyDetailGetQry qry) {
        InvoiceApplyDetailDTO dto = invoiceAppService.getApplyDetail(qry);
        return SingleResponse.of(dto);
    }

    /**
     * 提交发票申请
     *
     * @param cmd 参数
     * @return 发票申请
     */
    @PostMapping("/invoice-apply/submit")
    public SingleResponse<InvoiceApplyDTO> submitApply(@Valid @RequestBody InvoiceApplySubmitCmd cmd) {
        InvoiceApplyDTO dto = invoiceAppService.submitApply(cmd);
        return SingleResponse.of(dto);
    }

    /**
     * 开蓝票
     *
     * @param cmd 参数
     * @return 发票申请详情
     */
    @PostMapping("/invoice-apply/issue-blue")
    public SingleResponse<InvoiceApplyDetailDTO> issueBlue(@Valid @RequestBody InvoiceIssueCmd cmd) {
        InvoiceApplyDetailDTO dto = invoiceAppService.issueBlue(cmd);
        return SingleResponse.of(dto);
    }

    /**
     * 开红票
     *
     * @param cmd 参数
     * @return 发票申请详情
     */
    @PostMapping("/invoice-apply/issue-red")
    public SingleResponse<InvoiceApplyDetailDTO> issueRed(@Valid @RequestBody InvoiceIssueCmd cmd) {
        InvoiceApplyDetailDTO dto = invoiceAppService.issueRed(cmd);
        return SingleResponse.of(dto);
    }

    /**
     * 发票同步
     *
     * @param cmd 参数
     * @return 发票详情
     * @apiNote 重复执行覆盖原有数据，发票文件已保存则会更新发票申请状态
     */
    @PostMapping("/invoice/sync")
    public SingleResponse<InvoiceDTO> invoiceSync(@Valid @RequestBody InvoiceSyncCmd cmd) {
        InvoiceDTO dto = invoiceAppService.invoiceSync(cmd);
        return SingleResponse.of(dto);
    }

    /**
     * 发票文件同步
     *
     * @param cmd 参数
     * @return 发票文件
     * @apiNote 重复执行覆盖原有数据
     */
    @PostMapping("/invoice/file-sync")
    public SingleResponse<String> invoiceFileSync(@Valid @RequestBody InvoiceFileSyncCmd cmd) {
        String file = invoiceAppService.invoiceFileSync(cmd);
        return SingleResponse.of(file);
    }

    /**
     * 发票详情
     *
     * @param qry 参数
     * @return 发票详情
     * @apiNote 默认生成发票文件地址
     */
    @GetMapping("/invoice/detail")
    public SingleResponse<InvoiceDTO> invoiceDetail(@Valid InvoiceDetailGetQry qry) {
        InvoiceDTO dto = invoiceAppService.getInvoiceDetail(qry);
        return SingleResponse.of(dto);
    }

    /**
     * 发票文件
     *
     * @param qry 参数
     * @return 发票文件
     */
    @GetMapping("/invoice/file")
    public ResponseEntity<Resource> invoiceFile(@Valid InvoiceFileGetQry qry) {
        return invoiceAppService.invoiceFile(qry);
    }

}
