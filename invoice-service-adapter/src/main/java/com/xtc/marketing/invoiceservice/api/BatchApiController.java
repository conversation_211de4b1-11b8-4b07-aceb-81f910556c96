package com.xtc.marketing.invoiceservice.api;

import com.xtc.marketing.invoiceservice.batch.BatchAppService;
import com.xtc.marketing.invoiceservice.batch.dto.BatchFileDTO;
import com.xtc.marketing.marketingcomponentdto.dto.Response;
import com.xtc.marketing.marketingcomponentdto.dto.SingleResponse;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 批处理接口
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api")
public class BatchApiController {

    private final BatchAppService batchAppService;

    /**
     * 文件列表
     *
     * @param date 日期，默认查询当天的文件列表，格式：yyyyMMdd
     * @return 文件列表
     */
    @GetMapping("/batch/files")
    public SingleResponse<BatchFileDTO> batchFiles(@Length(max = 8) @RequestParam(value = "date", required = false) String date) {
        BatchFileDTO dto = batchAppService.batchFiles(date);
        return SingleResponse.of(dto);
    }

    /**
     * 下载文件
     *
     * @param objectName 文件对象名称
     * @return 文件
     * @apiNote 只支持 batch/ 路径下的文件
     */
    @GetMapping("/batch/file-download")
    public ResponseEntity<Resource> downloadFile(@NotBlank @Length(max = 200) @RequestParam("objectName") String objectName) {
        return batchAppService.downloadFile(objectName);
    }

    /**
     * 上传待处理文件
     *
     * @param file 文件
     * @return 文件对象名称
     */
    @PostMapping("/batch/upload-file")
    public SingleResponse<String> uploadFile(@NotNull @RequestPart("file") MultipartFile file) {
        String objectName = batchAppService.uploadFile(file);
        return SingleResponse.of(objectName);
    }

    /**
     * 删除文件
     *
     * @param objectName 文件对象名称
     * @return 执行结果
     */
    @PostMapping("/batch/remove-file")
    public Response removeFile(@NotBlank @Length(max = 200) @RequestParam("objectName") String objectName) {
        batchAppService.removeFile(objectName);
        return Response.buildSuccess();
    }

}
