package com.xtc.marketing.invoiceservice.job;

import com.xtc.marketing.invoiceservice.config.BaseJob;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 批处理任务
 */
@RequiredArgsConstructor
@Component
public class BatchJob extends BaseJob {

    private final BatchJobService batchJobService;

    /**
     * 非系统数据发票冲红
     */
    @XxlJob("notSystemIssueRedBatchJob")
    public void notSystemIssueRedBatchJob() {
        executeShard(batchJobService::notSystemIssueRedBatchJob);
    }

}
