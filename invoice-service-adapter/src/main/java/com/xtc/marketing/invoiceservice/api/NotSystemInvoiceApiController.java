package com.xtc.marketing.invoiceservice.api;

import com.xtc.marketing.invoiceservice.invoice.NotSystemInvoiceAppService;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.command.NormalIssueRedCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.query.NotSystemFileGetQry;
import com.xtc.marketing.invoiceservice.invoice.dto.query.NotSystemRedDetailQry;
import com.xtc.marketing.marketingcomponentdto.dto.SingleResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 非系统数据接口
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api")
public class NotSystemInvoiceApiController {

    private final NotSystemInvoiceAppService notSystemInvoiceAppService;

    /**
     * 非系统文件获取
     *
     * @param qry 参数
     * @return 文件
     */
    @GetMapping("/not-system/invoice/file")
    public ResponseEntity<Resource> invoiceFile(@Valid NotSystemFileGetQry qry) {
        return notSystemInvoiceAppService.invoiceFile(qry);
    }

    /**
     * 非系统红票详情
     *
     * @param qry 参数
     * @return 发票详情
     */
    @GetMapping("/not-system/invoice/red-detail")
    public SingleResponse<InvoiceDTO> notSystemRedDetail(@Valid NotSystemRedDetailQry qry) {
        InvoiceDTO dto = notSystemInvoiceAppService.notSystemRedDetail(qry);
        return SingleResponse.of(dto);
    }

    /**
     * 税控发票冲红
     *
     * @param cmd 参数
     * @return 发票详情
     */
    @PostMapping("/not-system/invoice/normal-issue-red")
    public SingleResponse<InvoiceDTO> normalIssueRed(@Valid @RequestBody NormalIssueRedCmd cmd) {
        InvoiceDTO dto = notSystemInvoiceAppService.normalIssueRed(cmd);
        return SingleResponse.of(dto);
    }

}
