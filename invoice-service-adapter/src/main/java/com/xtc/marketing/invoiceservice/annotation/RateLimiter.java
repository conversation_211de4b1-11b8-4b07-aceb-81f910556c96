package com.xtc.marketing.invoiceservice.annotation;

import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;

import java.lang.annotation.*;

/**
 * 流量限流器注解
 * <p>限制参数，默认：空字符串</p>
 * <p>限制流量，默认：0</p>
 * <p>限制时间间隔，默认：3秒</p>
 */
@Documented
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Order(Ordered.HIGHEST_PRECEDENCE)
public @interface RateLimiter {

    /**
     * 限制参数，默认：空字符串
     */
    String limitParam() default "";

    /**
     * 限制流量，默认：0
     */
    int rate() default 0;

    /**
     * 限制时间间隔，默认：3秒
     */
    int rateInterval() default 3;

}
