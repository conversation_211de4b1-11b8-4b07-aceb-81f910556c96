# *baiwang.output.invoice.issue*

*发票开具企业系统可以通过调用此接口传递发票开具所需数据，完成直连开票。此接口支持开具增值税专用发票、增值税纸质普通发票、增值税电子发票、增值税卷式发票和增值税电子专用发票。开具纸质发票时此接口只返回开具数据，打印纸质发票需要调用【发票打印】接口。此接口支持开具正数发票和负数发票（红冲）共用，通过开票类型参数区分。对于红冲：1.
电子发票只能红冲不能作废；纸质发票当月可以作废，跨月只能红冲。2. 专用发票红冲时，需先申请红字信息表。3增加红冲uuid字段。*

## 一、请求公共参数

| 名称        | 类型     | 是否必须 | 描述         |
|-----------|--------|------|------------|
| method    | String | 是    | 接口指定唯一标识   |
| requestId | String | 是    | 请求唯一标识     |
| version   | String | 是    | 版本号，当前为1.0 |

## 二、请求业务参数

| ID                                 | 类型         | 长度   | 是否必须 | 描述                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
|:-----------------------------------|:-----------|:-----|:-----|:------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| taxNo                              | String     | 20   | 是    | 税号                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| taxUserName                        | String     | 20   | 否    | 登录名，用于一税号多账户区分用，该登录名为税局页面登录名                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| invoiceTerminalCode                | String     | 30   | 否    | 开票终端代码。服务器/税务ukey 用户专普票必填，电子票非必填；税务ukey终端使用机器编号；盘用户，盘号终端选填，如果只有一个终端可不填，全电票种选填                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| isSplit                            | Boolean    | 10   | 否    | 超过发票单张限额是否需要拆分开具，默认不拆分（拆分只支持发票类型代码为 专票 004 普票 007 电子票 026的发票）                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| orgCode                            | String     | 64   | 否    | 组织机构编码，如果为空则上传至税号对应的机构下，如果维护了机构则按照机构归属待开信息，根据判断自行信息管理选择是否设置开票相关信息；根据组织机构编码获取销方信息                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |
| taxDiskNo                          | String     | 12   | 否    | 设备编号，即盘号，包括税控盘，航信盘，自持ukey。如果多个设备必填，只有一个终端可不填                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| formatGenerate                     | Boolean    | 10   | 否    | 是否需要生成版式返回版式链接(true / false)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| formatPushType                     | Boolean    | 1    | 否    | 版式生成是否推送(true / false)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |
| completionCustom                   | String     | 1    | 否    | 值为（1/0 1 需要补全 0不需要补全，默认为0）是否根据客户编码，购方税号，购方名称查询客户信息补全未填写的购方信息（购方税号，购方名称，购方地址电话，购方银行账号，邮箱，手机）                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |
| isAsync                            | String     | 1    | 否    | 开具请求类型：isAsync 0 同步 1 异步 默认同步开具（仅支持rpa）                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| isReturnRedInfo                    | String     | 1    | 否    | 是否返回蓝票已红冲红票信息，0否，1是，默认为0（仅支持税控发票）                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| data                               | Object     | ~    | 是    | ~                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| └ invoiceTypeCode                  | String     | 3    | 是    | 发票类型代码, 004：增值税专用发票；007：增值税普通发票；026：增值税电子发票；025：增值税卷式发票；028:增值税电子专用发票 01:全电发票(增值税专用发票) 02:全电发票(普通发票)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| └ paperInvoiceFlag                 | String     | 2    | 否    | 数电纸质发票标志，Y：是，N：否。税控类发票开具不校验此字段；暂只支持用数电电票红冲数电纸票                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
| └ paperInvoiceTypeCode             | String     | 10   | 否    | 选择纸质发票标志为Y时，纸票类型必填。票种为 普通发票02时可传： 04    2016版增值税普通发票（二联折叠票）， 05    2016版增值税普通发票（五联折叠票); 票种为 增值税专用发票01可传： 1130 增值税专用发票（中文三联无金额限制版） ，1140 增值税专用发票（中文四联无金额限制版） 1160 增值，税专用发票（中文六联无金额限制版） ，1170 增值税专用发票（中文七联无金额限制版）                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| └ invoiceType                      | String     | 32   | 否    | 开票类型 0:正数发票（蓝票） 1：负数发票（红票）默认0                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| └ invoiceSpecialMark               | String     | 2    | 否    | 特殊票种标志， 00：普通发票；01：农产品销售；02：农产品收购；08：成品油  机动车（默认是00普通发票）;16矿产品；03稀土； 全电类发票特殊票种标志：01 成品油发票；03：建筑服务发票；04：货物运输服务发票；05：不动产销售服务发票；06：不动产租赁服务发票；09：旅客运输发票；12：自产农产品销售；13：拖拉机和联合收割机；14：机动车；15：二手车；16：农产品收购；31：二手车*；24：报废产品收购；02：稀土；17：光伏收购；07：代收车船税；32：电子烟；18卷烟                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| └ isConfirmIssue                   | String     | 2    | 否    | 成品油单价过低是否确认开具标识：Y 是； N 否 为空时默认值为N （备注：专票单价过低强制开票会记入异常发票，红冲需要在电子税务局申请解除然后再红冲；普票单价过低开具成功发票可以正常红冲）                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| └ confirmIssue                     | String     | 1    | 否    | 税局二次确认是否继续开票，1：是；0：否；（03建筑服务发票，建筑服务发生地和销方注册地址不同时是否继续开票,24报废产品收购需要缴纳购置税的提示是否继续开具），若不传值，系统默认按照“是”处理;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| └ taxationMethod                   | String     | 1    | 否    | 征税方式， 0：普通征税；2：差额征税（默认是0普通征税）                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| └ taxationLabel                    | String     | 2    | 否    | 差额征税标签：01 全额开票、 02 差额开票 ；发票类型代码为01，02时且征税方式为2必填                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| └ reducedTaxCode                   | String     | 2    | 否    | 减按征税标识，01：个人出租住房；03：销售自己使用过的固定资产；05：住房租赁。默认为空,空代表无。                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └ invoiceListMark                  | String     | 1    | 否    | 0：无清单；1：带清单（专普票发票明细大于等于8行必须带清单）：大于8行必须为清单票(电子票只能为非请单票)（默认是0无清单），发票类型代码为01，02时该字段无                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| └ priceTaxMark                     | String     | 1    | 否    | 含税标志， 0：不含税；1：含税（默认不含税）                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| └ serialNo                         | String     | 50   | 是    | 开票流水号， 唯一标志开票请求。支持数字字母下划线组合。                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| └ orderNo                          | String     | 50   | 否    | 发票请求流水号                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| └ buyerTaxNo                       | String     | 20   | 否    | 购方单位税号， invoiceTypeCode=004、028、01（增值税专用发票、增值税电子专用发票、全电发票（增值税专用发票））开具时必传  invoiceSpecialMark=16&#124;17（农产品收购、光伏收购）开具时必传                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| └ buyerName                        | String     | 80   | 是    | 购方单位名称 全电为100个字符                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |
| └ buyerAddressPhone                | String     | 100  | 否    | 购方地址及电话， 增值税专用发票开具时必填，发票类型代码为01、02时该字段拆分为地址电话两个字段                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| └ buyerBankAccount                 | String     | 100  | 否    | 购方开户行及账号， 增值税专用发票开具时必填，发票类型代码为01、02时该字段拆分为银行名称、账号两个字段                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| └ drawer                           | String     | 20   | 否    | 开票人，税票选填，取值逻辑：如果终端有值取终端，如果没有去机构获取，如果都没有会自动获取机构下随机用户名称；数电票Web连接器选填，取值逻辑：taxUserName＞drawer＞默认开票人＞终端授权开票人，如果以上四处都未取到随机获取该税号下最近一次登录的数电账号对应的开票人；数电票乐企连接器必填，长度最大300字符。                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| └ checker                          | String     | 16   | 否    | 复核人， 16个字符；税控开具时： 为空时，如果终端有值取终端，如果没有去机构获取，若都没有则为空！全电类开具时：此字段非必填，即发票类型代码为01，02时该字段非必填；                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| └ payee                            | String     | 16   | 否    | 收款人， 16个字符；税控类开具时：若为空，如果终端有值取终端，如果没有去机构获取，若都没有则为空；全电类开具时：此字段非必填，即发票类型代码为01，02时该字段非必填                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| └ invoiceTotalPrice                | BigDecimal | 13   | 否    | 合计金额， 保留两位小数；支持价税分离                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └ invoiceTotalTax                  | BigDecimal | 13   | 否    | 合计税额， 保留两位小数；支持价税分离                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └ invoiceTotalPriceTax             | BigDecimal | 13   | 否    | 价税合计， 保留两位小数；支持价税分离                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └ remarks                          | String     | 200  | 否    | 备注 乐企长度为230个字符                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
| └ redInfoNo                        | String     | 20   | 否    | 红字信息表/确认单编号，仅invoiceType=1时需要传入数据 税控类红票开具时，invoiceTypeCode=004、028时必须传值，传入红字信息表编号； 全电类红票开具时，invoiceTypeCode=01、02时必须传值，传入红字确认单编号；                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| └ originalInvoiceCode              | String     | 12   | 否    | 原发票代码， invoiceType=1，税控负数普票开具时必传；红票选数电票时，此项可为空。                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| └ originalInvoiceNo                | String     | 8    | 否    | 原发票号码， invoiceType=1，税控负数普票开具时必传；红票选数电票时，此项可为空。                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| └ deductibleAmount                 | BigDecimal | 13   | 否    | 扣除额， taxationLabel=2，差额征税时必传。数值必须小于价税合计。                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| └ sellerAddressPhone               | String     | 100  | 否    | 销方地址及电话，发票类型代码为01、02时该字段拆分为地址、电话两个字段                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| └ sellerBankAccount                | String     | 100  | 否    | 销方开户行及账号，发票类型代码为01、02时不用此字段                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |
| └ ext                              | Map        | ~    | 否    | Map&#60;String, Object>, 扩展字段                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| └ systemName                       | String     | 150  | 否    | 第三方系统名称                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| └ systemId                         | String     | 50   | 否    | 第三方系统id                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| └ buyerEmail                       | String     | 100  | 否    | 客户邮箱                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| └ emailCarbonCopy                  | String     | 100  | 否    | 抄送人邮箱,多个用英文逗号隔开,最多5个抄送人信息 全电字符长度为200                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| └ buyerPhone                       | String     | 40   | 否    | 客户电话                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| └ userAccount                      | String     | 30   | 否    | 用户账号，用于个人维度数据标记                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| └ redIssueReason                   | String     | 10   | 否    | 红冲原因（1 销货退回 2 开票有误 3 服务终止 4 销售折让）税控红冲原因：建议按实际开票情况传入红冲原因，若不传系统会自动根据情况判断并赋值；全电红冲原因：必填                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └ discountType                     | String     | 10   | 否    | 整单折扣类型（数电发票暂不支持） 1 :按折扣金额价内折扣,2:按折扣金额价外折扣,3:按折扣率价内折扣,4:按折扣率价外折扣                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| └ discountAmount                   | BigDecimal | 20   | 否    | 整单折扣金额（数电发票暂不支持）,大于0小于发票总金额，如果是含税发票，大于0小于含税总金额                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
| └ discountRate                     | Integer    | 100  | 否    | 整单折扣率（数电发票暂不支持）,取值[1-100]正整数                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| └ mainGoodsName                    | String     | 10   | 否    | 卷式发票票样  01 02 03 04 05 06 07                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| └ buyerBankName                    | String     | 80   | 否    | 购买方银行名称，发票类型代码为01、02时该字段可用                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| └ buyerBankNumber                  | String     | 50   | 否    | 购买方银行账号，发票类型代码为01、02时该字段可用                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| └ buyerAddress                     | String     | 80   | 否    | 购买方地址，发票类型代码为01、02时该字段可用                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| └ buyerTelphone                    | String     | 20   | 否    | 购买方电话，发票类型代码为01、02时该字段可用                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| └ sellerBankName                   | String     | 80   | 否    | 销方银行名称，发票类型代码为01、02时该字段可用                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| └ sellerBankNumber                 | String     | 50   | 否    | 销方银行账号，发票类型代码为01、02时该字段可用                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| └ sellerAddress                    | String     | 200  | 否    | 销方地址，发票类型代码为01、02时该字段可用                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| └ sellerTelphone                   | String     | 20   | 否    | 销方电话，发票类型代码为01、02时该字段可用                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| └ redConfirmUuid                   | String     | 60   | 否    | 红字信息表UUID，开具全电负数发票必传                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| └ contractNumber                   | String     | 50   | 否    | 合同号                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └ voucherNo                        | String     | 50   | 否    | 凭证号                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └ buyerNaturalPerson               | String     | 1    | 否    | 购方自然人标记Y:自然人；  N:非自然人，默认为N                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| └ buyerCredentialsType             | String     | 3    | 条件必填 | 证件类型，特殊票种标志为16、24、17时必填； 特殊票种标志为其他时，证件类型、证件号码、国籍(或地区)同时为空或者同时不为空； 201：居民身份证；210：港澳居民来往内地通行证；213：台湾居民来往大陆通行证；227：中国护照；233：外国人永久居留身份证（外国人永久居留证）；237：中华人民共和国港澳居民居住证；238：中华人民共和国台湾居民居住证；208：外国护照；                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| └ buyerCredentialsNo               | String     | 20   | 条件必填 | 证件号码，特殊票种标志为16、24、17时必填； 特殊票种标志为其他时，证件类型、证件号码、国籍(或地区)同时为空或者同时不为空；                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| └ buyerNationalityRegion           | String     | 3    | 条件必填 | 国籍(或地区)，特殊票种标志为16、24、17时必填； 其他特殊票种标志时，证件类型、证件号码、国籍(或地区)同时为空或者同时不为空； 156: 中华人民共和国；344：中国香港特别行政区；446：中国澳门特别行政区；158：中国台湾；  其他国籍编码请参照：https://docs.qq.com/doc/DQ0tycU5uRG9WUFhw                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |
| └ displayBuyer                     | Boolean    | 10   | 否    | 购方开户行银行账号是否显示在备注，true：购方开户行银行账号显示在备注，或者false：购方开户行银行账号不显示在备注                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| └ displaySeller                    | Boolean    | 10   | 否    | 销方开户行银行账号是否显示在备注。true：销方开户行银行账号显示在备注，或者false：销方开户行银行账号不显示在备注                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| └ displayBuyerAddTel               | Boolean    | 10   | 否    | 购方地址和联系电话是否显示在备注，true：购方地址和联系电话显示在备注，false：购方地址和联系电话不显示在备注                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| └ displaySellerAddTel              | Boolean    | 10   | 否    | 销方地址和联系电话是否显示在备注，true：销方地址和联系电话显示在备注，false：销方地址和联系电话不显示在备注                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| └ mulPurchaserMark                 | String     | 1    | 否    | N：无共同购买方，Y：有共同购买方，传Y时共同购买方信息字段必填；不传默认按N处理；不支持乐企                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| └ credentialsType                  | String     | 20   | 否    | 证件类型，201:居民身份证;208:外国护照;210:港澳居民来往内地通行证;213:台湾居民来往大陆通行证;215:外国人居留证;219:香港永久性居民身份证;220:台湾身份证;221:澳门特别行政区永久性居民身份证;233:外国人永久居留身份证(外国人永久居留证);103:税务登记证;299:其他个人证件，特殊票种标识为24时必填                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| └ taxReason                        | String     | 2    | 否    | 报废产品收购1%转3%放弃优惠开具理由，2：前期已开具3%征收率的发票，发生销售折让、中止或者退回等情形需要开具3%征收率的红字发票，或者开票有误需要重新开具3%征收率的发票；3：因为实际经营业务需要，放弃享受减按1%征收率征收增值税政策:，不填时系统默认按3处理。                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| └ mulPurchaserList                 | List       | 1    | 否    | List&#60;OutputInvoiceIssueCoPurchaseInfo>, 共同购买方标识传Y时共同购买方明细字段必填                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| └└ purchaser                       | String     | 100  | 否    | 共同购买方传Y时，必填                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |
| └└ certificateType                 | String     | 2    | 否    | 共同购买方传Y时，必填。101:组织机构代码证102:营业执照103:税务登记证199:其他单位证件201:居民身份证202:军官证203:武警警官证204:士兵证205:军队离退休干部证206:残疾人证207:残疾军人证（1-8级）208:外国护照210:港澳居民来往内地通行证212:中华人民共和国往来港澳通行证213:台湾居民来往大陆通行证214:大陆居民往来台湾通行证215:外国人居留证216:外交官证217:使（领事）馆证218:海员证219:香港永久性居民身份证220:台湾身份证221:澳门特别行政区永久性居民身份证222:外国人身份证件223:就业失业登记证224:退休证225:离休证227:中国护照228:城镇退役士兵自谋职业证229:随军家属身份证明230:中国人民解放军军官转业证书231:中国人民解放军义务兵退出现役证232中国人民解放军士官退出现役证233:外国人永久居留身份证（外国人永久居留证）234:就业创业证235:香港特别行政区护照236:澳门特别行政区护照237:中华人民共和国港澳居民居住证238:中华人民共和国台湾居民居住证239:中华人民共和国外国人工作许可证》（A类）240:《中华人民共和国外国人工作许可证》（B类）241:《中华人民共和国外国人工作许可证》（C类）291:出生医学证明299:其他个人证件                                                                                                                                                                                                                                       |
| └└ certificateNo                   | String     | 20   | 否    | 共同购买方传Y时，必填                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |
| └ invoiceDetailsList               | List       | ~    | 是    | List&#60;OutputInvoiceIssueInvoiceDetail>, 商品明细节点                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| └└ goodsLineNo                     | Integer    | 4    | 是    | 明细行号                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| └└ originalInvoiceDetailNo         | String     | 4    | 否    | 对应蓝票明细行号 税控类红字发票开具时无需传值； 全电类红字发票开具时必须传值；                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| └└ invoiceLineNature               | String     | 1    | 否    | 发票行性质， 0：正常行；1：折扣行；2：被折扣行，默认为0                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
| └└ goodsCode                       | String     | 40   | 否    | 税收分类编码（末级节点）                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| └└ goodsPersonalCode               | String     | 50   | 否    | 商品编码， 可以支持根据商品名称或商品编码进行获取【增加商品编码字段】：平台必须维护商品信息                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
| └└ goodsName                       | String     | 100  | 否    | 商品名称， 支持根据商品或商品编码获取商品信息：平台必须维护商品信息   ～～二选一必填                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| └└ goodsSpecification              | String     | 36   | 否    | 规格型号，如果特殊票种为03、09、05、06，字段填写无效；特殊票种为15和31时填写车架号/车辆识别号；                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |
| └└ goodsUnit                       | String     | 14   | 否    | 计量单位 特殊票种为12时必填,只能为辆 乐企：单位、数量、单价应同时为空或同时不为空；rpa：01 成品油发票必填 只能为吨或升；特殊票种为稀土时必填，只能为公斤或吨；如果特殊票种为03、09、05、06时，字段填写无效；代收车船税发票必填，只能为辆；电子烟发票必填，只能为盒；卷烟发票必填，卷烟生产企业开具卷烟发票单位必须为“万支”；卷烟批发企业开具卷烟发票 的单位必须为“标准条”或“支”；                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |
| └└ goodsQuantity                   | BigDecimal | 14   | 否    | 商品数量，最多允许17位（含小数点和负号），小数点后最多允许13位，特殊票种为05（不动产销售服务发票）、06（不动产租赁服务发票）、12（机动车）、01 （全电成品油发票）时必填；乐企：单位、数量、单价应同时为空或同时不为空；如果特殊票种为03时，字段填写无效；特殊票种为稀土时，单价数量应该同时不为空。                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| └└ goodsPrice                      | BigDecimal | 13   | 否    | 商品单价，最多允许17位（含小数点），小数点后最多允许13位，特殊票种为12时必填  乐企：单位、数量、单价应同时为空或同时不为空；rpa : 01 成品油发票 必填；如果特殊票种为03时，字段填写无效；特殊票种为稀土时，单价数量应该同时不为空。                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |
| └└ goodsTotalPrice                 | BigDecimal | 13   | 是    | 金额，小数点后2位,超长自动保留两位小数                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| └└ goodsTotalTax                   | BigDecimal | 13   | 否    | 税额，小数点后2位,超长自动保留两位小数， 如果为空，根据金额、税率计算得出                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |
| └└ goodsTaxRate                    | BigDecimal | 5    | 是    | 税率,超长自动保留三位小数                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| └└ vatSpecialManagement            | String     | 200  | 否    | 优惠政策类型，preferentialMarkFlag=1，使用优惠政策时必传，如“免税”、“50%先征后退”、“即征即退50%”等                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| └└ freeTaxMark                     | String     | 1    | 否    | 零税率标识：空 代表不使用零税率； 1：出口免税和其他免税优惠政策； 2：不征增值税；3：普通零税率                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| └└ preferentialMarkFlag            | String     | 1    | 否    | 是否使用优惠政策， 0：未使用；1：使用 根据商品信息获取                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| └└ goodsDiscountAmount             | BigDecimal | 20   | 否    | 明细行折扣金额,全电不支持该字段                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |
| └└ ext                             | Map        | ~    | 否    | Map&#60;String, Object>, 扩展字段                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| └ invoiceSpecialInfoList           | List       | ~    | 否    | List&#60;OutputInvoiceIssueInvoiceSpecialInfo>, List<OutputInvoiceIssueInvoiceSpecialInfo>, 特定业务信息节点，当特殊票种标志为03、04、05、06、07时必填；当特殊票种标志为09时特定要素信息字段必须同时为空或同时不为空；                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| └└ buildingLocalAddress            | String     | 120  | 是    | 建筑服务特定要素-建筑服务发生地，按照省、市、区/县三级传值，以&符间隔，举例“北京市&东城区、河北省&石家庄市&正定县”(建筑服务发生地和详细地址之和为120)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| └└ buildingDetailAddress           | String     | 120  | 否    | 建筑服务特定要素-建筑服务详细地址，举例“北京市海淀区清华东路17号”(建筑服务发生地和详细地址之和为120)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| └└ buildingName                    | String     | 80   | 是    | 建筑服务特定要素-建筑项目名称                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| └└ buildingLandTaxNo               | String     | 16   | 否    | 建筑服务特定要素-土地增值税项目编号                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| └└ buildingCrossSign               | String     | 8    | 是    | 建筑服务特定要素-跨地（市）标志；标志：Y：是；N：否                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |
| └└ kqysssxbgglBm                   | String     | 50   | 否    | 跨区域涉税事项报验管理编号，跨地(市)标志为Y时，必填 乐企不支持                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| └└ transport_departure             | String     | 80   | 是    | 货物运输特定要素-起运地                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| └└ transport_arrive                | String     | 80   | 是    | 货物运输特定要素-到达地                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| └└ transport_tool_type             | String     | 40   | 是    | 货物运输特定要素-运输工具种类，铁路运输、公路运输、水路运输、航空运输、管道运输、其他运输工具                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| └└ transport_tool_num              | String     | 40   | 是    | 货物运输特定要素-货物运输特定业务：运输工具牌号                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| └└ transport_goods_name            | String     | 80   | 是    | 货物运输特定要素-货物运输特定业务：运输货物名称                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| └└ propertyPropertyNo              | String     | 64   | 否    | 不动产销售服务-房屋产权证书/不动产权证号码                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |
| └└ propertyAddress                 | String     | 120  | 是    | 不动产销售服务-不动产地址，按照省、市、区/县三级传值，以&符间隔，举例“北京市&东城区、河北省&石家庄市&正定县”(不动产地址和详细地址之和为120)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| └└ propertyDetailAddress           | String     | 120  | 是    | 不动产销售服务-详细地址，举例“北京市海淀区清华东路17号”(不动产地址和详细地址之和为120)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |
| └└ propertyContractNo              | String     | 28   | 否    | 不动产销售服务-不动产单元代码，不可与网签合同备案编号同时填写；                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |
| └└ propertyLandTaxNo               | String     | 16   | 否    | 不动产销售服务-土地增值税项目编号                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| └└ propertyCrossSign               | String     | 8    | 是    | 不动产销售服务-跨地（市）标志；标志：Y：是；N：否                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| └└ propertyAreaUnit                | String     | 16   | 是    | 不动产销售服务-面积单位 枚举值：平方千米、平方米、公顷、亩、h㎡、k㎡、㎡                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |
| └└ propertyApprovedPrice           | BigDecimal | 24,6 | 否    | 不动产销售服务-核定计税价格                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
| └└ propertyDealPrice               | BigDecimal | 24,6 | 否    | 不动产销售服务-实际成交含税金额                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |
| └└ leasePropertyNo                 | String     | 64   | 是    | 不动产租赁-房屋产权证书/不动产权证号码                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| └└ leaseAddress                    | String     | 120  | 是    | 不动产租赁-不动产地址，按照省、市、区/县三级传值，以&符间隔，举例“北京市&东城区、河北省&石家庄市&正定县”（不动产地址和详细地址之和为120）                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| └└ leaseDetailAddress              | String     | 120  | 是    | 不动产租赁-详细地址，举例“北京市海淀区清华东路17号”（不动产地址和详细地址之和为120）                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
| └└ leaseCrossSign                  | String     | 8    | 是    | 不动产租赁-跨地（市）标志；标志：Y：是；N：否                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| └└ leaseAreaUnit                   | String     | 16   | 是    | 不动产租赁-面积单位 枚举值：平方千米、平方米、公顷、亩、h㎡、k㎡、㎡、米(乐企不支持)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| └└ leaseHoldDateStart              | String     | 10   | 是    | 不动产租赁-租赁期起，格式传yyyy-MM-dd（如果传年月日时分，系统自动截取）； 开具3040502020200000000商编的商品时，格式为yyyy-MM-dd HH:mm ；                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| └└ leaseHoldDateEnd                | String     | 10   | 是    | 不动产租赁-租赁期止，格式传yyyy-MM-dd（如果传年月日时分，系统自动截取）； 开具3040502020200000000商编的商品时，格式为yyyy-MM-dd HH:mm；                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |
| └└ cph                             | String     | 10   | 否    | 车牌号；开具3040502020200000000商编的商品时可填写 开具其他商编的商品时，填写无效                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| └└ carriageName                    | String     | 20   | 是    | 出行人                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └└ carriageId                      | String     | 20   | 是    | 出行人证件类型，  101:组织机构代码证；102:营业执照；103:税务登记证；199:其它单位证件；201:居民身份证；202:军官证；203:武警警官证；204:士兵证；205:军队离退休干部证；206:残疾人证；207:残疾军人证（1-8级）；208:外国护照；210:港澳居民来往内地通行证；212:中华人民共和国往来港澳通行证；213:台湾居民来往大陆通行证；214:大陆居民往来台湾通行证；215:外国人居留证；216:外交官证；217:使（领事）馆证；219:香港永久性居民身份证；218:海员证；220:台湾身份证；221:澳门特别行政区永久性居民身份证；222:外国人身份证件；224:就业失业登记证；225:退休证；226:离休证；227:中国护照；228:城镇退役士兵自谋职业证；229:随军家属身份证明；230:中国人民解放军军官专业证书；231:中国人民解放军义务兵退出现役证；232:中国人民解放军士官退出现役证；233:外国人永久居留身份证（外国人永久居留证）；234:就业创业证；235:香港特别行政区护照；236:澳门特别行政区护照；237:中华人民共和国港澳居民身份证；238:中华人民共和国台湾居民身份证；239:《中华人民共和国外国人工作许可证》（A类）；240:《中华人民共和国外国人工作许可证》（B类）；241:《中华人民共和国外国人工作许可证》（C类）；291:医学出生证明；299:其他个人证件；                                                                                                                                                                                           |
| └└ carriageIdNo                    | String     | 20   | 是    | 出行人证件号码                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| └└ carriageDateYmd                 | String     | 20   | 是    | 出行日期； yyyy-MM-dd                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |
| └└ carriageLeave                   | String     | 80   | 是    | 出发地 出发省市区(出发地+详细地址总长度80)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| └└ carriageLeaveAddress            | String     | 80   | 否    | 出发地详细地址(出发地+详细地址总长度80)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |
| └└ carriageArrive                  | String     | 80   | 是    | 到达地 到达省市区(出发地+详细地址总长度80)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| └└ carriageArriveAddress           | String     | 80   | 否    | 到达地详细地址(出发地+详细地址总长度80)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |
| └└ carriageVehicleType             | String     | 1    | 是    | 交通工具类型，1：飞机；2：火车；3：长途汽车；4：公共交通；5：出租车；6：汽车；7：船舶；9：其他                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └└ carriageVehicleGrade            | String     | 20   | 是    | 等级，仅当交通工具种类为“飞机、火车、船舶”时，必填；飞机：公务舱、头等舱、经济舱 火车：一等座、二等座、软席（软座、软卧）、硬席（硬座、硬卧）船舶：一等舱、二等舱、三等舱                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |
| └└ propertyContractOnlineSigningNo | String     | 28   | 否    | 不动产销售服务-网签合同备案编号，不可与不动产单元代码同时填写；                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |
| └└ tractorRegistry                 | String     | 2    | 否    | 是否用于办理拖拉机和联合收割机登记 Y：是，N：否                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| └└ tractorEngineNo                 | String     | 40   | 否    | 发动机号码                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| └└ tractorVehicleNo                | String     | 40   | 否    | 底盘号/机架号                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| └└ usedCarAllelectricNo            | String     | 20   | 否    | 二手车销售统一发票数电票号码，特殊票种为31时该字段必填。二手车销售统一发票数电票号码、二手车销售统一发票代码和二手车销售统一发票号码需同时为空或不为空                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| └└ usedCarCode                     | String     | 12   | 否    | 二手车销售统一发票代码，特殊票种为31时该字段必填。                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| └└ usedCarNo                       | String     | 8    | 否    | 二手车销售统一发票号码，特殊票种为31时该字段必填。                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| └└ registrationNumber              | String     | 40   | 条件必填 | 代收车船税特定要素-牌号/船舶登记号，当特殊票种标志为07时必填。                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| └└ policyNumber                    | String     | 40   | 条件必填 | 代收车船税特定要素-保险单号，当特殊票种标志为07时必填。                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| └└ taxPeriod                       | String     | 20   | 条件必填 | 代收车船税特定要素-税款所属期（详细至月，格式是yyyy-MM+空格+yyyy-MM），当特殊票种标志为07时必填。                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| └└ taxCollectedAmount              | BigDecimal | 13   | 条件必填 | 代收车船税特定要素-代收车船税金额，必须大于0，当特殊票种标志为07时必填。                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |
| └└ lateFeeAmount                   | BigDecimal | 13   | 条件必填 | 代收车船税特定要素-滞纳金金额，可以为0，当特殊票种标志为07时必填。                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └└ totalAmount                     | BigDecimal | 13   | 条件必填 | 代收车船税特定要素-金额合计，必须大于0，当特殊票种标志为07时必填。                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └└ vehicleCode                     | String     | 17   | 条件必填 | 代收车船税特定要素-车辆识别代号/车架号码，当特殊票种标志为07时必填。                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| └ invoiceBalanceinfoList           | List       | ~    | 否    | List&#60;OutputInvoiceIssueInvoiceBalanceinfo>, 全电发票差额扣除额凭证明细，当征税方式为：差额征税-差额开票时必填                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| └└ balanceNo                       | String     | 10   | 是    | 序号                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| └└ balanceType                     | String     | 10   | 是    | 凭证类型：01 全电发票、02 增值税专用发票、03 增值税普通发票、04 营业税发票、05 财政票据、06 法院裁决书、07 契税完税凭证、08 其他发票类、09 其他扣除凭证                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| └└ balanceElectricNo               | String     | 30   | 否    | 全电发票号码：凭证类型为01时必填                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| └└ balanceInvoiceCode              | String     | 12   | 否    | 发票代码：凭证类型为02、03、04时必填                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| └└ balanceInvoiceNo                | String     | 8    | 否    | 发票号码：凭证类型为02、03、04时必填                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| └└ balanceVoucherNo                | String     | 20   | 否    | 凭证号码                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| └└ balanceIssueDate                | String     | 20   | 否    | 开具日期：凭证类型为01、02、03、04时必填                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| └└ balanceTotalAmount              | BigDecimal | 13   | 是    | 凭证合计金额                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |
| └└ balanceDeductAmount             | BigDecimal | 13   | 是    | 本次扣除金额                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |
| └└ balanceRemarks                  | String     | 100  | 否    | 备注：当凭证类型为08 其他发票类、09 其他扣除凭证时，备注必填。                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| └└ balanceSource                   | String     | 2    | 是    | 凭证来源：默认传0 ；0手工录入、1勾选导入、2模版导入                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| └ additionalDetails                | List       | 1    | 否    | List&#60;OutputInvoiceIssueAdditionalDetail>, 附加要素列表                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| └└ fieldTitle                      | String     | 200  | 否    | 附加要素名称                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |
| └└ fieldKey                        | String     | 200  | 否    | 附加要素唯一编码（不可重复）                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
| └└ fieldType                       | String     | 200  | 否    | 附加要素类型：date（日期）、input（文本）、inputnumber（数值）                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| └└ fieldValue                      | String     | 200  | 否    | 附加要素值，当为日期类型时，格式为yyyyy-MM-dd（年-月-日）                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └ accessPlatformNo                 | String     | 20   | 否    | 直连单位平台编号  乐企直连单位平台编号，当前税号关联多个直连单位开票传入，非必填  未传入时根据默认直连单位平台编码赋值                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| └ drawerCredentialsType            | String     | 4    | 否    | 开票人证件类型  当使用联用（使用单位与直连平台无控制关系）、他用能力时传入，非必填  传参时开票人证件类型需要开票人对应并且与关联直连单位申请接入时的信息一致，其他情况非必填   未传入时默认取乐企基础数据配置的默认开票人信息  101：组织机构代码证  102：营业执照  103：税务登记证  199：其他单位证件  201：居民身份证  202：军官证  203：武警警官证  204：士兵证  205：军队离退休干部证  206：残疾人证  207：残疾军人证（1-8级）  208：外国护照  209：港澳同胞回乡证  210：港澳居民来往内地通行证  211：台胞证  212：中华人民共和国往来港澳通行证  213：台湾居民来往大陆通行证  214：大陆居民往来台湾通行证  215：外国人居留证  216：外交官证  217：使（领事）馆证  218：海员证  219：香港永久性居民身份证  220：台湾身份证  221：澳门特别行政区永久性居民身份证  222：外国人身份证件  223：高校毕业生自主创业证  224：就业失业登记证  225：退休证  220：离休证  227：中国护照  228：城镇退役士兵自谋职业证  229：随军家属身份证明  230：中国人民解放军军官转业证书  231：中国人民解放军义务兵退出现役证  232：中国人民解放军士官退出现役证  233：外国人永久居留身份证（外国人永久居留证）  234：就业创业证  235：香港特别行政区护照  236：澳门特别行政区护照  237：中华人民共和国港澳居民居住证  238：中华人民共和国台湾居民居住证  239：《中华人民共和国外国人工作许可证》（A类）  240：《中华人民共和国外国人工作许可证》（B类）  241：《中华人民共和国外国人工作许可证》（C类）  291：出生医学证明  299：其他个人证件 |
| └ drawerCredentialsNo              | String     | 30   | 否    | 开票人证件号码  当使用联用（使用单位与直连平台无控制关系）或他用能力时使用，非必填  传参时开票人证件号码需要与开票人证件类型对应并且与关联直连单位申请接入时的信息一致   未传入时默认根据乐企基础数据配置的默认开票人信息进行赋值，若不存在则默认为空                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |

备注：用 └ 标识层级关系

## 三、响应公共参数

| 名称               | 类型      | 描述                                 |
|------------------|---------|------------------------------------|
| success          | Boolean | 接口调用成功或失败的标志 true：成功，false：失败      |
| requestId        | String  | 请求唯一标识， 与调用接口时传入的requestId一致       |
| model            | <T>     | 业务接口调用成功后返回的具体响应信息，不同的接口返回的model不同 |
| message          | String  | 响应message                          |
| └ errorCode      | String  | 调用失败后，返回的错误码, 服务端错误码或业务层错误码        |
| └ errorMessage   | String  | 调用失败后，返回的错误信息                      |
| └ successMessage | String  | 调用成功后，返回的成功信息                      |
| └ innerRequestId | String  | 下游业务层的requestId，若为业务调用失败，则返回此字段    |

## 四、响应业务参数

| ID                      | 名称                  | 类型         | 长度   | 描述                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
|-------------------------|---------------------|------------|------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| fail                    | 拆分开具失败返回拆分的发票金额明细信息 | List       |      | List&#60;OutputInvoiceIssueInvoice>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| └ serialNo              | 开票流水号               | String     | 50   | 唯一标志开票请求                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| └ invoiceTotalPrice     | 合计金额                | BigDecimal | 13   | ~                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └ invoiceTotalTax       | 合计税额                | BigDecimal | 13   | ~                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └ invoiceTotalPriceTax  | 价税合计                | BigDecimal | 13   | ~                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └ invoiceDetailsList    | 商品明细                | List       |      | List&#60;OutputInvoiceIssueInvoiceDetail>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |
| └└ goodsLineNo          | 明细行号                | String     | 4    | ~                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └└ invoiceLineNature    | 发票行性质               | String     | 1    | 0：正常行 1：折扣行 2：被折扣行                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| └└ goodsCode            | 商品编码                | String     | 40   | ~                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └└ goodsName            | 商品名称                | String     | 92   | ~                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └└ goodsSpecification   | 规格型号                | String     | 40   | 规格型号，特殊票种为15和31时，该字段代表车架号/车辆识别号；                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| └└ goodsUnit            | 计量单位                | String     | 14   | ~                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └└ goodsQuantity        | 商品数量                | String     | 14   | ~                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └└ goodsPrice           | 商品单价                | BigDecimal | 13   | ~                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └└ goodsTotalPrice      | 金额                  | BigDecimal | 13   | ~                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └└ goodsTotalTax        | 税额                  | BigDecimal | 13   | ~                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └└ goodsTaxRate         | 税率                  | BigDecimal | 5    | ~                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └└ priceTaxMark         | 含税标志                | String     | 1    | 0：不含税 1：含税                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| └└ vatSpecialManagement | 增值税特殊管理             | String     | 200  | ~                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └└ freeTaxMark          | 零税率标识               | String     | 1    | 空代表无 1 出口免税和其他免税优惠政策 2 不征增值税 3 普通零税率                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |
| └└ preferentialMark     | 是否使用优惠政策            | String     | 1    | 0:未使用，1:使用                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| success                 | 开具成功返回数据            | List       | ~    | List&#60;OutputInvoiceIssueInvoiceResult>, 部分开具或全部开具返回开具成功部分信息                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |
| └ invoiceCode           | 发票代码                | String     | 12   | ~                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └ invoiceNo             | 发票号码                | String     | 20   | ~                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └ invoiceCheckCode      | 校验码                 | String     | 22   | 校验码，发票类型为01/02时不返回此参数                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| └ invoiceDate           | 开票日期                | String     | 14   | 格式：yyyyMMddHHmmss                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └ invoiceQrCode         | 二维码                 | String     | 8182 | ~                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └ taxControlCode        | 税控码                 | String     | 22   | 税控码，发票类型为01/02时不返回此参数                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| └ invoiceTypeCode       | 发票类型代码              | String     | 22   | ~                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └ serialNo              | 流水号                 | String     | 22   | ~                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └ eInvoiceUrl           | 电子发票地址              | String     | 1024 | 电票版式文件链接。文件生成/下载存在延迟，未获取到文件流，请继续补偿获取。                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| └ invoiceTotalPrice     | 合计金额                | BigDecimal | 13   | ~                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └ invoiceTotalPriceTax  | 价税合计                | BigDecimal | 13   | ~                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └ invoiceTotalTax       | 合计税额                | BigDecimal | 13   | ~                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └ paperInvoiceCode      | 纸质发票代码              | String     | 12   | 全电纸质发票代码                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| └ paperInvoiceNo        | 纸质发票号码              | String     | 8    | 全电纸质发票号码                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| └ mulPurchaserMark      | 共同购买方标识             | String     | 1    | N：无共同购买方，Y：有公同购买方                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └ mulPurchaserList      | 共同购买方明细             | List       | 1    | List&#60;OutputInvoiceIssueCoPurchaseInfo>,                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| └└ purchaser            | 共同购买方姓名             | String     | 100  | ~                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └└ certificateType      | 证件类型                | String     | 2    | 101:组织机构代码证102:营业执照103:税务登记证199:其他单位证件201:居民身份证202:军官证203:武警警官证204:士兵证205:军队离退休干部证206:残疾人证207:残疾军人证（1-8级）208:外国护照210:港澳居民来往内地通行证212:中华人民共和国往来港澳通行证213:台湾居民来往大陆通行证214:大陆居民往来台湾通行证215:外国人居留证216:外交官证217:使（领事）馆证218:海员证219:香港永久性居民身份证220:台湾身份证221:澳门特别行政区永久性居民身份证222:外国人身份证件223:就业失业登记证224:退休证225:离休证227:中国护照228:城镇退役士兵自谋职业证229:随军家属身份证明230:中国人民解放军军官转业证书231:中国人民解放军义务兵退出现役证232中国人民解放军士官退出现役证233:外国人永久居留身份证（外国人永久居留证）234:就业创业证235:香港特别行政区护照236:澳门特别行政区护照237:中华人民共和国港澳居民居住证238:中华人民共和国台湾居民居住证239:中华人民共和国外国人工作许可证》（A类）240:《中华人民共和国外国人工作许可证》（B类）241:《中华人民共和国外国人工作许可证》（C类）291:出生医学证明299:其他个人证件 |
| └└ certificateNo        | 证件号码                | String     | 20   | ~                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └ invoiceDetailsList    | 发票明细集合              | List       | ~    | List&#60;OutputInvoiceIssuePreInvoiceDetailVO>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |
| └└ goodsLineNo          | 明细行号                | Integer    | ~    | ~                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └└ goodsName            | 商品名称                | String     | 100  | ~                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └└ goodsCode            | 商品编码                | String     | 50   | ~                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └└ goodsPrice           | 商品单价                | BigDecimal | 13   | ~                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └└ goodsUnit            | 单位                  | String     | 14   | ~                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └└ goodsQuantity        | 商品数量                | BigDecimal | 14   | ~                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └└ goodsTotalPrice      | 商品金额                | BigDecimal | 13   | ~                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └└ goodsTaxRate         | 税率                  | BigDecimal | 5    | ~                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └└ goodsTotalTax        | 商品税额                | BigDecimal | 13   | ~                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| └└ priceTaxMark         | 含税标志                | String     | 1    | 0：不含税 1：含税                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| └└ goodsSpecification   | 规格型号                | String     | 36   | 规格型号，特殊票种为15和31时，该字段代表车架号/车辆识别号；                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |

## 五、错误码

| 名称                    | 错误描述                                                                                                 |
|:----------------------|------------------------------------------------------------------------------------------------------|
| 00000004              | 没有USB设备                                                                                              |
| 00000006              | 税控设备密码错误 DiskErr:090003                                                                              |
| 00000007              | 选择税务应用错误 DiskErr:09c000                                                                              |
| 0000000A              | 证书口令错误 CftCode:000000A7 CftError:未插USBKEY(0xA7)                                                      |
| 0000000B              | 获取证书纳税人识别号时异常 CftCode:30000002 CftError:                                                             |
| 00000014              | 获取税控设备时钟异常 DiskErr:09c000                                                                            |
| 00000016              | 获取税控设备发票类型异常，请确定是否开通该类型发票-304                                                                        |
| 00000030              | 获取税控盘当前发票模板时异常 DiskErr:09c000                                                                        |
| 00000034              | 当前可用发票份数为0，请领购发票-801                                                                                 |
| 00000038              | 超过离线开票限定张数，请等待发票上传完毕后重试-706                                                                          |
| 000000A7              | 读取税控设备证书异常，请重试                                                                                       |
| 00000200              | 通知单编号长度错误                                                                                            |
| 00000201              | 购货单位识别号格式错误                                                                                          |
| 00000204              | 第1行费用项目，此税率不合法                                                                                       |
| 00000300              | 超出注册码截止时间                                                                                            |
| 0009240B              | 发票开具-税控设备操作错误-商品库存偏移有误                                                                               |
| 0009C001              | 发票开具-税控设备操作错误-税务UKey未个性化                                                                             |
| 0009C007              | 发票开具-税控设备操作错误-控制开票标志已被启用，禁止开票                                                                        |
| 0009D101              | 已超过单张发票开票金额限额-707                                                                                    |
| 0009D106              | 当前可用发票份数为0，请领购发票-801                                                                                 |
| 0009D109              | 已到开票截止日期，禁止开票-805                                                                                    |
| 0009D10B              | 校验税控设备口令-税控设备操作错误-口令锁定                                                                               |
| 0009D110              | 获取税控设备发票类型异常，请确定是否开通该类型发票-304                                                                        |
| 0009D11E              | 验签失败发票份数超过限定张数，请到开票软件中对验签失败发票进行作废                                                                    |
| 0009D11F              | 超过离线开票限定时间，请等待发票上传完毕后重试-706                                                                          |
| 0009D121              | 超过离线开票限定金额，请等待发票上传完毕后重试-706                                                                          |
| 0009D122              | 超过离线开票上传限定截止日期，请等待发票上传完毕后重试-706                                                                      |
| 00101010              | 没有USB设备                                                                                              |
| 01000011              | 购货单位地址电话格式有误                                                                                         |
| 01000022              | 商品折扣金额不能为非负数_FyxmN2                                                                                  |
| 010000A1              | 小规模纳税人发票只能开具（5%、3%、1.5%、0%）的税率!_FyxmN1                                                               |
| 010000B6              | 商品编码是汇总项或者商品编码已禁用_FyxmN1                                                                             |
| 010000B7              | 没有此税率的授权信息_FyxmN1                                                                                    |
| 010000EC              | 税率为0%，零税率标识不能为空_FyxmN1                                                                               |
| 02000005              | 输入口令长度错误                                                                                             |
| 0200000A              | 输入纳税人识别号有误                                                                                           |
| 0200007F              | 根据税号盘号获取注册码信息失败，请检查维护的终端信息是否正确                                                                       |
| 10000                 | 服务间调用超时                                                                                              |
| 10005                 | 平台中并未有此商品，请先去平台维护商品信息！                                                                               |
| 20019                 | 调用[发票开具]接口失败！【开票类型代码】字段不能为空!!!                                                                       |
| 20020                 | 根据开票点请求参数或盘用户默认唯一终端未获取到开票点信息！                                                                        |
| 20021                 | 根据税控盘编号未获取到开票点信息！                                                                                    |
| 20022                 | 根据开票点代码未获取到开票点信息！                                                                                    |
| 20023                 | 未获取到电票开票点信息！                                                                                         |
| 20024                 | 根据查询条件获取到[6]个开票终端                                                                                    |
| 20025                 | 根据税号没有获取到组织机构！                                                                                       |
| 30401                 | 购方单位开户行及账号（xxx），因其中含有非GBK字符集编码的字符串导致部分信息显示异常，请核对并修改后重试                                               |
| 30406                 | 当前请求流水号已存在！                                                                                          |
| 30503015              | 销方单位地址及电话最多为100个字符，50个汉字！                                                                            |
| 30504009              | 开票类型错误                                                                                               |
| 30504016              | 收款人最多为16个字符，8个汉字！                                                                                    |
| 30504018              | 复核人最多为16个字符，8个汉字                                                                                     |
| 30507001              | 合计金额不能为空！                                                                                            |
| 30507006              | 商品明细不能为空！                                                                                            |
| 30507011              | 电子票不能开具清单发票                                                                                          |
| 30507013              | 不能开具合计金额为0的发票！                                                                                       |
| 30507016              | 原发票查询失败,请确认原票是否开具成功！                                                                                 |
| 30507018              | 此税率商品红冲金额超过原票金额！                                                                                     |
| 30507024              | 合计金额大于原发票合计金额,不能开具！                                                                                  |
| 30507041              | 行号为[ xx ]的商品，开具卷式发票商品数量和单价不能为空                                                                       |
| 30507042              | 行号为[ xx ]的商品，商品数量和单价应同时为空或同时非空                                                                       |
| 30507047              | 行号为[ xx ]的商品，专票不能开具零税率发票！                                                                            |
| 30507049              | 行号为[ xx ]的商品，优惠政策标识只能为0或1！                                                                           |
| 30507050              | 行号为[ xx ]的商品，优惠政策标识为0时不能使用增值税特殊管理                                                                    |
| 30507052              | 行号为[ xx ]的商品，零税率标识不为空时，商品必须为0税率！                                                                     |
| 30507054              | 行号为[ xx ]的商品，含税标志只能为0或1！                                                                             |
| 30507055              | 行号为[ xx ]的商品，商品编码不能为空                                                                                |
| 30507056              | 行号为[ xx ]的商品，商品编码不能小于19位！                                                                            |
| 30507059              | 行号为[ xx ]的商品，自定义商品名称长度不能超过90个字符！                                                                     |
| 30507065              | 行号为[ xx ]的商品，商品编码信息不存在！                                                                              |
| 30507075              | 行号为[ 1 ]的商品，规格型号不能超过40个字符                                                                            |
| 30507080              | 行号为[ xx ]的商品，开具成品油卷式发票单位只能为升！                                                                        |
| 30507083              | 行号为[ xx ]的商品，折扣行上一行必须为被折扣行                                                                           |
| 30507085              | 行号为[ xx ]的商品，折扣行金额不能大于被折扣行！                                                                          |
| 30507087              | 行号为[ xx ]的商品，折扣行，规格型号必须为空！                                                                           |
| 30507089              | 行号为[ xx ]的商品，折扣行，商品数量必须为空！                                                                           |
| 30507097              | 合计税额不正确！                                                                                             |
| 30507101              | 商品明细不能为空！                                                                                            |
| 30507102              | 行号为[ xx ]的商品，单价*数量不等于金额！误差值为：xxx                                                                     |
| 30507103              | 行号为[ xx ]的商品，税额计算有误！误差值为：xxx                                                                         |
| 30507108              | 行号为[ xx ]的商品，使用优惠政策，增值税特殊管理不能为空！                                                                     |
| 30507109              | 行号为[ xx ]的商品，使用优惠政策，使用优惠政策，增值税特殊管理不能自定义！                                                             |
| 30507110              | 行号为[ xx ]的商品，税率不能小于0！                                                                                |
| 30507111              | 行号为[ xx ]的商品，对应的初始化商品税率不在可用税率范围内，请重新输入                                                               |
| 30507113              | 行号为[ xx ]的商品，商品单价不能为负数！                                                                              |
| 30507114              | 行号为[ xx ]的商品，商品金额必须为正数                                                                               |
| 30507115              | 行号为[ xx ]的商品，商品金额必须为负数                                                                               |
| 30507119              | 超过8行商品的发票只能开具清单发票                                                                                    |
| 30507122              | 行号为[ xx ]的商品信息中商品编码使用错误，开具非成品油发票不允许使用成品油编码                                                           |
| 30511001              | 购方名称不能为空！                                                                                            |
| 30511002              | 购方纳税人识别号不能为空                                                                                         |
| 30511003              | 购方地址电话不能为空                                                                                           |
| 30511004              | 购方开户行账号不能为空！                                                                                         |
| 30511013              | 购方客户名称最多为100个字符,50个汉字                                                                                |
| 30511019              | 购方开户行账号最多为100个字符，50个汉字                                                                               |
| 30511023              | 购方地址电话最多为100个字符，50个汉字！                                                                               |
| 30511025              | 手机号格式错误！                                                                                             |
| 30511028              | 邮箱格式错误！                                                                                              |
| 30511029              | 购方纳税人识别号为7-20位数字或字母！                                                                                 |
| 30801001              | 行号为[ xx ]的商品，税率不在核心板可用税率范围内                                                                          |
| 309040                | 发票请求流水号[xxx]对应的发票正在开具中，请15min稍后重试！                                                                   |
| 309110                | 红字信息表编号[ xx] 不符合规则，请检查后重试！                                                                           |
| 309149                | 开具增值税专用发票（机动车）,行号为[ xx ]的商品,单价、数量、单位不能为空                                                             |
| 309152                | 开具增值税专用发票（机动车）,行号为[ 1 ]的商品,商品编码只能选择机动车类税收分类编码                                                        |
| 309156                | 增值税专用发票（机动车）红字信息表编号再红字信息表中不存在                                                                        |
| 309158                | 备注不能超过xx个字符                                                                                          |
| 31506010              | 购方税号不能全部为0，可能会导致发票验签失败，请修改后重试！                                                                       |
| 32001                 | 09D103:发票已用完                                                                                         |
| 33038                 | 当前发票代码号码已存在，无法保存！                                                                                    |
| 51108                 | 调用税控系统未返回任何数据！                                                                                       |
| 52103                 | 税控盘【xx】不在线，请核实如下问题：1）云桌面/云助手是否已正常启动且配置正确；2）【税控盘】是否已正常连接电脑；3）网络状况是否畅通；确保以上问题正常无误后再进行重试，如仍存在问题，请联系管理员。 |
| 70031                 | 全电平台认证账号登录失效，业务系统接到此错误码后，可提示：请联系当前企业税务人员，请前往税务系统重新登录认证全电账号                                           |
| 70045                 | 全电平台认证账号需要刷脸认证，业务系统接到此错误码后，可提示：请联系当前企业税务人员，请前往税务系统重新登录认证全电账号                                         |
| 70046                 | 红字发票开具失败，原因：{}                                                                                       |
| 70051                 | 蓝字发票开具失败，原因：{}                                                                                       |
| 70076                 | 乐企可用授信额度不足，请先下载授信额度                                                                                  |
| 70118                 | 授信额度不在有效期内,请先申报或者调整授信额度有效期                                                                           |
| 70162                 | 开票员传值在全电账号不存在或者没有配置默认开票员！                                                                            |
| 70174                 | 调用发票池服务超时，请稍后再试                                                                                      |
| 70178                 | 销方银行账户不能为空                                                                                           |
| 70179                 | 销方银行名称不能为空                                                                                           |
| 70183                 | {73008}:{当前连接税局网络波动，请稍后再试}                                                                           |
| 70184                 | {73023}:{连接税局系统超时,请稍后再试！}                                                                            |
| 70185                 | {73036}:{保活程序运行中，请两分钟后重试，如仍无法完成，请从尝试手动重新登录}                                                          |
| 70186                 | {73037}:{发票开具超时,请稍后再试！}                                                                              |
| 70189                 | 非数电开票企业，无法开具数电发票！                                                                                    |
| 70190                 | 您有正在开具中的发票，请稍后查询确认！                                                                                  |
| 70191                 | 发票开具失败，{}                                                                                            |
| 90003                 | 当前发票号码已存在！；当前发票代码号码已存在！；待开当前请求流水号已存在！；已开当前请求流水号已存在！                                                  |
| 90008                 | 购买方纳税人识别号长度不能少于15个字符且不能超过20个字符                                                                       |
| 900159                | 来源传值有误                                                                                               |
| 900164                | 行号为[ n ]的商品，不是差额征税的商品                                                                                |
| 900165                | 行号为[ n ]的商品，差额征税不支持该发票类型                                                                             |
| 900166                | 行号为[ n ]的商品，差额征税适用税率异常                                                                               |
| 900168                | 当存在核定计税价格时，实际成交含税金额不能为空                                                                              |
| 900169                | 租赁期起止不能为空                                                                                            |
| 90017                 | 销售方开户行账号长度不能超过100个字符                                                                                 |
| 900170                | 蓝字发票不能开具合计金额小于等于0的发票                                                                                 |
| 900175                | 购方纳税人识别号最大长度20位，由数字和大写字母组成                                                                           |
| 900179                | 行号为[ n ]的商品，单价不能小于0                                                                                  |
| 900180                | 数电纸票暂不支持生成版式文件                                                                                       |
| 900183                | 土地增值税项目编号不能超过16个字符                                                                                   |
| 900186                | 行号为[ n ]的商品，商品单价过低，此张发票可能被判定为【成品油异常发票】，是否继续开具?                                                       |
| 900189                | 行号为[ n ]的商品，存在成品油商品编码，请检查                                                                            |
| 900193                | 行号为[ n ]的商品，计量单位错误，只能为 “吨” 或 “升”                                                                     |
| 900195                | 行号为[ n ]的商品，数量、单价不能为空                                                                                |
| 90033                 | 行号为[ N ]的商品，规格型号长度不能超过150个字符                                                                         |
| 90034                 | 行号为[ N ]的商品，单位长度不能超过300个字符                                                                           |
| 90046                 | 行号为[ n ]的商品，单价*数量不等于金额                                                                               |
| 90047                 | 行号为[ n ]的商品，税额计算有误                                                                                   |
| 90048                 | 发票合计金额与合计税额之和，必须与价税合计一致！                                                                             |
| 90051                 | 行号为[ n ]的商品，商品编码为非末级节点，无法进行开票                                                                        |
| 90052                 | 行号为[ n ]的商品，税率不能为空                                                                                   |
| 90053                 | 开具中的发票不允许重新开具                                                                                        |
| 90055                 | 行号为[ n ]的商品，开具电子发票(增值税专用发票)税率不能为0                                                                    |
| 90056                 | 行号为[ n ]的商品，明细不含税金额不能为空                                                                              |
| 90059                 | 行号为[ n ]的商品，商品编码不能为空                                                                                 |
| 90065                 | 开具电子发票(增值税专用发票)购方税号不能为空                                                                              |
| 90082                 | 手机号格式有误                                                                                              |
| 90083                 | 邮箱格式有误                                                                                               |
| 90085                 | 红蓝票购方信息[ n ]不一致！                                                                                     |
| 90095                 | 缺少特定业务数据                                                                                             |
| 90102                 | 建筑项目名称不能超过80个字符                                                                                      |
| 90103                 | 运输工具种类不能为空                                                                                           |
| 90106                 | 运输工具牌号不能超过40个字符                                                                                      |
| 90110                 | 到达地不能超过80个字符                                                                                         |
| 90111                 | 运输货物名称不能为空                                                                                           |
| 90114                 | 不动产地址不能超过120个字符                                                                                      |
| 90115                 | 房屋产权证书（不动产权证号）不能为空                                                                                   |
| 90116                 | 房屋产权证书（不动产权证号）不能超过64个字符                                                                              |
| 90119                 | 销售方电话长度不能超过60个字符                                                                                     |
| 90145                 | 原数电发票号码不能为空                                                                                          |
| 990001                | 近10分钟内，系统记录存在相同的已开发票数据[ n ]条                                                                         |
| 990002                | 近10分钟内，系统记录存在相同的待开发票数据[ n ]条                                                                         |
| 10144                 | 根据第[ n ]行商品编码:xxxxxx,未查到相关商品信息！                                                                      |
| 10053                 | 增专票特殊票种标识不能为空或内容不正确                                                                                  |
| F42008                | 未查询到蓝字发票信息，不允许开具红字发票                                                                                 |
| 10170                 | 查询的编码表版本号为空                                                                                          |
| 31724                 | 当前红字信息状态为查证未通过，不允许被使用                                                                                |
| 400                   | 服务未知错误，请提供requestId联系管理员定位问题！                                                                        |
| 920000                | 被折扣行缺少对应折扣行，请检查！                                                                                     |
| 90049                 | 发票明细行不含税金额、税额的合计与发票合计金额、合计税额不一致！                                                                     |
| 70032                 | 业务请求处理超时，请稍后再试                                                                                       |
| 70192                 | 未查询到待开数据                                                                                             |
| 8889                  | 发票明细行行号不可以为0；发票明细行行号不连续                                                                              |
| 70085                 | 乐企查询基本信息失败:{}                                                                                        |
| 90021                 | 备注长度不能超过230个字符                                                                                       |
| 90144                 | 红字确认单UUID不能为空；差额征税扣除额必须小于等于价税合计                                                                      |
| 90146                 | 行号为[ n ]的商品，对应原蓝字发票明细行号不能为空                                                                          |
| 70052                 | 查询基础信息失败，原因：{}                                                                                       |
| 900173                | 不动产租赁发票明细行为正常行时，只能有一行发票明细；乐企开具数电负数发票时红字确认单编号及红字UUID不能为空                                              |
| 90012                 | 购买方地址长度不能超过100个字符                                                                                    |
| 900163                | 差额征税时，只允许开具一条明细；                                                                                     |
| 90009                 | 购买方名称长度不能超过100个字符                                                                                    |
| 90011                 | 购买方开户行账号长度不能超过100个字符                                                                                 |
| BASIC.COMMON.E_SYSTEM | 系统内部异常，请稍后再试！                                                                                        |
| 20199                 | 自动补全关闭时，【购方名称】字段不能为空!!!；商品明细的行号goodsLineNo不可为空且不能相同！                                                 |
| 20166                 | 查询发票信息失败！；征收方式为差额征税，差额征税标签不能为空                                                                       |
| 70177                 | 刷新待开数据异常({})，请稍后再试                                                                                   |
| 90058                 | 行号为[ n ]的商品，商品名称不能为空                                                                                 |
| 90039                 | 差额征税标签传值有误                                                                                           |
| 901002                | 建筑服务明细行只支持单行                                                                                         |
| 30050                 | 特殊票种为非成品油，不能填写成品油的商品编码！                                                                              |
| 900171                | 红字发票不能开具合计金额于等于0的发票；蓝字发票信息中不应该包含红字发票相关参数                                                             |
| 900160                | 差额征税-差额开票时，差额凭证数据不能为空                                                                                |
| 70073                 | 乐企授信额度未初始化                                                                                           |
| 70194                 | （数电发票号码）                                                                                             |
| 90031                 | 红字确认单uuid长度不能超过32个字符                                                                                 |
| 70050                 | {73028}:{请求税局返回结果出错，原因：您当前无可用发票代码号码,请联系主管税务机关申领}                                                     |
| 30034                 | 纸质发票开具时，纸质发票类型代码传值与实际不符                                                                              |
| 30058                 | 开具自然人发票时，不能开具专票！；租赁起/止日期格式错误，请按照文档中的日期格式输入！                                                          |
| 30021                 | 暂不支持此类特定业务！                                                                                          |
| 90019                 | 销售方电话长度不能超过60个字符                                                                                     |
| -999                  | 旅客运输纸票，特定业务信息不允许超过1行；旅客运输只能有一行商品；行号为: （）特定要素校验不通过，错误信息：（）；商品明细不能为空                                   |
| 70116                 | 乐企赋码时没有可用赋码段，请手动申领赋码段或稍后重试                                                                           |
| 20027                 | 根据租户id和组织代码没有获取到组织机构！                                                                                |
| 90010                 | 购买方开户行名称长度不能超过100个字符                                                                                 |
| 921001                | 购销方不能为同一家企业                                                                                          |
| 20178                 | 差额票开具时，差额征税扣除额必填且必须大于0！                                                                              |
| 20177                 | 差额征税扣除额凭证明细行号不能为空且不能重复！                                                                              |
| 20176                 | 差额征税-差额开票时，第N行，差额扣除额信息填写有误：凭证类型为“其他发票、其他扣除凭证”时，必须录入凭证合计金额、本次扣除金额、备注！                                 |
| 70001                 | 系统异常，请稍后再试，原因:{}                                                                                     |
| 901101                | 行号为[ n ]的商品，含税单价*数量不等于价税合计                                                                           |
| 99999                 | 重复请求                                                                                                 |
| 90084                 | 抄送人数量或格式有误请检查                                                                                        |
| 900181                | 行号为[ n ]的发票行性质为折扣行，单价或数量不能有值，请核对后再试                                                                  |
| 30075                 | 发票明细不能为空！                                                                                            |
| 20170                 | 差额征税-差额开票时，差额扣除额明细信息不能为空                                                                             |
| 70036                 | 红字录入单查询异常，{}                                                                                         |
| 90112                 | 运输货物名称不能超过80个字符                                                                                      |
| 20241                 | 农产品销售票种，商品编码以101开头的商品不能与非101开头的商品混开                                                                  |
| 90029                 | 合同编号长度不能超过50个字符                                                                                      |
| 90072                 | 未查询到对应的发票信息                                                                                          |
| 70244                 | 发票开具请求流水号已存在                                                                                         |
| 70255                 | 票池调用失败,原因:{}                                                                                         |
| 70222                 | 成品油库存不足,请下载成品油库存                                                                                     |
| 70243                 | 同一红字确认单红票开具请求太频繁,请稍后重试                                                                               |
| 90036                 | 发票种类传值有误                                                                                             |
| 70169                 | 参数{}不能为空                                                                                             |
| 70242                 | 该红字确认单已开具,请勿重复开具,红票号码【{}】                                                                            |
| 900202                | 差额开票汇总金额应满足：                                                                                         |
| 90136                 | 交通工具类型为飞机、火车、船舶时，等级不能为空                                                                              |
| 900167                | 凭证合计金额必须大于等于本次扣除金额                                                                                   |
| 900199                | 差额开票,蓝字发票税额应满足：                                                                                      |
| 70245                 | 机动车开具失败,原因:{}                                                                                        |
| 20173                 | 差额征税-差额开票时，第【%s】行，差额扣除额信息填写有误：凭证类型为“数电发票”时，必须录入数电发票号码、开票日期、凭证合计金额、本次扣除金额！                            |
| 90050                 | 发票合计金额与合计税额之和，必须与价税合计一致！                                                                             |
| 900148                | 征收方式传值有误；减按征税标识传值有误                                                                                  |
| 90090                 | 红蓝票的发票行明细数量不一致！；当前租户下附加要素分类名称重复                                                                      |
| 70270                 | 同一流水号发票开具请求太频繁,请稍后重试                                                                                 |
| 70254                 | 刷新额度失败，有效期更新失败。原因为:{}                                                                                |
| 70274                 | 存在相同正在开具中的开票流水号【{}】，请勿重复请求                                                                           |
| 70269                 | 报废品回收开具失败,原因:{}                                                                                      |
| 30507120              | 发票明细行号不能超过4位                                                                                         |
| 70267                 | 稀土特定业务开具失败                                                                                           |
| 70165                 | 纳税人状态信息异常                                                                                            |
| 70303                 | 红字确认单状态异常,无法开票                                                                                       |
| 901007                | 车辆类型不能为空                                                                                             |
| 901007                | 跨地市标志为是时，跨区域涉税事项报验管理编号必填                                                                             |
| 90042                 | 开票类型传值有误                                                                                             |
| 70281                 | 乐企开票人不能为空                                                                                            |
| 70281                 | 授信额度下载配置为空                                                                                           |
| 30037                 | 当前蓝票被业务限制，进行发票红冲操作，请确认当前业务情况！                                                                        |
| 30022                 | 含税标识错误请检查                                                                                            |
| 900172                | "不动产单元代码长度不能超过28位"或"网签合同备案编号长度不能超过28位"或"不动产单元代码与网签合同备案编号不能同时有值"或"商品税率不能小于0"                          |
| 10611                 | 农产品收购票种不支持开具专票                                                                                       |
| 70252                 | 请求流水号对应的发票明细发生变化，请更换流水号后重新发起请求！                                                                      |

## 六、请求示例

json格式示例

```json
POST  http://ip:port/post?method=baiwang.output.invoice.issue&version=1.0&requestId=c789a7c2-967f-4a79-bc61-3e7ef4daab11

{
"isSplit": false,
"isReturnRedInfo": "1",
"completionCustom": "",
"data": {
"discountRate": 0,
"redConfirmUuid": "",
"buyerBankAccount": "中国工商银行海淀分行 *****************",
"buyerEmail":"",
"invoiceTotalTax": 1.3,
"buyerNaturalPerson": "N",
"discountAmount": 0,
"credentialsType": "",
"sellerAddress":"",
"checker": "李佳佳",
"taxationMethod": "0",
"redInfoNo": "",
"payee": "任盈盈",
"buyerCredentialsType":"",
"reducedTaxCode": "",
"buyerAddress": "",
"taxationLabel": "01",
"systemName": "",
"invoiceType":"0",
"redIssueReason": "",
"displayBuyerAddTel": false,
"emailCarbonCopy": "",
"ext": {},
"orderNo": "",
"buyerTaxNo": "91500000747150890S",
"taxReason": "",
"isConfirmIssue": "",
"contractNumber": "",
"originalInvoiceNo": "",
"invoiceListMark": "0",
"serialNo": "T150000431379",
"voucherNo": "",
"displaySeller": false,
"buyerPhone": "",
"confirmIssue": "1",
"invoiceTotalPrice": 10,
"userAccount": "",
"buyerCredentialsNo": "",
"invoiceDetailsList":[
{
"ext": {},
"goodsTaxRate": 0.13,
"invoiceLineNature": "0",
"goodsTotalPrice": 10,
"preferentialMarkFlag":"0",
"goodsPersonalCode": "",
"originalInvoiceDetailNo": "",
"goodsSpecification": "",
"goodsPrice": 1,
"freeTaxMark":"",
"goodsDiscountAmount": 0,
"goodsQuantity": 10,
"goodsUnit": "",
"goodsTotalTax": 1.3,
"goodsCode":"1010101070000000000",
"goodsName": "燕麦",
"goodsLineNo": 1,
"vatSpecialManagement": ""
}
],
"sellerBankName": "",
"displayBuyer": false,
"drawerCredentialsType": "",
"originalInvoiceCode": "",
"mulPurchaserMark": "",
"sellerTelphone": "",
"paperInvoiceTypeCode": "",
"priceTaxMark": "0",
"buyerTelphone": "",
"paperInvoiceFlag": "N",
"invoiceBalanceinfoList":[
{
"balanceVoucherNo": "",
"balanceType": "01",
"balanceDeductAmount": 10,
"balanceNo": "1",
"balanceIssueDate": "2023-01-22",
"balanceInvoiceNo": "",
"balanceTotalAmount": 20.0,
"balanceSource": "0",
"balanceElectricNo": "",
"balanceInvoiceCode": "",
"balanceRemarks": "备注"
}
],
"displaySellerAddTel": false,
"mainGoodsName": "",
"drawerCredentialsNo": "",
"sellerAddressPhone": "",
"buyerNationalityRegion": "",
"mulPurchaserList":[
{
"purchaser": "",
"certificateNo": "",
"certificateType": ""
}
],
"buyerBankName": "",
"discountType": "",
"buyerAddressPhone": "北京市海淀区 010-********",
"systemId": "",
"deductibleAmount": 0,
"drawer": "张一诺",
"invoiceSpecialMark": "00",
"buyerName": "百望测试40",
"additionalDetails":[
{
"fieldKey": "",
"fieldTitle": "",
"fieldType": "",
"fieldValue": ""
}
],
"invoiceTotalPriceTax": 11.3,
"accessPlatformNo": "",
"sellerBankNumber": "",
"buyerBankNumber": "",
"sellerBankAccount": "",
"invoiceTypeCode": "026",
"invoiceSpecialInfoList":[
{
"leaseHoldDateEnd": "",
"propertyContractNo": "",
"buildingLocalAddress": "",
"policyNumber": "",
"buildingCrossSign": "",
"carriageDateYmd": "",
"tractorVehicleNo": "",
"propertyCrossSign": "",
"cph": "",
"carriageId": "",
"carriageVehicleGrade": "",
"propertyAddress": "",
"tractorRegistry": "",
"leaseAreaUnit": "",
"carriageName": "",
"vehicleCode": "",
"propertyDetailAddress": "",
"tractorEngineNo": "",
"transportGoodsName": "",
"leasePropertyNo": "",
"buildingName": "",
"totalAmount": 0,
"leaseDetailAddress": "",
"transportToolType": "",
"propertyAreaUnit": "",
"usedCarNo": "",
"usedCarCode": "",
"taxPeriod": "",
"kqysssxbgglBm": "",
"carriageLeave": "",
"carriageVehicleType": "",
"propertyApprovedPrice": 0,
"lateFeeAmount": 0,
"carriageArrive": "",
"buildingDetailAddress": "",
"carriageLeaveAddress": "",
"propertyLandTaxNo": "",
"transportArrive": "",
"propertyDealPrice": 0,
"usedCarAllelectricNo": "",
"leaseAddress": "",
"propertyContractOnlineSigningNo": "",
"carriageIdNo": "",
"leaseCrossSign": "",
"transportDeparture": "",
"buildingLandTaxNo": "",
"propertyPropertyNo": "",
"registrationNumber": "",
"transportToolNum": "",
"taxCollectedAmount": 0,
"carriageArriveAddress": "",
"leaseHoldDateStart": ""
}
],
"remarks": ""
},
"orgCode": "",
"isAsync": "0",
"invoiceTerminalCode": "dzpzd008",
"taxNo":"512345678900000040",
"formatPushType": false,
"taxDiskNo": "",
"formatGenerate": false,
"taxUserName": "cccccccb"
}
```

sdk调用示例

```java
String url = "http://ip:port/post";       //接口地址
try
        {
OutputInvoiceIssueRequest request = new OutputInvoiceIssueRequest();
    request.

setIsSplit(false);
    request.

setIsReturnRedInfo("1");
    request.

setCompletionCustom("");

OutputInvoiceIssuePreInvoice data = new OutputInvoiceIssuePreInvoice();
    data.

setDiscountRate(0);
    data.

setRedConfirmUuid("");
    data.

setBuyerBankAccount("\u4E2D\u56FD\u5DE5\u5546\u94F6\u884C\u6D77\u6DC0\u5206\u884C *****************");
    data.

setBuyerEmail("");
    data.

setInvoiceTotalTax(BigDecimal.valueOf(1.3));
        data.

setBuyerNaturalPerson("N");
    data.

setDiscountAmount(BigDecimal.valueOf(0.0));
        data.

setCredentialsType("");
    data.

setSellerAddress("");
    data.

setChecker("\u674E\u4F73\u4F73");
    data.

setTaxationMethod("0");
    data.

setRedInfoNo("");
    data.

setPayee("\u4EFB\u76C8\u76C8");
    data.

setBuyerCredentialsType("");
    data.

setReducedTaxCode("");
    data.

setBuyerAddress("");
    data.

setTaxationLabel("01");
    data.

setSystemName("");
    data.

setInvoiceType("0");
    data.

setRedIssueReason("");
    data.

setDisplayBuyerAddTel(false);
    data.

setEmailCarbonCopy("");

Map<String, Object> ext = new HashMap<String, Object>();
    data.

setExt(ext);
    data.

setOrderNo("");
    data.

setBuyerTaxNo("91500000747150890S");
    data.

setTaxReason("");
    data.

setIsConfirmIssue("");
    data.

setContractNumber("");
    data.

setOriginalInvoiceNo("");
    data.

setInvoiceListMark("0");
    data.

setSerialNo("T150000431379");
    data.

setVoucherNo("");
    data.

setDisplaySeller(false);
    data.

setBuyerPhone("");
    data.

setConfirmIssue("1");
    data.

setInvoiceTotalPrice(BigDecimal.valueOf(10.0));
        data.

setUserAccount("");
    data.

setBuyerCredentialsNo("");

List<OutputInvoiceIssueInvoiceDetail> invoiceDetailsList = new ArrayList<OutputInvoiceIssueInvoiceDetail>();
OutputInvoiceIssueInvoiceDetail outputInvoiceIssueInvoiceDetail = new OutputInvoiceIssueInvoiceDetail();
Map<String, Object> ext = new HashMap<String, Object>();
    outputInvoiceIssueInvoiceDetail.

setExt(ext);
    outputInvoiceIssueInvoiceDetail.

setGoodsTaxRate(BigDecimal.valueOf(0.13));
        outputInvoiceIssueInvoiceDetail.

setInvoiceLineNature("0");
    outputInvoiceIssueInvoiceDetail.

setGoodsTotalPrice(BigDecimal.valueOf(10.0));
        outputInvoiceIssueInvoiceDetail.

setPreferentialMarkFlag("0");
    outputInvoiceIssueInvoiceDetail.

setGoodsPersonalCode("");
    outputInvoiceIssueInvoiceDetail.

setOriginalInvoiceDetailNo("");
    outputInvoiceIssueInvoiceDetail.

setGoodsSpecification("");
    outputInvoiceIssueInvoiceDetail.

setGoodsPrice(BigDecimal.valueOf(1.0));
        outputInvoiceIssueInvoiceDetail.

setFreeTaxMark("");
    outputInvoiceIssueInvoiceDetail.

setGoodsDiscountAmount(BigDecimal.valueOf(0.0));
        outputInvoiceIssueInvoiceDetail.

setGoodsQuantity(BigDecimal.valueOf(10.0));
        outputInvoiceIssueInvoiceDetail.

setGoodsUnit("");
    outputInvoiceIssueInvoiceDetail.

setGoodsTotalTax(BigDecimal.valueOf(1.3));
        outputInvoiceIssueInvoiceDetail.

setGoodsCode("1010101070000000000");
    outputInvoiceIssueInvoiceDetail.

setGoodsName("\u71D5\u9EA6");
    outputInvoiceIssueInvoiceDetail.

setGoodsLineNo(1);
    outputInvoiceIssueInvoiceDetail.

setVatSpecialManagement("");
    invoiceDetailsList.

add(outputInvoiceIssueInvoiceDetail);
    data.

setInvoiceDetailsList(invoiceDetailsList);
    data.

setSellerBankName("");
    data.

setDisplayBuyer(false);
    data.

setDrawerCredentialsType("");
    data.

setOriginalInvoiceCode("");
    data.

setMulPurchaserMark("");
    data.

setSellerTelphone("");
    data.

setPaperInvoiceTypeCode("");
    data.

setPriceTaxMark("0");
    data.

setBuyerTelphone("");
    data.

setPaperInvoiceFlag("N");

List<OutputInvoiceIssueInvoiceBalanceinfo> invoiceBalanceinfoList = new ArrayList<OutputInvoiceIssueInvoiceBalanceinfo>();
OutputInvoiceIssueInvoiceBalanceinfo outputInvoiceIssueInvoiceBalanceinfo = new OutputInvoiceIssueInvoiceBalanceinfo();
    outputInvoiceIssueInvoiceBalanceinfo.

setBalanceVoucherNo("");
    outputInvoiceIssueInvoiceBalanceinfo.

setBalanceType("01");
    outputInvoiceIssueInvoiceBalanceinfo.

setBalanceDeductAmount(BigDecimal.valueOf(10.0));
        outputInvoiceIssueInvoiceBalanceinfo.

setBalanceNo("1");
    outputInvoiceIssueInvoiceBalanceinfo.

setBalanceIssueDate("2023-01-22");
    outputInvoiceIssueInvoiceBalanceinfo.

setBalanceInvoiceNo("");
    outputInvoiceIssueInvoiceBalanceinfo.

setBalanceTotalAmount(BigDecimal.valueOf(20.0));
        outputInvoiceIssueInvoiceBalanceinfo.

setBalanceSource("0");
    outputInvoiceIssueInvoiceBalanceinfo.

setBalanceElectricNo("");
    outputInvoiceIssueInvoiceBalanceinfo.

setBalanceInvoiceCode("");
    outputInvoiceIssueInvoiceBalanceinfo.

setBalanceRemarks("\u5907\u6CE8");
    invoiceBalanceinfoList.

add(outputInvoiceIssueInvoiceBalanceinfo);
    data.

setInvoiceBalanceinfoList(invoiceBalanceinfoList);
    data.

setDisplaySellerAddTel(false);
    data.

setMainGoodsName("");
    data.

setDrawerCredentialsNo("");
    data.

setSellerAddressPhone("");
    data.

setBuyerNationalityRegion("");

List<OutputInvoiceIssueCoPurchaseInfo> mulPurchaserList = new ArrayList<OutputInvoiceIssueCoPurchaseInfo>();
OutputInvoiceIssueCoPurchaseInfo outputInvoiceIssueCoPurchaseInfo = new OutputInvoiceIssueCoPurchaseInfo();
    outputInvoiceIssueCoPurchaseInfo.

setPurchaser("");
    outputInvoiceIssueCoPurchaseInfo.

setCertificateNo("");
    outputInvoiceIssueCoPurchaseInfo.

setCertificateType("");
    mulPurchaserList.

add(outputInvoiceIssueCoPurchaseInfo);
    data.

setMulPurchaserList(mulPurchaserList);
    data.

setBuyerBankName("");
    data.

setDiscountType("");
    data.

setBuyerAddressPhone("\u5317\u4EAC\u5E02\u6D77\u6DC0\u533A 010-********");
    data.

setSystemId("");
    data.

setDeductibleAmount(BigDecimal.valueOf(0.0));
        data.

setDrawer("\u5F20\u4E00\u8BFA");
    data.

setInvoiceSpecialMark("00");
    data.

setBuyerName("\u767E\u671B\u6D4B\u8BD540");

List<OutputInvoiceIssueAdditionalDetail> additionalDetails = new ArrayList<OutputInvoiceIssueAdditionalDetail>();
OutputInvoiceIssueAdditionalDetail outputInvoiceIssueAdditionalDetail = new OutputInvoiceIssueAdditionalDetail();
    outputInvoiceIssueAdditionalDetail.

setFieldKey("");
    outputInvoiceIssueAdditionalDetail.

setFieldTitle("");
    outputInvoiceIssueAdditionalDetail.

setFieldType("");
    outputInvoiceIssueAdditionalDetail.

setFieldValue("");
    additionalDetails.

add(outputInvoiceIssueAdditionalDetail);
    data.

setAdditionalDetails(additionalDetails);
    data.

setInvoiceTotalPriceTax(BigDecimal.valueOf(11.3));
        data.

setAccessPlatformNo("");
    data.

setSellerBankNumber("");
    data.

setBuyerBankNumber("");
    data.

setSellerBankAccount("");
    data.

setInvoiceTypeCode("026");

List<OutputInvoiceIssueInvoiceSpecialInfo> invoiceSpecialInfoList = new ArrayList<OutputInvoiceIssueInvoiceSpecialInfo>();
OutputInvoiceIssueInvoiceSpecialInfo outputInvoiceIssueInvoiceSpecialInfo = new OutputInvoiceIssueInvoiceSpecialInfo();
    outputInvoiceIssueInvoiceSpecialInfo.

setLeaseHoldDateEnd("");
    outputInvoiceIssueInvoiceSpecialInfo.

setPropertyContractNo("");
    outputInvoiceIssueInvoiceSpecialInfo.

setBuildingLocalAddress("");
    outputInvoiceIssueInvoiceSpecialInfo.

setPolicyNumber("");
    outputInvoiceIssueInvoiceSpecialInfo.

setBuildingCrossSign("");
    outputInvoiceIssueInvoiceSpecialInfo.

setCarriageDateYmd("");
    outputInvoiceIssueInvoiceSpecialInfo.

setTractorVehicleNo("");
    outputInvoiceIssueInvoiceSpecialInfo.

setPropertyCrossSign("");
    outputInvoiceIssueInvoiceSpecialInfo.

setTransport_tool_num("");
    outputInvoiceIssueInvoiceSpecialInfo.

setCph("");
    outputInvoiceIssueInvoiceSpecialInfo.

setCarriageId("");
    outputInvoiceIssueInvoiceSpecialInfo.

setCarriageVehicleGrade("");
    outputInvoiceIssueInvoiceSpecialInfo.

setPropertyAddress("");
    outputInvoiceIssueInvoiceSpecialInfo.

setTractorRegistry("");
    outputInvoiceIssueInvoiceSpecialInfo.

setLeaseAreaUnit("");
    outputInvoiceIssueInvoiceSpecialInfo.

setCarriageName("");
    outputInvoiceIssueInvoiceSpecialInfo.

setVehicleCode("");
    outputInvoiceIssueInvoiceSpecialInfo.

setPropertyDetailAddress("");
    outputInvoiceIssueInvoiceSpecialInfo.

setTractorEngineNo("");
    outputInvoiceIssueInvoiceSpecialInfo.

setLeasePropertyNo("");
    outputInvoiceIssueInvoiceSpecialInfo.

setBuildingName("");
    outputInvoiceIssueInvoiceSpecialInfo.

setTotalAmount(BigDecimal.valueOf(0.0));
        outputInvoiceIssueInvoiceSpecialInfo.

setTransport_departure("");
    outputInvoiceIssueInvoiceSpecialInfo.

setLeaseDetailAddress("");
    outputInvoiceIssueInvoiceSpecialInfo.

setPropertyAreaUnit("");
    outputInvoiceIssueInvoiceSpecialInfo.

setTransport_tool_type("");
    outputInvoiceIssueInvoiceSpecialInfo.

setUsedCarNo("");
    outputInvoiceIssueInvoiceSpecialInfo.

setUsedCarCode("");
    outputInvoiceIssueInvoiceSpecialInfo.

setTaxPeriod("");
    outputInvoiceIssueInvoiceSpecialInfo.

setKqysssxbgglBm("");
    outputInvoiceIssueInvoiceSpecialInfo.

setCarriageLeave("");
    outputInvoiceIssueInvoiceSpecialInfo.

setCarriageVehicleType("");
    outputInvoiceIssueInvoiceSpecialInfo.

setPropertyApprovedPrice(BigDecimal.valueOf(0.0));
        outputInvoiceIssueInvoiceSpecialInfo.

setLateFeeAmount(BigDecimal.valueOf(0.0));
        outputInvoiceIssueInvoiceSpecialInfo.

setCarriageArrive("");
    outputInvoiceIssueInvoiceSpecialInfo.

setBuildingDetailAddress("");
    outputInvoiceIssueInvoiceSpecialInfo.

setCarriageLeaveAddress("");
    outputInvoiceIssueInvoiceSpecialInfo.

setPropertyLandTaxNo("");
    outputInvoiceIssueInvoiceSpecialInfo.

setPropertyDealPrice(BigDecimal.valueOf(0.0));
        outputInvoiceIssueInvoiceSpecialInfo.

setUsedCarAllelectricNo("");
    outputInvoiceIssueInvoiceSpecialInfo.

setLeaseAddress("");
    outputInvoiceIssueInvoiceSpecialInfo.

setPropertyContractOnlineSigningNo("");
    outputInvoiceIssueInvoiceSpecialInfo.

setCarriageIdNo("");
    outputInvoiceIssueInvoiceSpecialInfo.

setTransport_arrive("");
    outputInvoiceIssueInvoiceSpecialInfo.

setLeaseCrossSign("");
    outputInvoiceIssueInvoiceSpecialInfo.

setBuildingLandTaxNo("");
    outputInvoiceIssueInvoiceSpecialInfo.

setPropertyPropertyNo("");
    outputInvoiceIssueInvoiceSpecialInfo.

setRegistrationNumber("");
    outputInvoiceIssueInvoiceSpecialInfo.

setTaxCollectedAmount(BigDecimal.valueOf(0.0));
        outputInvoiceIssueInvoiceSpecialInfo.

setCarriageArriveAddress("");
    outputInvoiceIssueInvoiceSpecialInfo.

setLeaseHoldDateStart("");
    outputInvoiceIssueInvoiceSpecialInfo.

setTransport_goods_name("");
    invoiceSpecialInfoList.

add(outputInvoiceIssueInvoiceSpecialInfo);
    data.

setInvoiceSpecialInfoList(invoiceSpecialInfoList);
    data.

setRemarks("");
    request.

setData(data);
    request.

setOrgCode("");
    request.

setIsAsync("0");
    request.

setInvoiceTerminalCode("dzpzd008");
    request.

setTaxNo("512345678900000040");
    request.

setFormatPushType(false);
    request.

setTaxDiskNo("");
    request.

setFormatGenerate(false);
    request.

setTaxUserName("cccccccb");

IBWClient client = new BWRestClient(url); // 初始化一个客户端
OutputInvoiceIssueResponse response = client.outputInvoice().issue(request);
    if(response.

isSuccess()){
        System.out.

println(response.getModel());
        }else{
        System.out.

println(response.getMessage().

getErrorMessage());
        System.out.

println(response.getMessage().

getErrorCode());
        }
        }
        catch(
ISPException e){
        e.

printStackTrace();
}
```

## 七、响应示例

成功返回示例

```json
{
  "success": true,
  "requestId": "5c2bc8a3-1399-4ff0-a039-92688c6740e0",
  "model": {
    "fail": [
      {
        "invoiceTotalPrice": 0,
        "invoiceTotalTax": 0,
        "invoiceDetailsList": [
          {
            "priceTaxMark": "",
            "goodsTaxRate": 0,
            "invoiceLineNature": "",
            "goodsTotalPrice": 0,
            "goodsSpecification": "",
            "goodsPrice": 0,
            "freeTaxMark": "",
            "goodsQuantity": "",
            "goodsUnit": "",
            "goodsTotalTax": 0,
            "goodsCode": "",
            "preferentialMark": "",
            "goodsName": "",
            "goodsLineNo": "",
            "vatSpecialManagement": ""
          }
        ],
        "invoiceTotalPriceTax": 0,
        "serialNo": ""
      }
    ],
    "success": [
      {
        "paperInvoiceCode": "",
        "eInvoiceUrl": "",
        "invoiceTotalTax": 0,
        "invoiceDate": "20190612110126",
        "invoiceCode": "015123456789",
        "invoiceTotalPriceTax": 0,
        "serialNo": "",
        "mulPurchaserList": [
          {
            "purchaser": "",
            "certificateNo": "",
            "certificateType": ""
          }
        ],
        "taxControlCode": "037-+9<56<>-0570/<+583/*9920545/4887/996/*96*384*/<--*12>>400*7<0/28>873+*68296*74>3723>*********<0706367>1<612-",
        "invoiceCheckCode": "08566901911295514678",
        "invoiceQrCode": "iVBORw0KGgoAAAANSUhEUgAAAJQAAACUCAIAAAD6XpeDAAADCUlEQVR42u3cy24CMQwF0Pn/n6bbbiqVsa+dgZMlAprkBI0fUa8rMF6/xl+vV97z7t/qmn9lLddTBjx48OB9Cd6rMCqT7oKsrHFyH7q+Hx48ePDg9eB1bXrXYioY6Tmkgyl48ODBg/cMvApAV3A0eSAmAzp48ODBg/eMgKUSXCSCncnkGh48ePDgnYXXNbl0AzYxTihKHNFVgAcPHrwPxpu8gPTNrx89IMGDB++D8V6Do2s+k+s9eW/hwYMHD979uaUTw66GajrhTRSdEwVrePDgwYP33p6ni8WnJfVdB7QrECt9Fh48ePDg3cabXPwJAdQkWGS98ODBgwevfb2J5DRxESi9rsQFrXhTFx48ePDgRRPbrWZs4kQnDlZbQxgePHjw4N3O87oewl2bsrYRg8X0tkIBPHjw4MFrx0ssLB2wJA5oIvEvFSXgwYMHD95Rhel0I3Tr8J1QTIAHDx48eP25XbqYm7jkc9pFpjZ4ePDgwYPXnrCnX09/duuyU2IO8ODBgwfvPl7X5BLJbCLoSBcZEgX0K32y4MGDB+8L8bowRpuNgeLAVuL/9nzgwYMHD15HPzLyz60Tm5UOXiqF+8SPAR48ePDgPSNgOblJmz7QiUY3PHjw4MG7j5cu1CYe/omAKF3IPu6iETx48ODBWw8o0k3gBHDX5ajIRsCDBw8evHaA9HcmAqutYKQECQ8ePHjwog3YtYfzUsG6st5EEfxfjVl48ODBg3f7WT4ZFFQamOn3JA5Wej7w4MGDB28uz0t/dvI9iYJ4248EHjx48ODdxmuLdgrJ71YCnihGxwNDePDgwYP3VjE6MRLY6abu5GEt7QM8ePDgweveh7YHb+Ihv3U4ug7KddqABw8evC/B6wpMuh7IicJ3ImBJFKZLzVh48ODBg9fSjE0kwluBQ6KRmw76/lWkhgcPHjx463iVzZoMrNIXpSIHGh48ePDgreMl3rO1QVuFAnjw4MGD94zC9GRwccKGds3niGYsPHjw4H0w3tYFpMngJZ2kJ34Yo6jw4MGD97l4PxCtu/dLsRmSAAAAAElFTkSuQmCC",
        "invoiceTotalPrice": 3.0,
        "invoiceDetailsList": [
          {
            "priceTaxMark": "",
            "goodsTaxRate": 0,
            "goodsSpecification": "",
            "goodsPrice": 0,
            "goodsQuantity": 0,
            "goodsUnit": "",
            "goodsTotalTax": 0,
            "goodsCode": "",
            "goodsName": "",
            "goodsLineNo": 0,
            "goodsTotalPrice": 0
          }
        ],
        "invoiceTypeCode": "007",
        "paperInvoiceNo": "",
        "invoiceNo": "00900554",
        "mulPurchaserMark": ""
      }
    ]
  }
}
```

失败返回示例

```json
{
  "requestId": "c789a7c2-967f-4a79-bc61-3e7ef4daab11",
  "success": false,
  "message": {
    "errorCode": "-1",
    "errorMessage": "No instances available for 10.100.3.2",
    "innerRequestId": ""
  }
}
```
