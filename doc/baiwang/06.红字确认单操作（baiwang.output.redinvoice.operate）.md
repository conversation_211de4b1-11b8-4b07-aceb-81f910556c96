# *baiwang.output.redinvoice.operate*

*红字确认单操作*

## 一、请求公共参数

| 名称        | 类型     | 是否必须 | 描述         |
|-----------|--------|------|------------|
| method    | String | 是    | 接口指定唯一标识   |
| requestId | String | 是    | 请求唯一标识     |
| version   | String | 是    | 版本号，当前为1.0 |

## 二、请求业务参数

| ID             | 类型     | 长度 | 是否必须 | 描述                     |
|:---------------|:-------|:---|:-----|:-----------------------|
| taxNo          | String | 20 | 是    | 机构税号                   |
| taxUserName    | String | 20 | 否    | 电子税局登录账号               |
| sellerTaxNo    | String | 20 | 是    | 统一社会信用代码/纳税人识别号/身份证件号码 |
| redConfirmUuid | String | 32 | 是    | 红字确认单 UUID             |
| redConfirmNo   | String | 20 | 是    | 红字确认单编号                |
| confirmType    | String | 2  | 是    | 确认类型 01：确认，02：否认；03：撤销 |

备注：用 └ 标识层级关系

## 三、响应公共参数

| 名称               | 类型      | 描述                                 |
|------------------|---------|------------------------------------|
| success          | Boolean | 接口调用成功或失败的标志 true：成功，false：失败      |
| requestId        | String  | 请求唯一标识， 与调用接口时传入的requestId一致       |
| model            | <T>     | 业务接口调用成功后返回的具体响应信息，不同的接口返回的model不同 |
| message          | String  | 响应message                          |
| └ errorCode      | String  | 调用失败后，返回的错误码, 服务端错误码或业务层错误码        |
| └ errorMessage   | String  | 调用失败后，返回的错误信息                      |
| └ successMessage | String  | 调用成功后，返回的成功信息                      |
| └ innerRequestId | String  | 下游业务层的requestId，若为业务调用失败，则返回此字段    |

## 四、响应业务参数

| ID             | 名称             | 类型     | 长度 | 描述                                                                                                                                        |
|----------------|----------------|--------|----|-------------------------------------------------------------------------------------------------------------------------------------------|
| redConfirmUuid | redConfirmUuid | String | 32 | 红字确认单 UUID                                                                                                                                |
| redConfirmNo   | redConfirmNo   | String | 20 | 红字确认单编号                                                                                                                                   |
| confirmState   | confirmState   | String | 2  | 红字确认单状态 01：无需确认；02：销方录入待购方确认；03：购方录入待销方确认；04：购销双方已确认；05：作废（销方录入购方否认）；06：作废（购方录入销方否认）；07：作废（超72小时未确认）；08：作废（发起方已撤销）；09：作废（确认后撤销）【web连接器支持】 |
| confirmType    | confirmType    | String | 2  | 确认类型 01：确认，02：否认；03：撤销（仅支持web连接器方式）                                                                                                       |
| confirmDate    | confirmDate    | String | 19 | 确认日期 yyyy-MM-dd HH:mm:ss                                                                                                                  |
| redInvoiceNo   | redInvoiceNo   | String | 20 | 红字发票号码                                                                                                                                    |

## 五、错误码

| 名称    | 错误描述         |
|:------|--------------|
| 70169 | 参数{}不能为空     |
| 70170 | 参数{}不正确      |
| 70035 | 红字录入单操作异常，{} |
| 70103 | 乐企红字确认失败     |

## 六、请求示例

json格式示例

```json
POST  http://ip:port/post?method=baiwang.output.redinvoice.operate&version=1.0&requestId=b859c42f-005e-4d2c-bc97-da6a13ad447b

{
"redConfirmUuid": "6df741db489945bcae761d0338087435",
"sellerTaxNo": "91440604570155821Y",
"redConfirmNo": "44060422121001100222",
"taxNo": "91440604570155821Y",
"confirmType": "01",
"taxUserName": ""
}
```

sdk调用示例

```java
String url = "http://ip:port/post";       //接口地址
try
        {
OutputRedinvoiceOperateRequest request = new OutputRedinvoiceOperateRequest();
    request.

setRedConfirmUuid("6df741db489945bcae761d0338087435");
    request.

setSellerTaxNo("91440604570155821Y");
    request.

setRedConfirmNo("44060422121001100222");
    request.

setTaxNo("91440604570155821Y");
    request.

setConfirmType("01");
    request.

setTaxUserName("");

IBWClient client = new BWRestClient(url); // 初始化一个客户端
OutputRedinvoiceOperateResponse response = client.outputRedinvoice().operate(request);
    if(response.

isSuccess()){
        System.out.

println(response.getModel());
        }else{
        System.out.

println(response.getMessage().

getErrorMessage());
        System.out.

println(response.getMessage().

getErrorCode());
        }
        }
        catch(
ISPException e){
        e.

printStackTrace();
}
```

## 七、响应示例

成功返回示例

```json
{
  "success": true,
  "requestId": "5c2bc8a3-1399-4ff0-a039-92688c6740e0",
  "model": [
    {
      "redConfirmUuid": "6df741db489945bcae761d0338087435",
      "confirmDate": "",
      "redInvoiceNo": "",
      "confirmState": "08",
      "redConfirmNo": "44060422121001100222",
      "confirmType": "03"
    }
  ]
}
```

失败返回示例

```json
{
  "requestId": "b859c42f-005e-4d2c-bc97-da6a13ad447b",
  "success": false,
  "message": {
    "errorCode": "-1",
    "errorMessage": "No instances available for 10.100.3.2",
    "innerRequestId": ""
  }
}
```
