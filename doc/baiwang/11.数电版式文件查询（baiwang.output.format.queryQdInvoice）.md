# *baiwang.output.format.queryQdInvoice*

*全电版式查询*

## 一、请求公共参数

| 名称        | 类型     | 是否必须 | 描述         |
|-----------|--------|------|------------|
| method    | String | 是    | 接口指定唯一标识   |
| requestId | String | 是    | 请求唯一标识     |
| version   | String | 是    | 版本号，当前为1.0 |

## 二、请求业务参数

| ID          | 类型     | 长度 | 是否必须 | 描述 |
|:------------|:-------|:---|:-----|:---|
| taxNo       | String | 18 | 是    | ~  |
| data        | Object | 1  | 是    | ~  |
| └ invoiceNo | String | 20 | 是    | ~  |

备注：用 └ 标识层级关系

## 三、响应公共参数

| 名称               | 类型      | 描述                                 |
|------------------|---------|------------------------------------|
| success          | Boolean | 接口调用成功或失败的标志 true：成功，false：失败      |
| requestId        | String  | 请求唯一标识， 与调用接口时传入的requestId一致       |
| model            | <T>     | 业务接口调用成功后返回的具体响应信息，不同的接口返回的model不同 |
| message          | String  | 响应message                          |
| └ errorCode      | String  | 调用失败后，返回的错误码, 服务端错误码或业务层错误码        |
| └ errorMessage   | String  | 调用失败后，返回的错误信息                      |
| └ successMessage | String  | 调用成功后，返回的成功信息                      |
| └ innerRequestId | String  | 下游业务层的requestId，若为业务调用失败，则返回此字段    |

## 四、响应业务参数

| ID     | 名称      | 类型     | 长度  | 描述      |
|--------|---------|--------|-----|---------|
| pdfUrl | pdfurl  | String | 100 | pdf地址   |
| ofdUrl | field_2 | String | 100 | ofd地址   |
| xmlUrl | field_3 | String | 100 | xml下载地址 |

## 五、错误码

| 名称 | 错误描述 |
|:---|------|
|    |      |

## 六、请求示例

json格式示例

```json
POST  http://ip:port/post?method=baiwang.output.format.queryQdInvoice&version=1.0&requestId=aad6d619-aa4b-4718-82d1-fc7743847c11

{
"data": {
"invoiceNo": "22442000000921380435"
},
"taxNo": "91440604570155821Y"
}
```

sdk调用示例

```java
String url = "http://ip:port/post";       //接口地址
try
        {
OutputFormatQueryQdInvoiceRequest request = new OutputFormatQueryQdInvoiceRequest();
OutputFormatQueryQdInvoiceData data = new OutputFormatQueryQdInvoiceData();
    data.

setInvoiceNo("22442000000921380435");
    request.

setData(data);
    request.

setTaxNo("91440604570155821Y");

IBWClient client = new BWRestClient(url); // 初始化一个客户端
OutputFormatQueryQdInvoiceResponse response = client.outputFormat().queryQdInvoice(request);
    if(response.

isSuccess()){
        System.out.

println(response.getModel());
        }else{
        System.out.

println(response.getMessage().

getErrorMessage());
        System.out.

println(response.getMessage().

getErrorCode());
        }
        }
        catch(
ISPException e){
        e.

printStackTrace();
}
```

## 七、响应示例

成功返回示例

```json
{
  "success": true,
  "requestId": "5c2bc8a3-1399-4ff0-a039-92688c6740e0",
  "model": {
    "pdfUrl": "",
    "ofdUrl": "",
    "xmlUrl": ""
  }
}
```

失败返回示例

```json
{
  "requestId": "aad6d619-aa4b-4718-82d1-fc7743847c11",
  "success": false,
  "message": {
    "errorCode": "-1",
    "errorMessage": "No instances available for 10.100.3.2",
    "innerRequestId": ""
  }
}
```
