# *baiwang.output.format.create*

*版式文件生成对于电子发票，企业系统可以通过调用该接口，根据发票代码号码，或者发票请求流水号生成版式文件，并将该版式文件推送交付至受票方（手机短信、电子邮箱）。此接口支持OFD和PDF两种格式的版式文件生成和推送服务。接口出入参节点内容“长度”均为字节，税控发票业务字符集为GBK，一个中文汉字等于两个字节；数电发票业务字符集为UTF-8，一个中文汉字等于三个字节。（举例说明：参数长度限制12字节，税控发票转换汉字最多6个汉字，数电发票转换汉字最多4个汉字）*

## 一、请求公共参数

| 名称        | 类型     | 是否必须 | 描述         |
|-----------|--------|------|------------|
| method    | String | 是    | 接口指定唯一标识   |
| requestId | String | 是    | 请求唯一标识     |
| version   | String | 是    | 版本号，当前为1.0 |

## 二、请求业务参数

| ID                 | 类型     | 长度  | 是否必须 | 描述                                 |
|:-------------------|:-------|:----|:-----|:-----------------------------------|
| taxNo              | String | 20  | 是    | 销方机构税号                             |
| data               | Object | ~   | 是    | ~                                  |
| └ invoiceCode      | String | 20  | 否    | 发票代码                               |
| └ invoiceNo        | String | 50  | 否    | 发票号码                               |
| └ serialNo         | String | 50  | 否    | 发票请求流水号                            |
| └ phone            | String | 40  | 否    | 手机号码                               |
| └ email            | String | 100 | 否    | 邮箱                                 |
| └ emailCarbonCopy  | String | 200 | 否    | 最多可配置5个邮件地址，地址之间用英文逗号分隔            |
| └ pushType         | String | 20  | 否    | 版式通道标识，为空时默认为10000，传入非法值时会导致版式无法推送 |
| └ invoiceIssueMode | String | 2   | 否    | 1 全电版式生成 其他代表税控发票生成                |
| └ einvoiceNo       | String | 20  | 否    | 全电发票号码                             |

备注：用 └ 标识层级关系

## 三、响应公共参数

| 名称               | 类型      | 描述                                 |
|------------------|---------|------------------------------------|
| success          | Boolean | 接口调用成功或失败的标志 true：成功，false：失败      |
| requestId        | String  | 请求唯一标识， 与调用接口时传入的requestId一致       |
| model            | <T>     | 业务接口调用成功后返回的具体响应信息，不同的接口返回的model不同 |
| message          | String  | 响应message                          |
| └ errorCode      | String  | 调用失败后，返回的错误码, 服务端错误码或业务层错误码        |
| └ errorMessage   | String  | 调用失败后，返回的错误信息                      |
| └ successMessage | String  | 调用成功后，返回的成功信息                      |
| └ innerRequestId | String  | 下游业务层的requestId，若为业务调用失败，则返回此字段    |

## 四、响应业务参数

| ID          | 名称      | 类型     | 长度  | 描述                           |
|-------------|---------|--------|-----|------------------------------|
| eInvoiceUrl | 版式下载地址  | String | 150 | 版式生成后返回的下载地址，可以根据这个地址获取到版式文件 |
| fileType    | 板式类型    | String | 10  | PDF  OFD                     |
| urlMap      | 地址集合    | Object | 1   | ~                            |
| └ pdfUrl    | pdf下载地址 | String | 150 | ~                            |
| └ ofdUrl    | ofd下载地址 | String | 150 | ~                            |
| └ xmlUrl    | xml下载地址 | String | 150 | ~                            |

## 五、错误码

| 名称 | 错误描述 |
|:---|------|
|    |      |

## 六、请求示例

json格式示例

```json
POST  http://ip:port/post?method=baiwang.output.format.create&version=1.0&requestId=dd9b65b6-7b78-4f57-98a6-59a944b8267b

{
"data": {
"einvoiceNo": "",
"phone":"",
"emailCarbonCopy": "",
"invoiceNo": "*********",
"invoiceCode": "050003521280",
"email": "",
"serialNo":"",
"pushType": "",
"invoiceIssueMode": ""
},
"taxNo": "512345678900000040"
}
```

sdk调用示例

```java
String url = "http://ip:port/post";       //接口地址
try
        {
OutputFormatCreateRequest request = new OutputFormatCreateRequest();
OutputFormatCreateFormat data = new OutputFormatCreateFormat();
    data.

setEinvoiceNo("");
    data.

setPhone("");
    data.

setEmailCarbonCopy("");
    data.

setInvoiceNo("*********");
    data.

setInvoiceCode("050003521280");
    data.

setEmail("");
    data.

setSerialNo("");
    data.

setPushType("");
    data.

setInvoiceIssueMode("");
    request.

setData(data);
    request.

setTaxNo("512345678900000040");

IBWClient client = new BWRestClient(url); // 初始化一个客户端
OutputFormatCreateResponse response = client.outputFormat().create(request);
    if(response.

isSuccess()){
        System.out.

println(response.getModel());
        }else{
        System.out.

println(response.getMessage().

getErrorMessage());
        System.out.

println(response.getMessage().

getErrorCode());
        }
        }
        catch(
ISPException e){
        e.

printStackTrace();
}
```

## 七、响应示例

成功返回示例

```json
{
  "success": true,
  "requestId": "5c2bc8a3-1399-4ff0-a039-92688c6740e0",
  "model": {
    "urlMap": {
      "pdfUrl": "",
      "ofdUrl": "",
      "xmlUrl": ""
    },
    "eInvoiceUrl": "http://123.56.92.221/fp/d?d=DDAE3D96F0BC9EF12204BE678AC1E9F55A1834839C736EC97E6194F0E41362E2",
    "fileType": "PDF"
  }
}
```

失败返回示例

```json
{
  "requestId": "dd9b65b6-7b78-4f57-98a6-59a944b8267b",
  "success": false,
  "message": {
    "errorCode": "-1",
    "errorMessage": "No instances available for 10.100.3.2",
    "innerRequestId": ""
  }
}
```
