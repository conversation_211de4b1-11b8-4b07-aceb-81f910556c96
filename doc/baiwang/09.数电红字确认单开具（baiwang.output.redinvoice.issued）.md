# *baiwang.output.redinvoice.issued*

*使用红字确认单开具负数发票*

## 一、请求公共参数

| 名称        | 类型     | 是否必须 | 描述         |
|-----------|--------|------|------------|
| method    | String | 是    | 接口指定唯一标识   |
| requestId | String | 是    | 请求唯一标识     |
| version   | String | 是    | 版本号，当前为1.0 |

## 二、请求业务参数

| ID                    | 类型     | 长度 | 是否必须 | 描述                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
|:----------------------|:-------|:---|:-----|:-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| taxNo                 | String | 20 | 是    | 机构税号，必填                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| serialNo              | String | 50 | 是    | 开票流水号， 唯一标志开票请求                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |
| redConfirmUuid        | String | 20 | 是    | 红字确认单UUID，必填                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| drawer                | String | 20 | 条件必填 | 数电票Web连接器开票人取值优先级顺序：taxUserName＞drawer＞默认开票人（数电链接器处设置）；如果以上三处都未取到随机获取该税号下最近一次登录的数电账号对应的开票人，如果仍然取不到则无法开票；乐企：该字段与默认开票人（"云开票设置"处配置）不能同时为空，如果同时为空则无法开票；                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| taxUserName           | String | 20 | 否    | 税局登录用户名，不传时任选一个在线用户                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| sellerTaxNo           | String | 20 | 否    | 销方税号，不传用taxNo，农产品收购等传销售方身份标志                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| accessPlatformNo      | String | 20 | 否    | 直连单位平台编号  乐企直连单位平台编号，当前税号关联多个直连单位开票传入，非必填  未传入时根据默认直连单位平台编码赋值                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
| drawerCredentialsType | String | 4  | 否    | 开票人证件类型 当使用联用（使用单位与直连平台无控制关系）、他用能力时传入，非必填  传参时开票人证件类型需要开票人对应并且与关联直连单位申请接入时的信息一致，其他情况非必填   未传入时默认取乐企基础数据配置的默认开票人信息  101：组织机构代码证  102：营业执照  103：税务登记证  199：其他单位证件  201：居民身份证  202：军官证  203：武警警官证  204：士兵证  205：军队离退休干部证  206：残疾人证  207：残疾军人证（1-8级）  208：外国护照  209：港澳同胞回乡证  210：港澳居民来往内地通行证  211：台胞证  212：中华人民共和国往来港澳通行证  213：台湾居民来往大陆通行证  214：大陆居民往来台湾通行证  215：外国人居留证  216：外交官证  217：使（领事）馆证  218：海员证  219：香港永久性居民身份证  220：台湾身份证  221：澳门特别行政区永久性居民身份证  222：外国人身份证件  223：高校毕业生自主创业证  224：就业失业登记证  225：退休证  220：离休证  227：中国护照  228：城镇退役士兵自谋职业证  229：随军家属身份证明  230：中国人民解放军军官转业证书  231：中国人民解放军义务兵退出现役证  232：中国人民解放军士官退出现役证  233：外国人永久居留身份证（外国人永久居留证）  234：就业创业证  235：香港特别行政区护照  236：澳门特别行政区护照  237：中华人民共和国港澳居民居住证  238：中华人民共和国台湾居民居住证  239：《中华人民共和国外国人工作许可证》（A类）  240：《中华人民共和国外国人工作许可证》（B类）  241：《中华人民共和国外国人工作许可证》（C类）  291：出生医学证明  299：其他个人证件 |
| drawerCredentialsNo   | String | 30 | 否    | 开票人证件号码  乐企直连单位平台编号，当前税号关联多个直连单位开票传入，非必填  未传入时根据默认直连单位平台编码赋值                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

备注：用 └ 标识层级关系

## 三、响应公共参数

| 名称               | 类型      | 描述                                 |
|------------------|---------|------------------------------------|
| success          | Boolean | 接口调用成功或失败的标志 true：成功，false：失败      |
| requestId        | String  | 请求唯一标识， 与调用接口时传入的requestId一致       |
| model            | <T>     | 业务接口调用成功后返回的具体响应信息，不同的接口返回的model不同 |
| message          | String  | 响应message                          |
| └ errorCode      | String  | 调用失败后，返回的错误码, 服务端错误码或业务层错误码        |
| └ errorMessage   | String  | 调用失败后，返回的错误信息                      |
| └ successMessage | String  | 调用成功后，返回的成功信息                      |
| └ innerRequestId | String  | 下游业务层的requestId，若为业务调用失败，则返回此字段    |

## 四、响应业务参数

| ID                   | 名称          | 类型     | 长度   | 描述                                                 |
|----------------------|-------------|--------|------|----------------------------------------------------|
| serialNo             | 开票流水号       | String | 50   | 开票流水号， 唯一标志开票请求                                    |
| invoiceDate          | 开票日期        | String | 20   | 开票日期，格式：yyyyMMddHHmmss                             |
| einvoiceNo           | 全电发票号码      | String | 50   | 全电发票号码                                             |
| invoiceTypeCode      | 发票类型代码      | String | 10   | 发票类型代码                                             |
| invoiceTotalPriceTax | 发票票面价税合计总金额 | String | 50   | 发票票面价税合计总金额                                        |
| InvoiceTotalPrice    | 发票票面合计金额    | String | 50   | 发票票面合计金额，不含税                                       |
| invoiceTotalTax      | 发票票面合计税额    | String | 50   | 发票票面合计税额                                           |
| invoiceQrCode        | 二维码信息       | String | 50   | 二维码信息                                              |
| eInvoiceUrl          | 电子发票地址      | String | 1024 | 电票版式文件链接。文件生成/下载存在延迟，未获取到文件流，请继续补偿获取。              |
| paperInvoiceCode     | 纸质发票代码      | String | 12   | 全电纸质发票代码                                           |
| paperInvoiceNo       | 纸质发票号码      | String | 8    | 全电纸质发票号码                                           |
| invoiceDetailsList   | 发票明细节点      | List   |      | List&#60;OutputRedinvoiceIssuedObjectType>, 发票明细节点 |
| └ goodsName          | 商品名称        | String | 50   | 商品名称                                               |
| └ goodsCode          | 商品和服务税收分类编码 | String | 50   | 商品和服务税收分类编码（末级节点）                                  |
| └ goodsUnit          | 单位          | String | 20   | 单位                                                 |
| └ goodsQuantity      | 数量          | String | 50   | 数量                                                 |
| └ goodsPrice         | 单价          | String | 50   | 单价                                                 |
| └ goodsTotalPrice    | 金额          | String | 50   | 金额                                                 |
| └ goodsTaxRate       | 税率          | String | 50   | 税率                                                 |
| └ goodsTotalTax      | 税额          | String | 50   | 税额                                                 |
| └ goodsLineNo        | 商品行明细行号     | String | 50   | 商品行明细行号                                            |

## 五、错误码

| 名称 | 错误描述 |
|:---|------|
|    |      |

## 六、请求示例

json格式示例

```json
POST  http://ip:port/post?method=baiwang.output.redinvoice.issued&version=1.0&requestId=7684817f-a4ce-4a6e-9557-032804091443

{
"redConfirmUuid": "",
"sellerTaxNo": "",
"drawer": "",
"taxNo": "",
"drawerCredentialsNo": "110101198808080093",
"accessPlatformNo": "eb5ff6ab17ae11eeb4a3",
"drawerCredentialsType": "210",
"serialNo": "",
"taxUserName": ""
}
```

sdk调用示例

```java
String url = "http://ip:port/post";       //接口地址
try
        {
OutputRedinvoiceIssuedRequest request = new OutputRedinvoiceIssuedRequest();
    request.

setRedConfirmUuid("");
    request.

setSellerTaxNo("");
    request.

setDrawer("");
    request.

setTaxNo("");
    request.

setDrawerCredentialsNo("110101198808080093");
    request.

setAccessPlatformNo("eb5ff6ab17ae11eeb4a3");
    request.

setDrawerCredentialsType("210");
    request.

setSerialNo("");
    request.

setTaxUserName("");

IBWClient client = new BWRestClient(url); // 初始化一个客户端
OutputRedinvoiceIssuedResponse response = client.outputRedinvoice().issued(request);
    if(response.

isSuccess()){
        System.out.

println(response.getModel());
        }else{
        System.out.

println(response.getMessage().

getErrorMessage());
        System.out.

println(response.getMessage().

getErrorCode());
        }
        }
        catch(
ISPException e){
        e.

printStackTrace();
}
```

## 七、响应示例

成功返回示例

```json
{
  "success": true,
  "requestId": "5c2bc8a3-1399-4ff0-a039-92688c6740e0",
  "model": {
    "einvoiceNo": "",
    "invoiceQrCode": "",
    "paperInvoiceCode": "",
    "eInvoiceUrl": "",
    "invoiceTotalPrice": "",
    "invoiceTotalTax": "",
    "invoiceDetailsList": [
      {
        "goodsTaxRate": "",
        "goodsPrice": "",
        "goodsQuantity": "",
        "goodsUnit": "",
        "goodsTotalTax": "",
        "goodsCode": "",
        "goodsName": "",
        "goodsTotalPrice": "",
        "goodsLineNo": ""
      }
    ],
    "invoiceTypeCode": "",
    "paperInvoiceNo": "",
    "invoiceDate": "",
    "invoiceTotalPriceTax": "",
    "serialNo": ""
  }
}
```

失败返回示例

```json
{
  "requestId": "7684817f-a4ce-4a6e-9557-032804091443",
  "success": false,
  "message": {
    "errorCode": "-1",
    "errorMessage": "No instances available for 10.100.3.2",
    "innerRequestId": ""
  }
}
```
