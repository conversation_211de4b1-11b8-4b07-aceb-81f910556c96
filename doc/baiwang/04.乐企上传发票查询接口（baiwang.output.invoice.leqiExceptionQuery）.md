# *baiwang.output.invoice.leqiExceptionQuery*

*企业通过该接口查询乐企上传的发票，包括处理中、上传失败和上传成功状态的发票*

## 一、请求公共参数

| 名称        | 类型     | 是否必须 | 描述         |
|-----------|--------|------|------------|
| method    | String | 是    | 接口指定唯一标识   |
| requestId | String | 是    | 请求唯一标识     |
| version   | String | 是    | 版本号，当前为1.0 |

## 二、请求业务参数

| ID                 | 类型      | 长度  | 是否必须 | 描述                                                         |
|:-------------------|:--------|:----|:-----|:-----------------------------------------------------------|
| taxNo              | String  | 20  | 是    | 机构税号                                                       |
| data               | Object  | 1   | 否    | ~                                                          |
| └ serialNo         | String  | 100 | 否    | 开票流水号                                                      |
| └ invoiceNo        | String  | 20  | 否    | 发票号码                                                       |
| └ invoiceTypeCode  | String  | 2   | 否    | 01 增值税专用发票  02 普通发票  03 机动车销售统一发票  04 二手车销售统一发票  06 电子行程单; |
| └ invoiceStartDate | String  | 10  | 否    | 开票开始时间，格式：yyyy-MM-dd                                       |
| └ invoiceEndDate   | String  | 10  | 否    | 开票结束时间，格式：yyyy-MM-dd                                       |
| └ pageNo           | Integer | 20  | 否    | 默认查第一页                                                     |
| └ pageSize         | Integer | 20  | 否    | 每一页最多100条（默认查10条）                                          |

备注：用 └ 标识层级关系

## 三、响应公共参数

| 名称               | 类型      | 描述                                 |
|------------------|---------|------------------------------------|
| success          | Boolean | 接口调用成功或失败的标志 true：成功，false：失败      |
| requestId        | String  | 请求唯一标识， 与调用接口时传入的requestId一致       |
| model            | <T>     | 业务接口调用成功后返回的具体响应信息，不同的接口返回的model不同 |
| message          | String  | 响应message                          |
| └ errorCode      | String  | 调用失败后，返回的错误码, 服务端错误码或业务层错误码        |
| └ errorMessage   | String  | 调用失败后，返回的错误信息                      |
| └ successMessage | String  | 调用成功后，返回的成功信息                      |
| └ innerRequestId | String  | 下游业务层的requestId，若为业务调用失败，则返回此字段    |

## 四、响应业务参数

| ID                 | 名称                 | 类型     | 长度 | 描述                          |
|--------------------|--------------------|--------|----|-----------------------------|
| uploadResult       | uploadResult       | String | 2  | 上传结果,00：上传成功 01：处理中 02：上传失败 |
| uploadFailedReason | uploadFailedReason | String | 21 | 上传失败原因                      |
| taxNo              | taxNo              | String | 18 | 税号                          |
| serialNo           | serialNo           | String | 1  | 开票流水号                       |
| invoiceNo          | invoiceNo          | String | 13 | 发票号码                        |
| invoiceDate        | invoiceDate        | String | 20 | 开票日期，yyyy-MM-dd HH:mm:ss    |

## 五、错误码

| 名称 | 错误描述 |
|:---|------|
|    |      |

## 六、请求示例

json格式示例

```json
POST  http://ip:port/post?method=baiwang.output.invoice.leqiExceptionQuery&version=1.0&requestId=0aa06f1d-76bc-49dd-96e5-20f9136a69ad

{
"data": {
"pageNo": 0,
"invoiceTypeCode":"06",
"pageSize": 0,
"invoiceNo": "24157200000000033507",
"invoiceStartDate": "2024-04-01",
"invoiceEndDate": "2024-04-18",
"serialNo":"20221227011"
},
"taxNo": "91150202MA0Q17NK5H"
}
```

sdk调用示例

```java
String url = "http://ip:port/post";       //接口地址
try
        {
OutputInvoiceLeqiExceptionQueryRequest request = new OutputInvoiceLeqiExceptionQueryRequest();
OutputInvoiceLeqiExceptionQueryData data = new OutputInvoiceLeqiExceptionQueryData();
    data.

setPageNo(0);
    data.

setInvoiceTypeCode("06");
    data.

setPageSize(0);
    data.

setInvoiceNo("24157200000000033507");
    data.

setInvoiceStartDate("2024-04-01");
    data.

setInvoiceEndDate("2024-04-18");
    data.

setSerialNo("20221227011");
    request.

setData(data);
    request.

setTaxNo("91150202MA0Q17NK5H");

IBWClient client = new BWRestClient(url); // 初始化一个客户端
OutputInvoiceLeqiExceptionQueryResponse response = client.outputInvoice().leqiExceptionQuery(request);
    if(response.

isSuccess()){
        System.out.

println(response.getModel());
        }else{
        System.out.

println(response.getMessage().

getErrorMessage());
        System.out.

println(response.getMessage().

getErrorCode());
        }
        }
        catch(
ISPException e){
        e.

printStackTrace();
}
```

## 七、响应示例

成功返回示例

```json
{
  "success": true,
  "requestId": "5c2bc8a3-1399-4ff0-a039-92688c6740e0",
  "model": [
    {
      "uploadResult": "02",
      "uploadFailedReason": "73010 请求乐企失败:发票票种取值错误",
      "taxNo": "91150202MA0Q17NK5H",
      "invoiceNo": "1300000093788",
      "invoiceDate": "2024-04-18 17:57:24",
      "serialNo": ""
    }
  ]
}
```

失败返回示例

```json
{
  "requestId": "0aa06f1d-76bc-49dd-96e5-20f9136a69ad",
  "success": false,
  "message": {
    "errorCode": "-1",
    "errorMessage": "No instances available for 10.100.3.2",
    "innerRequestId": ""
  }
}
```
