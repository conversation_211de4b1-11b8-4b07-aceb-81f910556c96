# *baiwang.output.invoice.query*

**(此文档内容配套的SDK版本号为：1.7.51)**

*已开发票查询2.0
企业系统可以通过调用此接口查询已开发票的信息。可以根据企业税号、开票流水号、发票代码、发票号码、发票类型代码、开票日期起止等条件进行组合查询。*

## 一、请求公共参数

| 名称        | 类型     | 是否必须 | 描述         |
|-----------|--------|------|------------|
| method    | String | 是    | 接口指定唯一标识   |
| requestId | String | 是    | 请求唯一标识     |
| version   | String | 是    | 版本号，当前为1.0 |

## 二、请求业务参数

| ID                    | 类型      | 长度  | 是否必须 | 描述                                                                                       |
|:----------------------|:--------|:----|:-----|:-----------------------------------------------------------------------------------------|
| taxNo                 | String  | 20  | 是    | 销方机构税号                                                                                   |
| orgCode               | String  | 64  | 否    | 组织机构编码，可以查询对应税号下该组织机构编码的发票数据                                                             |
| data                  | Object  | ~   | 是    | 集合                                                                                       |
| └ queryAll            | Boolean | 5   | 否    | 是否查询税号下所有发票(如果机构代码为空且该字段为true，则查询税号下所有符合条件的发票)                                           |
| └ serialNo            | String  | 50  | 否    | 开票流水号                                                                                    |
| └ invoiceCode         | String  | 12  | 否    | 发票代码                                                                                     |
| └ invoiceNo           | String  | 8   | 否    | 发票号码                                                                                     |
| └ invoiceTypeCode     | String  | 3   | 否    | 发票类型代码 004：增值税专用发票；007：增值税普通发票；026：增值税电子发票；025：增值税卷式发票；028:增值税电子专用发票 005 机动车发票 006 二手车发票 |
| └ invoiceTerminalCode | String  | 30  | 否    | 开票终端代码                                                                                   |
| └ sourceMark          | String  | 80  | 否    | 来源标志 01 接口开具 02已开上传 03 开票申请单 04 流水单 05 接口待开上传 06 销方待开导入 07 购方待开导入 08 非百望云开具 17 历史发票管理    |
| └ invoiceSpecialMark  | String  | 2   | 否    | 特殊票种标志， 00：普通发票；01：农产品销售；02：农产品收购；08：成品油 12 机动车                                          |
| └ buyerTaxNo          | String  | 20  | 否    | 购方税号                                                                                     |
| └ buyerName           | String  | 80  | 否    | 购方单位名称                                                                                   |
| └ printStatus         | String  | 1   | 否    | 打印状态 0，1                                                                                 |
| └ invoiceStatus       | String  | 2   | 否    | 发票状态：发票状态默认为空，00开具成功 02空白发票作废 03:已开发票作废                                                  |
| └ invoiceType         | String  | 1   | 否    | 开票类型：0:正数 1：负数                                                                           |
| └ invoiceStartDate    | String  | 14  | 否    | 开票日期起，格式：yyyy-MM-dd                                                                      |
| └ invoiceEndDate      | String  | 14  | 否    | 开票日期止，格式：yyyy-MM-dd                                                                      |
| └ expressNo           | String  | 50  | 否    | 快递单号                                                                                     |
| └ contractNo          | String  | 50  | 否    | 保单号                                                                                      |
| └ orderNo             | String  | 50  | 否    | 业务请求流水号                                                                                  |
| └ taxationMethod      | String  | 1   | 否    | 征收方式 0：普通征收 2：差额征收                                                                       |
| └ invoiceListMark     | String  | 1   | 否    | 清单标志 0：非请单 1：清单                                                                          |
| └ machineNo           | String  | 10  | 否    | 机器编号                                                                                     |
| └ systemName          | String  | 150 | 否    | 第三方系统名称                                                                                  |
| └ systemId            | String  | 50  | 否    | 第三方系统id                                                                                  |
| └ invoiceCheckMark    | String  | 3   | 否    | 验签状态(N00 未签名,Y00 已签名,未上传 Y11,验签成功 Y10 验签失败)                                              |
| └ pageNo              | Integer | 5   | 否    | 页码                                                                                       |
| └ pageSize            | Integer | 3   | 否    | 每页条数 最多100条                                                                              |
| └ ext                 | Map     | ~   | 否    | Map&#60;String, Object>, 扩展字段                                                            |
| └ updateTimeStart     | String  | 10  | 否    | 更新开始时间，格式：yyyy-MM-dd                                                                     |
| └ updateTimeEnd       | String  | 10  | 否    | 更新终止时间，格式：yyyy-MM-dd                                                                     |
| └ originalInvoiceCode | String  | 12  | 否    | 原发票代码                                                                                    |
| └ originalInvoiceNo   | String  | 8   | 否    | 原发票号码                                                                                    |

备注：用 └ 标识层级关系

## 三、响应公共参数

| 名称               | 类型      | 描述                                 |
|------------------|---------|------------------------------------|
| success          | Boolean | 接口调用成功或失败的标志 true：成功，false：失败      |
| requestId        | String  | 请求唯一标识， 与调用接口时传入的requestId一致       |
| model            | <T>     | 业务接口调用成功后返回的具体响应信息，不同的接口返回的model不同 |
| message          | String  | 响应message                          |
| └ errorCode      | String  | 调用失败后，返回的错误码, 服务端错误码或业务层错误码        |
| └ errorMessage   | String  | 调用失败后，返回的错误信息                      |
| └ successMessage | String  | 调用成功后，返回的成功信息                      |
| └ innerRequestId | String  | 下游业务层的requestId，若为业务调用失败，则返回此字段    |

## 四、响应业务参数

| ID                         | 名称                  | 类型         | 长度   | 描述                                                                                    |
|----------------------------|---------------------|------------|------|---------------------------------------------------------------------------------------|
| serialNo                   | 开票流水号               | String     | 50   | ~                                                                                     |
| invoiceCode                | 发票代码                | String     | 12   | ~                                                                                     |
| invoiceNo                  | 发票号码                | String     | 8    | ~                                                                                     |
| deviceType                 | 设备类型                | String     | ~    | 0税控服务器，1税控盘                                                                           |
| buyerTaxNo                 | 购方纳税人识别号            | String     | 20   | ~                                                                                     |
| buyerName                  | 购方单位名称              | String     | 80   | ~                                                                                     |
| buyerAddressPhone          | 购方地址及电话             | String     | 100  | ~                                                                                     |
| buyerBankAccount           | 购方开户行及账号            | String     | 100  | ~                                                                                     |
| sellerTaxNo                | 销方单位代码              | String     | 20   | ~                                                                                     |
| sellerName                 | 销方单位名称              | String     | 100  | ~                                                                                     |
| sellerAddressPhone         | 销方地址及电话             | String     | 100  | ~                                                                                     |
| sellerBankAccount          | 销方开户行及账号            | String     | 100  | ~                                                                                     |
| invoiceTotalPrice          | 合计金额，保留两位小数         | BigDecimal | 13   | ~                                                                                     |
| invoiceTotalTax            | 合计税额，保留两位小数         | BigDecimal | 13   | ~                                                                                     |
| invoiceTotalPriceTax       | 价税合计，保留两位小数         | BigDecimal | 13   | ~                                                                                     |
| invoiceListMark            | 清单标志：0：无清单1：带清单     | String     | 1    | ~                                                                                     |
| invoiceTypeCode            | 发票类型代码              | String     | 3    | ~                                                                                     |
| invoiceType                | 发票类型：0：正数1：负数       | String     | 1    | ~                                                                                     |
| taxationMethod             | 征收方式                | String     | 1    | ~                                                                                     |
| invoiceSpecialMark         | 特殊票种标志              | String     | 2    | 特殊票种标志， 00：普通发票；01：农产品销售；02：农产品收购；08：成品油 12 机动车（默认是00普通发票）                            |
| invoiceTerminalCode        | 开票点编码               | String     | 30   | ~                                                                                     |
| invoiceDate                | 开票日期                | String     | 14   | yyyy-MM-dd                                                                            |
| taxControlCode             | 税控码                 | String     | 22   | ~                                                                                     |
| deductibleAmount           | 扣除额，保留两位小数          | BigDecimal | 13   | ~                                                                                     |
| remarks                    | 备注                  | String     | 184  | ~                                                                                     |
| remarkPrintInfo            | 打印备注信息              | String     | 256  | ~                                                                                     |
| drawer                     | 开票人                 | String     | 20   | ~                                                                                     |
| checker                    | 复核人                 | String     | 16   | ~                                                                                     |
| payee                      | 收款人                 | String     | 16   | ~                                                                                     |
| invoiceCheckCode           | 校验码                 | String     | 22   | ~                                                                                     |
| invoiceQrCode              | 二维码                 | String     | 8192 | ~                                                                                     |
| buyerEmail                 | 购方客户邮箱              | String     | 100  | ~                                                                                     |
| buyerPhone                 | 购方客户电话              | String     | 40   | ~                                                                                     |
| eInvoiceUrl                | 电子发票下载地址            | String     | 150  | ~                                                                                     |
| h5InvoiceUrl               | 电子发票预览地址            | String     | 150  | ~                                                                                     |
| invoiceInvalidDate         | 作废日期                | String     | 14   | ~                                                                                     |
| invoiceStatus              | 发票状态                | String     | 2    | ~                                                                                     |
| invoiceCheckMark           | 验签标志                | String     | 3    | N00 未签名 Y00 已签名，未上传 Y11 验签成功 Y10 验签失败                                                 |
| invoiceUploadMark          | 上传标志                | String     | ~    | 1/0                                                                                   |
| invoiceSignMark            | 签名标志                | String     | ~    | Y/N                                                                                   |
| redInfoNo                  | 红字信息表编号             | String     | 20   | ~                                                                                     |
| originalInvoiceCode        | 原发票代码               | String     | 12   | ~                                                                                     |
| originalInvoiceNo          | 原发票号码               | String     | 8    | ~                                                                                     |
| systemId                   | 第三方系统id             | String     | ~    | ~                                                                                     |
| systemName                 | 第三方系统名称             | String     | 150  | ~                                                                                     |
| sourceMark                 | 来源标志                | String     | 50   | 来源标志 01 接口开具 02已开上传 03 开票申请单 04 流水单 05 接口待开上传 06 销方待开导入 07 购方待开导入 08 非百望云开具 17 历史发票管理 |
| orderNo                    | 业务请求流水号             | String     | 50   | ~                                                                                     |
| ext                        | 扩展字段                | Map        | ~    | Map&#60;String, Object>                                                               |
| attachInfo                 | 发票附加信息              | Object     | ~    | ~                                                                                     |
| └ formatPushStatus         | 版式推送状态              | String     | 4    | ~                                                                                     |
| invoiceDetailsList         |                     | List       | ~    | List&#60;OutputInvoiceQueryOutputInvoiceQueryInvoiceDetail>                           |
| └ goodsLineNo              | 明细行号                | String     | 4    | ~                                                                                     |
| └ goodsCode                | 商品编码                | String     | 40   | ~                                                                                     |
| └ goodsName                | 商品名称                | String     | 92   | ~                                                                                     |
| └ goodsSpecification       | 规格型号                | String     | 40   | ~                                                                                     |
| └ goodsUnit                | 计量单位                | String     | 14   | ~                                                                                     |
| └ goodsQuantity            | 商品数量                | BigDecimal | 14   | ~                                                                                     |
| └ goodsPrice               | 商品单价                | BigDecimal | 13   | ~                                                                                     |
| └ goodsTotalPrice          | 金额                  | BigDecimal | 13   | ~                                                                                     |
| └ goodsTotalTax            | 税额                  | BigDecimal | 13   | ~                                                                                     |
| └ goodsTaxRate             | 税率                  | BigDecimal | 13   | ~                                                                                     |
| └ priceTaxMark             | 含税标志0：不含税 1：含税      | String     | 1    | ~                                                                                     |
| └ vatSpecialManagement     | 增值税特殊管理             | String     | 200  | ~                                                                                     |
| └ freeTaxMark              | 零税率标识 空代表无          | String     | 1    | ~                                                                                     |
| └ preferentialMarkFlag     | 是否使用优惠政策 0:未使用，1:使用 | String     | 1    | ~                                                                                     |
| └ invoiceLineNature        | 发票行性质               | String     | 1    | ~                                                                                     |
| └ excludTaxgoodsPrice      | 不含税商品单价             | BigDecimal | 13   | ～                                                                                     |
| └ excludTaxgoodsTotalPrice | 不含税金额               | BigDecimal | 13   | ～                                                                                     |
| └ includTaxgoodsPrice      | 含税商品单价              | BigDecimal | 13   | ～                                                                                     |
| └ includTaxgoodsTotalPrice | 含税金额                | BigDecimal | 13   | ～                                                                                     |
| └ ext                      | 扩展字段                | Map        | ~    | Map&#60;String, Object>                                                               |
| invoiceVehicleInfo         | 商品明细                | Object     | ~    | 机动车二手车明细                                                                              |
| └ licensePlate             | 车牌照号                | String     | ~    | ~                                                                                     |
| └ vehicleType              | 车辆类型                | String     | ~    | ~                                                                                     |
| └ registryNo               | 登记证号                | String     | ~    | ~                                                                                     |
| └ brandModel               | 厂牌型号                | String     | ~    | ~                                                                                     |
| └ originPlace              | 产地                  | String     | ~    | ~                                                                                     |
| └ vehicleAdministration    | 转入地车辆管理所名称          | String     | ~    | ~                                                                                     |
| └ vehicleNo                | 车辆识别代号/车架号码         | String     | ~    | ~                                                                                     |
| └ carPaymentVoucherNo      | 车购税完税凭证号码           | String     | ~    | ~                                                                                     |
| └ invoiceMark              | 开票标识                | String     | ~    | 0未开票、1已开票                                                                             |
| └ vatMark                  | 增值税标志               | String     | ~    | 0免征增值税、1不征增值税                                                                         |
| └ issuedInvoiceCode        | 已开发票代码              | String     | ~    | ~                                                                                     |
| └ issuedInvoiceNo          | 已开发票号码              | String     | ~    | ~                                                                                     |
| └ issuedTotalPrice         | 已开票销售额              | BigDecimal | ~    | ~                                                                                     |
| └ issuedTotalTax           | 已开票税额               | BigDecimal | ~    | ~                                                                                     |
| └ issuedTaxRate            | 已开票税率               | BigDecimal | ~    | ~                                                                                     |
| └ paymentVoucherMark       | 开具完税证明标识            | String     | ~    | 0为未开、1为已开                                                                             |
| └ paymentVoucherNo         | 完税凭证号码              | String     | ~    | ~                                                                                     |
| └ paymentVoucherToralPrice | 完税凭证销售额             | BigDecimal | ~    | ~                                                                                     |
| └ paymentVoucherTaxRate    | 完税凭证税率              | BigDecimal | ~    | ~                                                                                     |
| └ paymentVoucherTotalTax   | 完税凭证税额              | BigDecimal | ~    | ~                                                                                     |
| └ goodsCode                | 商品编码                | String     | ~    | (总局固定19位编码)，不能修改 +自行编码，(以2位为一级，共10级，每级可用编码值为00-99 或AA-ZZ)                             |
| └ vatSpecialManagement     | 增值税特殊管理             | String     | ~    | ~                                                                                     |
| └ freeTaxMark              | 零税率标识               | String     | ~    | 空代表无1 出口免税和其他免税优惠政策2 不征增值税3 普通零税率                                                     |
| └ preferentialMarkFlag     | 优惠政策标识              | String     | ~    | 0:未使用，1:使用                                                                            |
| └ sellerTaxNo              | 卖方单位代码              | String     | ~    | ~                                                                                     |
| └ sellerName               | 卖方单位名称              | String     | ~    | ~                                                                                     |
| └ sellerAddress            | 卖方单位地址              | String     | ~    | ~                                                                                     |
| └ sellerPhone              | 卖方单位电话              | String     | ~    | ~                                                                                     |
| └ manufacturingEnterprise  | 产地                  | String     | ~    | ~                                                                                     |
| └ certificateNo            | 合格证号                | String     | ~    | ~                                                                                     |
| └ importCertificateNo      | 进口证明书号              | String     | ~    | ~                                                                                     |
| └ inspectionListNo         | 商检单号                | String     | ~    | ~                                                                                     |
| └ engineNo                 | 发动机号码               | String     | ~    | ~                                                                                     |
| └ tonnage                  | 吨位                  | String     | ~    | ~                                                                                     |
| └ passengersLimited        | 限乘人数                | String     | ~    | ~                                                                                     |
| └ ext                      | 扩展字段                | Map        | ~    | Map&#60;String, Object>                                                               |
| └ vehicleSample            | 机动车新旧规范标识           | String     | 1    | 机动车新旧规范标识 0 旧规范 1 新规范 默认为0                                                            |
| invoiceInvalidOperator     | 作废人                 | String     | 20   | ~                                                                                     |
| reverseIssueMark           | 二手车反向开具标识           | String     | 2    | 二手车反向开具标识                                                                             |
| taxAuthorityName           | 税务机关名称              | String     | 50   | ~                                                                                     |
| taxAuthorityNo             | 税务机关代码              | String     | 50   | ~                                                                                     |
| invoiceTime                | 开票时间                | String     | 20   | 开票时间（精确到秒） 格式：yyyy-MM-dd HH:mm:ss                                                     |
| printStatus                | 打印状态                | String     | 1    | 打印状态 ：1 已打印；0 未打印                                                                     |

## 五、错误码

| 名称 | 错误描述 |
|:---|------|
|    |      |

## 六、请求示例

json格式示例

```json
POST  http://ip:port/post?method=baiwang.output.invoice.query&version=1.0&requestId=17e2f299-bc48-4f6f-8a5c-b2c7692d34cb

{
"data": {
"updateTimeEnd": "2021-11-30",
"expressNo":"",
"contractNo": "",
"pageSize": 1,
"queryAll": false,
"taxationMethod": "",
"systemName":"",
"invoiceCheckMark": "",
"pageNo": 1,
"invoiceTerminalCode": "",
"invoiceType": "",
"machineNo":"",
"invoiceNo": "",
"invoiceEndDate": "",
"ext": {},
"systemId": "",
"orderNo": "",
"buyerTaxNo": "",
"printStatus": "",
"invoiceSpecialMark": "",
"buyerName": "",
"updateTimeStart": "2021-11-01",
"originalInvoiceNo": "",
"invoiceCode": "",
"invoiceStartDate": "",
"invoiceListMark": "",
"serialNo": "",
"sourceMark": "",
"invoiceTypeCode": "",
"invoiceStatus": "",
"originalInvoiceCode": ""
},
"orgCode": "",
"taxNo": "512345678900000040"
}
```

sdk调用示例

```java
String url = "http://ip:port/post";       //接口地址
try
        {
OutputInvoiceQueryRequest request = new OutputInvoiceQueryRequest();
OutputInvoiceQueryOutputInvoiceQueryParam data = new OutputInvoiceQueryOutputInvoiceQueryParam();
    data.

setUpdateTimeEnd("2021-11-30");
    data.

setExpressNo("");
    data.

setContractNo("");
    data.

setPageSize(1);
    data.

setQueryAll(false);
    data.

setTaxationMethod("");
    data.

setSystemName("");
    data.

setInvoiceCheckMark("");
    data.

setPageNo(1);
    data.

setInvoiceTerminalCode("");
    data.

setInvoiceType("");
    data.

setMachineNo("");
    data.

setInvoiceNo("");
    data.

setInvoiceEndDate("");

Map<String, Object> ext = new HashMap<String, Object>();
    data.

setExt(ext);
    data.

setSystemId("");
    data.

setOrderNo("");
    data.

setBuyerTaxNo("");
    data.

setPrintStatus("");
    data.

setInvoiceSpecialMark("");
    data.

setBuyerName("");
    data.

setUpdateTimeStart("2021-11-01");
    data.

setOriginalInvoiceNo("");
    data.

setInvoiceCode("");
    data.

setInvoiceStartDate("");
    data.

setInvoiceListMark("");
    data.

setSerialNo("");
    data.

setSourceMark("");
    data.

setInvoiceTypeCode("");
    data.

setInvoiceStatus("");
    data.

setOriginalInvoiceCode("");
    request.

setData(data);
    request.

setOrgCode("");
    request.

setTaxNo("512345678900000040");

IBWClient client = new BWRestClient(url); // 初始化一个客户端
OutputInvoiceQueryResponse response = client.outputInvoice().query(request);
    if(response.

isSuccess()){
        System.out.

println(response.getModel());
        }else{
        System.out.

println(response.getMessage().

getErrorMessage());
        System.out.

println(response.getMessage().

getErrorCode());
        }
        }
        catch(
ISPException e){
        e.

printStackTrace();
}
```

## 七、响应示例

成功返回示例

```json
{
  "success": true,
  "requestId": "5c2bc8a3-1399-4ff0-a039-92688c6740e0",
  "model": [
    {
      "invoiceTime": "",
      "buyerBankAccount": "中国港市荷城支行1103046919208899850",
      "buyerEmail": "<EMAIL>",
      "invoiceTotalTax": 0.0,
      "checker": "复核人",
      "taxationMethod": "0",
      "redInfoNo": "",
      "payee": "收款人",
      "systemName": "",
      "invoiceType": "0",
      "invoiceTerminalCode": "ceshi40",
      "invoiceNo": "********",
      "deviceType": "0",
      "ext": {},
      "orderNo": "",
      "buyerTaxNo": "512345678900000040",
      "invoiceInvalidDate": "",
      "invoiceSignMark": "",
      "originalInvoiceNo": "",
      "invoiceCode": "************",
      "invoiceListMark": "0",
      "serialNo": "20191011057er-1-1",
      "taxControlCode": "03+12*5--/*+7/+<1<44060<8/1/5-071>9*-4*********+6051>59036+68905<<-**5<*4+8840<<>>**********+2012<3019-+282-3/2>",
      "reverseIssueMark": "1",
      "sellerTaxNo": "512345678900000040",
      "buyerPhone": "***********",
      "sourceMark": "",
      "invoiceTotalPrice": 9.0,
      "invoiceDetailsList": [
        {
          "ext": {},
          "priceTaxMark": "0",
          "goodsTaxRate": 0.0,
          "invoiceLineNature": "0",
          "excludTaxgoodsTotalPrice": 0.0,
          "includTaxgoodsTotalPrice": 0.0,
          "goodsTotalPrice": 1.0,
          "preferentialMarkFlag": "1",
          "goodsSpecification": "规格型号1",
          "excludTaxgoodsPrice": 0.0,
          "goodsPrice": 1,
          "freeTaxMark": "",
          "goodsQuantity": 1,
          "goodsUnit": "单位",
          "goodsTotalTax": 0.0,
          "includTaxgoodsPrice": 0.0,
          "goodsCode": "1070601990000000000",
          "goodsName": "*塑料制品*MINI家族A-69U(爱度)车体左护板ABS白件273G1",
          "goodsLineNo": "1",
          "vatSpecialManagement": "100%先征后退"
        }
      ],
      "taxAuthorityName": "",
      "originalInvoiceCode": "",
      "invoiceInvalidOperator": "",
      "eInvoiceUrl": "",
      "sellerName": "测试40",
      "sellerAddressPhone": "马连坡集团2号街,.，18910673200",
      "invoiceQrCode": "iVBORw0KGgoAAAANSUhEUgAAAJQAAACUCAIAAAD6XpeDAAADDUlEQVR42u3a207DMBAFwP7/T5dXkECQ+Jy1SyePVVqcHaPsxY9H4Xp+un76/Kd7/nL/1e+urPPq2q7+znEXPHjw4L0J3nPhaiy68fCN51r5fGU98ODBgwcvg5dKTFYSkMamadzT3tzw4MGDB+818BqBThX7qcDBgwcPHjx4yeK9UdjuKq7hwYMHD95ZeKnFNZrU7WuywX30VAEePHjw/jFeY+Dp86Ggw4MHCR68YxOByeFqKtE4IYZf1gYPHjx48G6vbVthOHgQqAEzOaC+vFB48ODBg7f94E0qoKkhaiM+qX+MpQkDPHjw4MH7tc5rP8AJSVN7/ZONAnjw4MGDl6/52gVmoxm9kvi0A31E5wUePHjw3gQvNTjd9fCp4j2VQI0mLPDgwYMHL9KATjV5Vx441exuFPuNjXL578KDBw8evAjeK77AJ5OpdmF+uUiHBw8ePHi38RoHinYFpX3AaddGhwcPHjx4mfdf6uHbw9V2c3nyENTSpoEHDx48eLfjM9kUTjXHU2trJyypCx48ePDgXVtzqpnb/m47wakMS8uJITx48ODBy+BVsqBCkd5oDuzaZJdjCw8ePHjw0rEdTVhS90y+71ONZnjw4MGD18VrBOK0pnAqUWrEZ2nN8ODBgwfv9ppTDzP5km83wXfhVSYM8ODBg/fmeO0kZSUZafx+u+Fw3CQdHjx48OBVk4tUIjBZ1E8W/rHhNjx48ODBi7//2i/tBuqu4LYb60uDWXjw4MGDN1YLNpKLRuP7hKFrLPmCBw8ePHhbho3t4WobqdFwjyUp8ODBgwcvgtcuxl9lQzzKFzx48ODB6+I9y9euIKYOKaXW0DhABQ8ePHjwqn3XpRdvY6ibWufkhptMguDBgwcPXiagK4tLHXBqrHkFcnKIDQ8ePHjwMnj1LngheWlspl3F8lKTGh48ePDgbcdLvbQbjfLG8LmxabYlLPDgwYMHL15stn+z3SxuD6vhwYMHD14er9HkbQSxkfg0EpD68BkePHjw4B17AGlyM00mRyc8Fzx48ODB+/bvfgCPdbv3kFpXNQAAAABJRU5ErkJggg==",
      "invoiceCheckMark": "07594433668815072007",
      "invoiceUploadMark": "",
      "taxAuthorityNo": "",
      "buyerAddressPhone": "北京18833670970",
      "systemId": "",
      "deductibleAmount": 0,
      "h5InvoiceUrl": "http://mtest.baiwang-inner.com/invoice?param=083BCEDF34361E64A3A4127707D3B8F4D80C02F30A519E2AC411836B076AE123",
      "invoiceVehicleInfo": {
        "invoiceMark": "",
        "originPlace": "",
        "paymentVoucherTotalTax": 0,
        "sellerName": "",
        "brandModel": "",
        "sellerAddress": "",
        "vehicleSample": "",
        "registryNo": "",
        "paymentVoucherNo": "",
        "licensePlate": "",
        "tonnage": "",
        "paymentVoucherMark": "",
        "vehicleType": "",
        "issuedTotalTax": 0,
        "issuedTaxRate": 0,
        "paymentVoucherTaxRate": 0,
        "ext": {},
        "engineNo": "",
        "paymentVoucherToralPrice": 0,
        "passengersLimited": "",
        "sellerPhone": "",
        "carPaymentVoucherNo": "",
        "importCertificateNo": "",
        "certificateNo": "",
        "preferentialMarkFlag": "",
        "vehicleNo": "",
        "sellerTaxNo": "",
        "issuedTotalPrice": 0,
        "freeTaxMark": "",
        "manufacturingEnterprise": "",
        "vehicleAdministration": "",
        "issuedInvoiceCode": "",
        "issuedInvoiceNo": "",
        "vatMark": "",
        "goodsCode": "",
        "inspectionListNo": "",
        "vatSpecialManagement": ""
      },
      "printStatus": "",
      "drawer": "cpy001d",
      "invoiceSpecialMark": "00",
      "buyerName": "测试40",
      "invoiceDate": "2019-10-12",
      "invoiceTotalPriceTax": 9.0,
      "remarkPrintInfo": "红字信息编号：****************\\n10",
      "invoiceCheckCode": "07594433668815072007",
      "attachInfo": {
        "formatPushStatus": ""
      },
      "sellerBankAccount": "即将开机了*************",
      "invoiceTypeCode": "026",
      "invoiceStatus": "00",
      "remarks": "dfggedf"
    }
  ]
}
```

失败返回示例

```json
{
  "requestId": "17e2f299-bc48-4f6f-8a5c-b2c7692d34cb",
  "success": false,
  "message": {
    "errorCode": "-1",
    "errorMessage": "No instances available for 10.100.3.2",
    "innerRequestId": ""
  }
}
```
