# *baiwang.output.invoice.exceptionOperation*

*企业通过乐企开具的数电票，包括电子客票行程单，如果上传状态为失败，可以通过当前接口进行删除或者重新上传*

## 一、请求公共参数

| 名称        | 类型     | 是否必须 | 描述         |
|-----------|--------|------|------------|
| method    | String | 是    | 接口指定唯一标识   |
| requestId | String | 是    | 请求唯一标识     |
| version   | String | 是    | 版本号，当前为1.0 |

## 二、请求业务参数

| ID             | 类型     | 长度  | 是否必须 | 描述                  |
|:---------------|:-------|:----|:-----|:--------------------|
| taxNo          | String | 20  | 是    | 机构税号                |
| data           | Object | 1   | 否    | ~                   |
| └ invoiceNo    | String | 20  | 否    | 数电发票号码，和开票流水号二选一必填  |
| └ serialNo     | String | 255 | 否    | 开票流水号，和数电发票号码二选一笔填  |
| └ uploadResult | String | 2   | 否    | 上传结果，01：处理中 02：上传失败 |
| └ operateType  | String | 1   | 是    | 0：重新上传 1: 删除 2: 刷新  |

备注：用 └ 标识层级关系

## 三、响应公共参数

| 名称               | 类型      | 描述                                 |
|------------------|---------|------------------------------------|
| success          | Boolean | 接口调用成功或失败的标志 true：成功，false：失败      |
| requestId        | String  | 请求唯一标识， 与调用接口时传入的requestId一致       |
| model            | <T>     | 业务接口调用成功后返回的具体响应信息，不同的接口返回的model不同 |
| message          | String  | 响应message                          |
| └ errorCode      | String  | 调用失败后，返回的错误码, 服务端错误码或业务层错误码        |
| └ errorMessage   | String  | 调用失败后，返回的错误信息                      |
| └ successMessage | String  | 调用成功后，返回的成功信息                      |
| └ innerRequestId | String  | 下游业务层的requestId，若为业务调用失败，则返回此字段    |

## 四、响应业务参数

| ID         | 名称         | 类型     | 长度 | 描述 |
|------------|------------|--------|----|----|
| returnMsg  | returnMsg  | String | 1  | ~  |
| returnCode | returnCode | String | 1  | ~  |
| serialNo   | serialNo   | String | 1  | ~  |

## 五、错误码

| 名称 | 错误描述 |
|:---|------|
|    |      |

## 六、请求示例

json格式示例

```json
POST  http://ip:port/post?method=baiwang.output.invoice.exceptionOperation&version=1.0&requestId=53234020-2932-4012-a090-446cf1ae4f08

{
"data": {
"uploadResult": "",
"operateType":"0",
"invoiceNo": "",
"serialNo": ""
},
"taxNo": "91150202MA0Q17NK5H"
}
```

sdk调用示例

```java
String url = "http://ip:port/post";       //接口地址
try
        {
OutputInvoiceExceptionOperationRequest request = new OutputInvoiceExceptionOperationRequest();
OutputInvoiceExceptionOperationData data = new OutputInvoiceExceptionOperationData();
    data.

setUploadResult("");
    data.

setOperateType("0");
    data.

setInvoiceNo("");
    data.

setSerialNo("");
    request.

setData(data);
    request.

setTaxNo("91150202MA0Q17NK5H");

IBWClient client = new BWRestClient(url); // 初始化一个客户端
OutputInvoiceExceptionOperationResponse response = client.outputInvoice().exceptionOperation(request);
    if(response.

isSuccess()){
        System.out.

println(response.getModel());
        }else{
        System.out.

println(response.getMessage().

getErrorMessage());
        System.out.

println(response.getMessage().

getErrorCode());
        }
        }
        catch(
ISPException e){
        e.

printStackTrace();
}
```

## 七、响应示例

成功返回示例

```json
{
  "success": true,
  "requestId": "5c2bc8a3-1399-4ff0-a039-92688c6740e0",
  "model": [
    {
      "returnCode": "",
      "returnMsg": "",
      "serialNo": ""
    }
  ]
}
```

失败返回示例

```json
{
  "requestId": "53234020-2932-4012-a090-446cf1ae4f08",
  "success": false,
  "message": {
    "errorCode": "-1",
    "errorMessage": "No instances available for 10.100.3.2",
    "innerRequestId": ""
  }
}
```
