# *baiwang.output.redinvoice.redforminfo*

*红字信息详情查询接口出入参节点内容“长度”均为字节，税控发票业务字符集为GBK，一个中文汉字等于两个字节；数电发票业务字符集为UTF-8，一个中文汉字等于三个字节。（举例说明：参数长度限制12字节，税控发票转换汉字最多6个汉字，数电发票转换汉字最多4个汉字）*

## 一、请求公共参数

| 名称        | 类型     | 是否必须 | 描述         |
|-----------|--------|------|------------|
| method    | String | 是    | 接口指定唯一标识   |
| requestId | String | 是    | 请求唯一标识     |
| version   | String | 是    | 版本号，当前为1.0 |

## 二、请求业务参数

| ID             | 类型     | 长度 | 是否必须 | 描述                        |
|:---------------|:-------|:---|:-----|:--------------------------|
| taxNo          | String | 20 | 是    | 机构税号                      |
| taxUserName    | String | 20 | 否    | 电子税局登录账号                  |
| sellerTaxNo    | String | 20 | 是    | 销售方统一社会信用代码/纳税人识别号/身份证件号码 |
| redConfirmUuid | String | 32 | 是    | 红字确认单UUID                 |

备注：用 └ 标识层级关系

## 三、响应公共参数

| 名称               | 类型      | 描述                                 |
|------------------|---------|------------------------------------|
| success          | Boolean | 接口调用成功或失败的标志 true：成功，false：失败      |
| requestId        | String  | 请求唯一标识， 与调用接口时传入的requestId一致       |
| model            | <T>     | 业务接口调用成功后返回的具体响应信息，不同的接口返回的model不同 |
| message          | String  | 响应message                          |
| └ errorCode      | String  | 调用失败后，返回的错误码, 服务端错误码或业务层错误码        |
| └ errorMessage   | String  | 调用失败后，返回的错误信息                      |
| └ successMessage | String  | 调用成功后，返回的成功信息                      |
| └ innerRequestId | String  | 下游业务层的requestId，若为业务调用失败，则返回此字段    |

## 四、响应业务参数

| ID                        | 名称                       | 类型         | 长度   | 描述                                                                                                                                                                                                                  |
|---------------------------|--------------------------|------------|------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| redConfirmUuid            | redConfirmUuid           | String     | 32   | 红字确认单UUID                                                                                                                                                                                                           |
| redConfirmNo              | redConfirmNo             | String     | 20   | 红字确认单编号                                                                                                                                                                                                             |
| redConfirmSerialNo        | redConfirmSerialNo       | String     | 60   | 红字确认单流水号                                                                                                                                                                                                            |
| entryIdentity             | entryIdentity            | String     | 2    | 录入方身份 01:销方,02:购方                                                                                                                                                                                                   |
| sellerTaxNo               | sellerTaxNo              | String     | 20   | 销售方统一社会信用代码/纳税人识别号/身份证件号码                                                                                                                                                                                           |
| sellerName                | sellerName               | String     | 300  | 销售方名称                                                                                                                                                                                                               |
| buyerTaxNo                | buyerTaxNo               | String     | 20   | 购买方统一社会信用代码/纳税人识别号/身份证件号码                                                                                                                                                                                           |
| buyerName                 | buyerName                | String     | 300  | 购买方名称                                                                                                                                                                                                               |
| originalPaperInvoiceCode  | originalPaperInvoiceCode | String     | 12   | 纸质、税控发票代码                                                                                                                                                                                                           |
| originalPaperInvoiceNo    | originalPaperInvoiceNo   | String     | 8    | 纸质、税控发票号码                                                                                                                                                                                                           |
| originalInvoiceNo         | originalInvoiceNo        | String     | 20   | 全电蓝字发票号码                                                                                                                                                                                                            |
| originInvoiceIsPaper      | originInvoiceIsPaper     | String     | 1    | 是否纸质发票标志 Y :纸质发票 N:电子发票                                                                                                                                                                                             |
| originInvoiceDate         | originInvoiceDate        | String     | 19   | 蓝字发票开票日期 yyyy-MM-dd HH:mm:ss                                                                                                                                                                                        |
| originInvoiceTotalPrice   | originInvoiceTotalPrice  | BigDecimal | 18,2 | 蓝字发票金额                                                                                                                                                                                                              |
| originInvoiceTotalTax     | originInvoiceTotalTax    | BigDecimal | 18,2 | 蓝字发票税额                                                                                                                                                                                                              |
| originInvoiceType         | originInvoiceType        | String     | 2    | 蓝字发票票种代码 01: 增值税专用发票 02: 普通发票 03: 机动车统一销售发票 04: 二手车统一销售发票                                                                                                                                                           |
| originInvoiceSetCode      | originInvoiceSetCode     | String     | 2    | 蓝字发票特定要素类型代码 01：成品油发票 02：稀土发票 03：建筑服务发票 04：货物运输服务发票 05：不动产销售服务发票 06：不动产租赁服务发票 07：代收车船税 08：通行费 09：旅客运输服务发票 10：医疗服务（住院）发票 11：医疗服务（门诊）发票 12：自产农产品销售发票 13 拖拉机和联合收割机发票 14：机动车 15：二手车 16：农产品收购发票 17：光伏收购发票 18：卷烟发票；32：电子烟 |
| invoiceTotalPrice         | invoiceTotalPrice        | BigDecimal | 18,2 | 红字冲销金额                                                                                                                                                                                                              |
| invoiceTotalTax           | invoiceTotalTax          | BigDecimal | 18,2 | 红字冲销税额                                                                                                                                                                                                              |
| redInvoiceLabel           | redInvoiceLabel          | String     | 2    | 红字发票冲红原因代码 01：开票有误 02：销货退回 03：服务中止 04：销售折让                                                                                                                                                                          |
| confirmState              | confirmState             | String     | 2    | 红字确认单状态 01：无需确认；    02：销方录入待 购方确认；   03：购方录入待销方确认；    04：购销 双方已确认；05：作废（销方录入购方否认）； 06：作废（购方录入销方否认）；   07：作废（超 72小时未确认）；08：作废（发起方已撤销）； 09：作废（确认后撤销）；10： 作废（异常凭证）                                                     |
| confirmDate               | confirmDate              | String     | 19   | 确认日期 yyyy-MM-dd HH:mm:ss                                                                                                                                                                                            |
| alreadyRedInvoiceFlag     | alreadyRedInvoiceFlag    | String     | 1    | 已开具红字发票标记 Y:已开具,N:未开具                                                                                                                                                                                               |
| redInvoiceNo              | redInvoiceNo             | String     | 20   | 红字发票号码                                                                                                                                                                                                              |
| redInvoiceDate            | redInvoiceDate           | String     | 19   | 红字开票日期 yyyy-MM-dd HH:mm:ss                                                                                                                                                                                          |
| entryDate                 | entryDate                | String     | 19   | 录入日期 yyyy-MM-dd HH:mm:ss                                                                                                                                                                                            |
| modifyDate                | modifyDate               | String     | 19   | 修改日期 yyyy-MM-dd HH:mm:ss                                                                                                                                                                                            |
| validFlag                 | validFlag                | String     | 1    | 有效标志 Y：有效；N：无效                                                                                                                                                                                                      |
| taxationTypeCode          | taxationTypeCode         | String     | 2    | 差额征税类型代码 01：差额征税-全额开票；02：差额征税-差额开票                                                                                                                                                                                  |
| confirmBillingMark        | 税局确认即开标识                 | String     | 1    | Y：确认即开   N或空：非确认即开 连接器模式生效                                                                                                                                                                                          |
| electricInvoiceDetails    | electricInvoiceDetails   | List       | 1    | List&#60;OutputRedinvoiceRedforminfoObjectType>                                                                                                                                                                     |
| └ originalInvoiceDetailNo | originalInvoiceDetailNo  | Integer    | 8    | 蓝字发票明细序号                                                                                                                                                                                                            |
| └ goodsLineNo             | goodsLineNo              | Integer    | 8    | 序号                                                                                                                                                                                                                  |
| └ goodsCode               | goodsCode                | String     | 19   | 商品和服务税收分类合并编码                                                                                                                                                                                                       |
| └ goodsName               | goodsName                | String     | 300  | 货物或应税劳务、服务名称                                                                                                                                                                                                        |
| └ goodsSimpleName         | goodsSimpleName          | String     | 120  | 商品服务简称                                                                                                                                                                                                              |
| └ projectName             | projectName              | String     | 600  | 项目名称                                                                                                                                                                                                                |
| └ goodsSpecification      | goodsSpecification       | String     | 150  | 规格型号，乐企连接器最大长度限制150，Web连接器最大长度限制40，                                                                                                                                                                                 |
| └ goodsUnit               | goodsUnit                | String     | 300  | 单位，乐企连接器最大长度限制300，Web连接器最大长度限制22                                                                                                                                                                                    |
| └ goodsPrice              | goodsPrice               | BigDecimal | 25   | 单价，乐企连接器小数点前12位，小数点后13位，长度最大合计16位；Web连接器最大长度25位，整数部分不能超过12位、小数部分不能超过13位                                                                                                                                             |
| └ goodsQuantity           | goodsQuantity            | BigDecimal | 25   | 商品数量，乐企连接器小数点前12位，小数点后13位，长度最大合计16位；Web连接器最大长度25位，整数部分不能超过12位、小数部分不能超过13位                                                                                                                                           |
| └ goodsTotalPrice         | goodsTotalPrice          | BigDecimal | 18,2 | 金额                                                                                                                                                                                                                  |
| └ goodsTaxRate            | goodsTaxRate             | BigDecimal | 16,6 | 税率                                                                                                                                                                                                                  |
| └ goodsTotalTax           | goodsTotalTax            | BigDecimal | 18,2 | 税额                                                                                                                                                                                                                  |
| └ specialTaxCode          | specialTaxCode           | String     | 2    | 特定征税方式代码 01:不征税 02:零税率 03:差额开票 04:免税 05:简易计税 06:减按                                                                                                                                                                  |

## 五、错误码

| 名称    | 错误描述         |
|:------|--------------|
| 70169 | 参数{}不能为空     |
| 70036 | 红字录入单查询异常，{} |

## 六、请求示例

json格式示例

```json
POST  http://ip:port/post?method=baiwang.output.redinvoice.redforminfo&version=1.0&requestId=1874db71-35e8-4345-8c04-8f5f94203a97

{
"redConfirmUuid": "6df741db489945bcae761d0338087435",
"sellerTaxNo": "91440604570155821Y",
"taxNo": "91440604570155821Y",
"taxUserName": ""
}
```

sdk调用示例

```java
String url = "http://ip:port/post";       //接口地址
try
        {
OutputRedinvoiceRedforminfoRequest request = new OutputRedinvoiceRedforminfoRequest();
    request.

setRedConfirmUuid("6df741db489945bcae761d0338087435");
    request.

setSellerTaxNo("91440604570155821Y");
    request.

setTaxNo("91440604570155821Y");
    request.

setTaxUserName("");

IBWClient client = new BWRestClient(url); // 初始化一个客户端
OutputRedinvoiceRedforminfoResponse response = client.outputRedinvoice().redforminfo(request);
    if(response.

isSuccess()){
        System.out.

println(response.getModel());
        }else{
        System.out.

println(response.getMessage().

getErrorMessage());
        System.out.

println(response.getMessage().

getErrorCode());
        }
        }
        catch(
ISPException e){
        e.

printStackTrace();
}
```

## 七、响应示例

成功返回示例

```json
{
  "success": true,
  "requestId": "5c2bc8a3-1399-4ff0-a039-92688c6740e0",
  "model": [
    {
      "redConfirmUuid": "6df741db489945bcae761d0338087435",
      "originalPaperInvoiceCode": "",
      "originalPaperInvoiceNo": "",
      "validFlag": "Y",
      "sellerName": "销方单位名称，乐企连接器最大长度限制300，Web连接器最大长度限制100",
      "invoiceTotalTax": -41.4,
      "originInvoiceIsPaper": "N",
      "redInvoiceLabel": "01",
      "confirmBillingMark": "",
      "originInvoiceTotalTax": 41.4,
      "originInvoiceSetCode": "03",
      "electricInvoiceDetails": [
        {
          "goodsTaxRate": 0.09,
          "specialTaxCode": "",
          "goodsSimpleName": "建筑服务",
          "goodsTotalPrice": -460,
          "originalInvoiceDetailNo": 1,
          "goodsSpecification": "",
          "goodsPrice": 0,
          "goodsQuantity": 0,
          "goodsUnit": "",
          "goodsTotalTax": -41.4,
          "goodsCode": "3059900000000000000",
          "projectName": "建筑服务*建筑服务",
          "goodsName": "*建筑服务*建筑服务*建筑服务",
          "goodsLineNo": 1
        }
      ],
      "redConfirmNo": "44060422121001100222",
      "originInvoiceDate": "2022-12-27 18:53:59",
      "buyerTaxNo": "91510000621601108W",
      "modifyDate": "2022-12-30 15:42:06",
      "entryDate": "2022-12-29 11:11:32",
      "entryIdentity": "01",
      "confirmState": "08",
      "buyerName": "购方单位名称，乐企连接器最大长度限制300，Web连接器自然人最大长度限制96，非自然人最大长度限制100",
      "originalInvoiceNo": "22442000001000080951",
      "originInvoiceTotalPrice": 460,
      "redInvoiceDate": "",
      "confirmDate": "",
      "sellerTaxNo": "91440604570155821Y",
      "originInvoiceType": "01",
      "invoiceTotalPrice": -460,
      "redInvoiceNo": "",
      "taxationTypeCode": "",
      "alreadyRedInvoiceFlag": "N",
      "redConfirmSerialNo": "liushuihao"
    }
  ]
}
```

失败返回示例

```json
{
  "requestId": "1874db71-35e8-4345-8c04-8f5f94203a97",
  "success": false,
  "message": {
    "errorCode": "-1",
    "errorMessage": "No instances available for 10.100.3.2",
    "innerRequestId": ""
  }
}
```
