# *baiwang.output.redinvoice.formlist*

*红字列表查询接口
接口出入参节点内容“长度”均为字节，税控发票业务字符集为GBK，一个中文汉字等于两个字节；数电发票业务字符集为UTF-8，一个中文汉字等于三个字节。（举例说明：参数长度限制12字节，税控发票转换汉字最多6个汉字，数电发票转换汉字最多4个汉字）*

## 一、请求公共参数

| 名称        | 类型     | 是否必须 | 描述         |
|-----------|--------|------|------------|
| method    | String | 是    | 接口指定唯一标识   |
| requestId | String | 是    | 请求唯一标识     |
| version   | String | 是    | 版本号，当前为1.0 |

## 二、请求业务参数

| ID                       | 类型      | 长度 | 是否必须 | 描述                                                                                                                                                                                   |
|:-------------------------|:--------|:---|:-----|:-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| taxNo                    | String  | 20 | 是    | 机构税号                                                                                                                                                                                 |
| taxUserName              | String  | 20 | 否    | 电子税局登录账号                                                                                                                                                                             |
| redConfirmSerialNo       | String  | 60 | 否    | 红字确认单流水号                                                                                                                                                                             |
| buySelSelector           | String  | 1  | 是    | 购销方选择，0：销方 ；1：购方；2：二手车市场/二手车拍卖企业                                                                                                                                                     |
| entryIdentity            | String  | 2  | 否    | 录入方身份 01：销方；02：购方                                                                                                                                                                    |
| buyerTaxNo               | String  | 20 | 否    | 购买方纳税人识别号，购销方角色为【购方】时，必填                                                                                                                                                             |
| sellerTaxNo              | String  | 20 | 否    | 销售方纳税人识别号，购销方角色为【销方】时，必填；                                                                                                                                                            |
| invoiceStartDate         | String  | 20 | 否    | 录入日期始 yyyy-MM-dd，当web连接器时必填；                                                                                                                                                         |
| invoiceEndDate           | String  | 20 | 否    | 录入日期止 yyyy-MM-dd，当web连接器时必填；                                                                                                                                                         |
| originalInvoiceNo        | String  | 20 | 否    | 蓝字发票号码，传入该值，只会查询本地数据，不会查询税局实时数据                                                                                                                                                      |
| originalPaperInvoiceCode | String  | 12 | 否    | 纸质发票代码，传入该值，只会查询本地数据，不会查询税局实时数据                                                                                                                                                      |
| originalPaperInvoiceNo   | String  | 8  | 否    | 纸质发票号码，传入该值，只会查询本地数据，不会查询税局实时数据                                                                                                                                                      |
| redConfirmNo             | String  | 20 | 否    | 红字确认单编号，传入该值，只会查询本地数据，不会查询税局实时数据                                                                                                                                                     |
| confirmState             | String  | 2  | 否    | 红字确认单状态 01：无需确认；    02：销方录入待购方确认； 03：购方录入待销方确认；    04：购销双方已确认；    05：作废（销方录入购方否认）；   06：作废（购方录入销方否认）；   07：作废 （超72小时未确认）；   08：作废（发起方已撤销）；   09：作废 （确认后撤销）【web连接器支持09码】；10： 作废（异常凭证） |
| pageNo                   | Integer | 8  | 是    | 页数                                                                                                                                                                                   |
| pageSize                 | Integer | 8  | 是    | 每页数量，最大50条每页                                                                                                                                                                         |

备注：用 └ 标识层级关系

## 三、响应公共参数

| 名称               | 类型      | 描述                                 |
|------------------|---------|------------------------------------|
| success          | Boolean | 接口调用成功或失败的标志 true：成功，false：失败      |
| requestId        | String  | 请求唯一标识， 与调用接口时传入的requestId一致       |
| model            | <T>     | 业务接口调用成功后返回的具体响应信息，不同的接口返回的model不同 |
| message          | String  | 响应message                          |
| └ errorCode      | String  | 调用失败后，返回的错误码, 服务端错误码或业务层错误码        |
| └ errorMessage   | String  | 调用失败后，返回的错误信息                      |
| └ successMessage | String  | 调用成功后，返回的成功信息                      |
| └ innerRequestId | String  | 下游业务层的requestId，若为业务调用失败，则返回此字段    |

## 四、响应业务参数

| ID                       | 名称                      | 类型         | 长度   | 描述                                                                                                                                                                                                                  |
|--------------------------|-------------------------|------------|------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| redConfirmUuid           | redConfirmUuid          | String     | 32   | 红字确认单uuid                                                                                                                                                                                                           |
| redConfirmNo             | redConfirmNo            | String     | 20   | 红字确认单编号                                                                                                                                                                                                             |
| redConfirmSerialNo       | redConfirmSerialNo      | String     | 60   | 红字确认单流水号                                                                                                                                                                                                            |
| entryIdentity            | entryIdentity           | String     | 2    | 录入方身份 01:销方,02:购方,03：二手车市场/二手车拍卖企业                                                                                                                                                                                  |
| sellerTaxNo              | sellerTaxNo             | String     | 20   | （销售方）统一社会信用代码/纳税人识别号/身份证件号码                                                                                                                                                                                         |
| sellerName               | sellerName              | String     | 300  | 销方单位名称，乐企连接器最大长度限制300，Web连接器最大长度限制100                                                                                                                                                                               |
| buyerTaxNo               | buyerTaxNo              | String     | 20   | （购买方）统一社会信用代码/纳税人识别号/身份证件号码                                                                                                                                                                                         |
| buyerName                | buyerName               | String     | 300  | 购方单位名称，乐企连接器最大长度限制300，Web连接器自然人最大长度限制96，非自然人最大长度限制100                                                                                                                                                               |
| originalPaperInvoiceCode | originalInvoiceCode     | String     | 12   | 纸质发票代码、税控发票代码                                                                                                                                                                                                       |
| originalPaperInvoiceNo   | originalInvoiceNo       | String     | 8    | 纸质发票号码、税控发票号码                                                                                                                                                                                                       |
| originalInvoiceNo        | originalInvoiceNo       | String     | 20   | 全电蓝字发票号码                                                                                                                                                                                                            |
| originInvoiceIsPaper     | originInvoiceIsPaper    | String     | 1    | 是否纸质发票标志 Y :纸质发票 N:电子发票                                                                                                                                                                                             |
| originInvoiceDate        | originInvoiceDate       | String     | 19   | 蓝字发票开票日期 yyyy-MM-dd HH:mm:ss                                                                                                                                                                                        |
| originInvoiceTotalPrice  | originInvoiceTotalPrice | BigDecimal | 18,2 | 蓝字发票金额                                                                                                                                                                                                              |
| originInvoiceTotalTax    | originInvoiceTotalTax   | BigDecimal | 18,2 | 蓝字发票税额                                                                                                                                                                                                              |
| originInvoiceType        | originInvoiceType       | String     | 2    | 蓝字发票票种代码 01: 增值税专用发票 02: 普通发票 03: 机动车统一销售发票 04: 二手车统一销售发票                                                                                                                                                           |
| originInvoiceSetCode     | originInvoiceSetCode    | String     | 2    | 蓝字发票特定要素类型代码 01：成品油发票 02：稀土发票 03：建筑服务发票 04：货物运输服务发票 05：不动产销售服务发票 06：不动产租赁服务发票 07：代收车船税 08：通行费 09：旅客运输服务发票 10：医疗服务（住院）发票 11：医疗服务（门诊）发票 12：自产农产品销售发票 13 拖拉机和联合收割机发票 14：机动车 15：二手车 16：农产品收购发票 17：光伏收购发票 18：卷烟发票 32：电子烟 |
| invoiceTotalPrice        | invoiceTotalPrice       | BigDecimal | 18,2 | 红字冲销金额                                                                                                                                                                                                              |
| invoiceTotalTax          | invoiceTotalTax         | BigDecimal | 18,2 | 红字冲销税额                                                                                                                                                                                                              |
| redInvoiceLabel          | redInvoiceLabel         | String     | 2    | 红字发票冲红原因代码 01：开票有误 02：销货退回 03：服务中止 04：销售折让                                                                                                                                                                          |
| confirmState             | confirmState            | String     | 2    | 红字确认单状态 01：无需确认； 02：销方录入待购方确认； 03：购方录入待销方确认； 04：购销双方已确认； 05：作废（销方录入购方否认）； 06：作废（购方录入销方否认）； 07：作废（超72小时未确认）； 08：作废（发起方已撤销）； 09：作废（确认后撤销）                                                                             |
| confirmDate              | confirmDate             | String     | 19   | 确认日期 yyyy-MM-dd HH:mm:ss                                                                                                                                                                                            |
| alreadyRedInvoiceFlag    | alreadyRedInvoiceFlag   | String     | 1    | 已开具红字发票标记 Y:已开具,N:未开具                                                                                                                                                                                               |
| redInvoiceNo             | redInvoiceNo            | String     | 20   | 红字发票号码                                                                                                                                                                                                              |
| redInvoiceDate           | redInvoiceDate          | String     | 1    | 红字开票日期 yyyy-MM-dd HH:mm:ss                                                                                                                                                                                          |
| entryDate                | entryDate               | String     | 19   | 录入日期 yyyy-MM-dd HH:mm:ss                                                                                                                                                                                            |
| modifyDate               | modifyDate              | String     | 19   | 修改日期 yyyy-MM-dd HH:mm:ss                                                                                                                                                                                            |
| validFlag                | validFlag               | String     | 1    | 有效标志 Y：有效；N：无效                                                                                                                                                                                                      |
| taxationTypeCode         | taxationTypeCode        | String     | 2    | 差额征税类型代码 01：差额征税-全额开票；02：差额征税-差额开票                                                                                                                                                                                  |
| confirmBillingMark       | 税局确认即开标识                | String     | 1    | Y：确认即开   N或空：非确认即开; 连接器开票用                                                                                                                                                                                          |

## 五、错误码

| 名称    | 错误描述         |
|:------|--------------|
| 70169 | 参数{}不能为空     |
| 70170 | 参数{}不正确      |
| 70036 | 红字录入单查询异常，{} |

## 六、请求示例

json格式示例

```json
POST  http://ip:port/post?method=baiwang.output.redinvoice.formlist&version=1.0&requestId=c2773b7f-6f13-46ec-8256-bd6e5a8d3ee5

{
"originalPaperInvoiceCode": "",
"originalPaperInvoiceNo": "",
"buyerTaxNo": "",
"entryIdentity": "01",
"confirmState": "",
"pageSize": 10,
"buySelSelector": "0",
"originalInvoiceNo": "",
"invoiceStartDate": "2022-12-27",
"sellerTaxNo": "91150202MA0Q17NK5H",
"pageNo": 1,
"redConfirmNo": "",
"taxNo": "91150202MA0Q17NK5H",
"invoiceEndDate": "2022-12-28",
"taxUserName": "",
"redConfirmSerialNo": ""
}
```

sdk调用示例

```java
String url = "http://ip:port/post";       //接口地址
try
        {
OutputRedinvoiceFormlistRequest request = new OutputRedinvoiceFormlistRequest();
    request.

setOriginalPaperInvoiceCode("");
    request.

setOriginalPaperInvoiceNo("");
    request.

setBuyerTaxNo("");
    request.

setEntryIdentity("01");
    request.

setConfirmState("");
    request.

setPageSize(10);
    request.

setBuySelSelector("0");
    request.

setOriginalInvoiceNo("");
    request.

setInvoiceStartDate("2022-12-27");
    request.

setSellerTaxNo("91150202MA0Q17NK5H");
    request.

setPageNo(1);
    request.

setRedConfirmNo("");
    request.

setTaxNo("91150202MA0Q17NK5H");
    request.

setInvoiceEndDate("2022-12-28");
    request.

setTaxUserName("");
    request.

setRedConfirmSerialNo("");

IBWClient client = new BWRestClient(url); // 初始化一个客户端
OutputRedinvoiceFormlistResponse response = client.outputRedinvoice().formlist(request);
    if(response.

isSuccess()){
        System.out.

println(response.getModel());
        }else{
        System.out.

println(response.getMessage().

getErrorMessage());
        System.out.

println(response.getMessage().

getErrorCode());
        }
        }
        catch(
ISPException e){
        e.

printStackTrace();
}
```

## 七、响应示例

成功返回示例

```json
{
  "success": true,
  "requestId": "5c2bc8a3-1399-4ff0-a039-92688c6740e0",
  "model": [
    {
      "redConfirmUuid": "ffbb7d3c6c284e4e806a4e46e44f2866",
      "originalPaperInvoiceCode": "",
      "originalPaperInvoiceNo": "",
      "validFlag": "Y",
      "sellerName": "包头市乾芮医药连锁有限责任公司站北路店",
      "invoiceTotalTax": -0.09,
      "originInvoiceIsPaper": "",
      "redInvoiceLabel": "01",
      "confirmBillingMark": "",
      "originInvoiceTotalTax": 0.09,
      "originInvoiceSetCode": "",
      "redConfirmNo": "15020222121000900041",
      "originInvoiceDate": "2022-12-13 16:39:38",
      "buyerTaxNo": "",
      "modifyDate": "2022-12-28 18:35:36",
      "entryDate": "2022-12-28 18:35:35",
      "entryIdentity": "01",
      "confirmState": "01",
      "buyerName": "广州市海珠区雅坞互动设计室",
      "originalInvoiceNo": "22157000000001278815",
      "originInvoiceTotalPrice": 1.0,
      "redInvoiceDate": "",
      "confirmDate": "",
      "sellerTaxNo": "91150202MA0Q17NK5H",
      "originInvoiceType": "02",
      "invoiceTotalPrice": -1.0,
      "redInvoiceNo": "",
      "taxationTypeCode": "",
      "alreadyRedInvoiceFlag": "N",
      "redConfirmSerialNo": "liushuihao"
    }
  ]
}
```

失败返回示例

```json
{
  "requestId": "c2773b7f-6f13-46ec-8256-bd6e5a8d3ee5",
  "success": false,
  "message": {
    "errorCode": "-1",
    "errorMessage": "No instances available for 10.100.3.2",
    "innerRequestId": ""
  }
}
```
