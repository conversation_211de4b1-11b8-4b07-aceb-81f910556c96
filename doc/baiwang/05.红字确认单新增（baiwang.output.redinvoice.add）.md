# *baiwang.output.redinvoice.add*

*全电红字确认单申请*

## 一、请求公共参数

| 名称        | 类型     | 是否必须 | 描述         |
|-----------|--------|------|------------|
| method    | String | 是    | 接口指定唯一标识   |
| requestId | String | 是    | 请求唯一标识     |
| version   | String | 是    | 版本号，当前为1.0 |

## 二、请求业务参数

| ID                            | 类型         | 长度   | 是否必须 | 描述                                                                                                                                                                                                                                    |
|:------------------------------|:-----------|:-----|:-----|:--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| taxNo                         | String     | 20   | 是    | 机构税号                                                                                                                                                                                                                                  |
| orgCode                       | String     | 64   | 否    | 组织机构编码，如果为空则上传至税号对应的机构下，如果维护了则按照组织机构编码获取销方信息                                                                                                                                                                                          |
| taxUserName                   | String     | 20   | 否    | 电子税局登录账号，WEB连接器开票人                                                                                                                                                                                                                    |
| drawer                        | String     | 20   | 否    | 乐企开票人                                                                                                                                                                                                                                 |
| redConfirmSerialNo            | String     | 60   | 是    | 红字确认单流水号,调用方传递                                                                                                                                                                                                                        |
| entryIdentity                 | String     | 2    | 是    | 录入方身份 01:销方,02:购方,03:二手车市场/拍卖企业                                                                                                                                                                                                       |
| sellerTaxNo                   | String     | 20   | 是    | 销售方统一社会信用代码/纳税人识别号/身份证件号码                                                                                                                                                                                                             |
| sellerTaxName                 | String     | 300  | 是    | 销售方名称                                                                                                                                                                                                                                 |
| deliverFlag                   | String     | 1    | 否    | 1：使用蓝票邮箱、手机号交付；0：使用入参中的buyerEmail、buyerPhone交付，buyerEmail、buyerPhone为空则不交付；                                                                                                                                                           |
| buyerEmail                    | String     | 100  | 否    | 红票客户邮箱                                                                                                                                                                                                                                |
| buyerPhone                    | String     | 20   | 否    | 红票客户手机号                                                                                                                                                                                                                               |
| buyerTaxNo                    | String     | 20   | 否    | 购买方统一社会信用代码/纳税人识别号/身份证件号码, 录入方身份为【购方】时必填                                                                                                                                                                                              |
| buyerTaxName                  | String     | 300  | 是    | 购买方名称                                                                                                                                                                                                                                 |
| redInvoiceIsPaper             | String     | 1    | 否    | 数电票票种（仅支持WEB连接器），N电子发票，Y纸质发票 ，默认N，只有原蓝票为纸质发票时，此字段生效；机动车统一销售发票、二手车统一销售发票，默认Y，目前不支持N；                                                                                                                                                   |
| originInvoiceIsPaper          | String     | 2    | 是    | 是否纸质发票标志 Y：纸质发票 N：电子发票                                                                                                                                                                                                                |
| originalInvoiceNo             | String     | 20   | 否    | 蓝字发票全电发票号码，【发票来源】为2时必填                                                                                                                                                                                                                |
| originalPaperInvoiceCode      | String     | 12   | 否    | 纸质、税控发票代码，【发票来源】为1时或全电纸票时必填                                                                                                                                                                                                           |
| originalPaperInvoiceNo        | String     | 8    | 否    | 纸质、税控发票号码，【发票来源】为1时或全电纸票时必填                                                                                                                                                                                                           |
| originInvoiceDate             | String     | 19   | 是    | 蓝字发票开票日期 yyyy-MM-dd HH:mm:ss                                                                                                                                                                                                          |
| originInvoiceTotalPrice       | BigDecimal | 18,2 | 是    | 蓝字发票合计金额                                                                                                                                                                                                                              |
| originInvoiceTotalTax         | BigDecimal | 18,2 | 是    | 蓝字发票合计税额                                                                                                                                                                                                                              |
| originInvoiceType             | String     | 2    | 是    | 蓝字发票票种代码 01:增值税专用发票 02:普通发票 03:机动车统一销售发票 04:二手车统一销售发票                                                                                                                                                                                 |
| originInvoiceSetCode          | String     | 2    | 否    | 蓝字发票特定要素类型代码 01：成品油发票 02：稀土发票 03：建筑服务发票 04：货物运输服务发票 05：不动产销售服务发票 06：不动产租赁服务发票 07：代收车船税 08：通行费 09：旅客运输服务发票 10：医疗服务（住院）发票 11：医疗服务（门诊）发票 12：自产农产品销售发票 13 拖拉机和联合收割机发票 14：机动车 15：二手车 16：农产品收购发票 17：光伏收购发票 18：卷烟发票 31：二手车* 24：报废产品收购；32：电子烟 |
| autoIssueSwitch               | String     | 2    | 否    | 非确认即开—自动开票； Y：自动开票； N：不自动开票，默认为N；红字确认单需确认时，对方确认完成，百望系统将自动开红票，WEB连接器和乐企均支持；乐企自动开票为Y时，默认开票人（云开票配置处配置）不能为空，否则无法自动开票；场景说明：当税局“确认即开票“标识为”否“时，可通过传入此字段确定是否自动开票。                                                                             |
| invoiceTotalPrice             | BigDecimal | 18,2 | 是    | 红字冲销金额                                                                                                                                                                                                                                |
| invoiceTotalTax               | BigDecimal | 18,2 | 是    | 红字冲销税额                                                                                                                                                                                                                                |
| redInvoiceLabel               | String     | 2    | 是    | 红字发票冲红原因代码 01:开票有误 02:销货退回 03:服务中止 04:销售折让。二手车销售统一发票仅可使用01、02                                                                                                                                                                         |
| invoiceSource                 | String     | 2    | 是    | 发票来源：全电平台红冲必须要传递的字段 1:增值税发票管理系统：表示此发票是通过原税控系统开具的增值税发票，红冲此类发票时，税控设备需注销后才可以申请全电的红字确认单；  2:电子发票服务平台：表示此发票是通过电子发票服务平台开具的全电发票（包括全电纸质发票），红冲此类发票时需要传递蓝票属性为此；                                                                                 |
| priceTaxMark                  | String     | 1    | 否    | 含税标志，0-不含税、1-含税，默认为0                                                                                                                                                                                                                  |
| redConfirmDetailReqEntityList | List       | 1    | 是    | List&#60;OutputRedinvoiceAddObjectType>, 明细信息                                                                                                                                                                                         |
| └ originalInvoiceDetailNo     | Integer    | 8    | 是    | 蓝字发票明细序号                                                                                                                                                                                                                              |
| └ goodsLineNo                 | Integer    | 8    | 是    | 序号                                                                                                                                                                                                                                    |
| └ goodsCode                   | String     | 19   | 是    | 税收分类编码(商品和服务税收分类合并编码)                                                                                                                                                                                                                 |
| └ goodsName                   | String     | 300  | 否    | 商品全称（ *简称*自定义名称）                                                                                                                                                                                                                      |
| └ goodsSimpleName             | String     | 120  | 否    | 商品服务简称(税收分类简称)                                                                                                                                                                                                                        |
| └ projectName                 | String     | 600  | 是    | 项目名称(自定义商品名称)                                                                                                                                                                                                                         |
| └ goodsSpecification          | String     | 150  | 否    | 规格型号，也可代表车架号/车辆识别号                                                                                                                                                                                                                    |
| └ goodsUnit                   | String     | 300  | 否    | 单位                                                                                                                                                                                                                                    |
| └ goodsPrice                  | String     | 25   | 否    | 不含税单价，当含税标志为0-不含税时，如需要，传此值                                                                                                                                                                                                            |
| └ goodsQuantity               | String     | 25   | 否    | 数量                                                                                                                                                                                                                                    |
| └ goodsTaxRate                | BigDecimal | 16,6 | 是    | 税率                                                                                                                                                                                                                                    |
| └ goodsTotalPrice             | BigDecimal | 18,2 | 条件必填 | 不含税金额，当含税标志为0-不含税时，传此值                                                                                                                                                                                                                |
| └ goodsTotalTax               | BigDecimal | 18,2 | 是    | 税额                                                                                                                                                                                                                                    |
| └ goodsPriceTax               | BigDecimal | 18,2 | 否    | 含税单价，当含税标志为1-含税时，如需要，传此值                                                                                                                                                                                                              |
| └ goodsTotalPriceTax          | BigDecimal | 18,2 | 条件必填 | 含税金额，当含税标志为1-含税时，传此值                                                                                                                                                                                                                  |

备注：用 └ 标识层级关系

## 三、响应公共参数

| 名称               | 类型      | 描述                                 |
|------------------|---------|------------------------------------|
| success          | Boolean | 接口调用成功或失败的标志 true：成功，false：失败      |
| requestId        | String  | 请求唯一标识， 与调用接口时传入的requestId一致       |
| model            | <T>     | 业务接口调用成功后返回的具体响应信息，不同的接口返回的model不同 |
| message          | String  | 响应message                          |
| └ errorCode      | String  | 调用失败后，返回的错误码, 服务端错误码或业务层错误码        |
| └ errorMessage   | String  | 调用失败后，返回的错误信息                      |
| └ successMessage | String  | 调用成功后，返回的成功信息                      |
| └ innerRequestId | String  | 下游业务层的requestId，若为业务调用失败，则返回此字段    |

## 四、响应业务参数

| ID                 | 名称                 | 类型     | 长度 | 描述                                                                                                                                          |
|--------------------|--------------------|--------|----|---------------------------------------------------------------------------------------------------------------------------------------------|
| redConfirmSerialNo | redConfirmSerialNo | String | 60 | 红字确认单流水号                                                                                                                                    |
| redConfirmNo       | redConfirmNo       | String | 20 | 红字确认单编号                                                                                                                                     |
| redConfirmUuid     | redConfirmUuid     | String | 32 | 红字确认单UUID                                                                                                                                   |
| confirmState       | confirmState       | String | 2  | 红字确认单状态 01：无需确认；02：销方录入待购方确认；03：购方录入待销方确认；04：购销双方已确认；05：作废（销方录入购方否认）；06：作废（购方录入销方否认）；07：作废（超72 小 时 未 确认）；08：（发起方撤销）；09：作废（确认后撤销）；10 作废（异常凭证 |
| redInvoiceNo       | redInvoiceNo       | String | 20 | 红字发票号码，红字确认单符合直接开票条件时会直接返回开出的红字发票的发票号码                                                                                                      |
| confirmBillingMark | 税局确认即开标志           | String | 1  | Y：确认即开   N或空：非确认即开;  连接器开票用                                                                                                                 |

## 五、错误码

| 名称    | 错误描述         |
|:------|--------------|
| 70169 | 参数{}不能为空     |
| 70170 | 参数{}不正确      |
| 70035 | 红字录入单操作异常，{} |
| 70166 | 乐企红字申请失败:{}  |

## 六、请求示例

json格式示例

```json
POST  http://ip:port/post?method=baiwang.output.redinvoice.add&version=1.0&requestId=7eab30cb-f113-4571-941c-4df8f3f7b186

{
"originalPaperInvoiceCode": "",
"priceTaxMark": "1",
"redConfirmDetailReqEntityList":[
{
"goodsTaxRate": 0.09,
"goodsTotalPriceTax": 0,
"goodsSimpleName": "谷物",
"goodsTotalPrice": -1.0,
"goodsPriceTax": 0,
"originalInvoiceDetailNo": 1,
"goodsSpecification": "1",
"goodsPrice": "1.0000000000000000000",
"goodsQuantity": "-1",
"goodsUnit": "元",
"goodsTotalTax": -0.09,
"goodsCode": "1010101010000000000",
"projectName": "大米",
"goodsName": "*谷物*大米",
"goodsLineNo": 1
}
],
"originalPaperInvoiceNo": "",
"buyerEmail": "",
"autoIssueSwitch": "N",
"invoiceTotalTax": -0.09,
"originInvoiceIsPaper": "N",
"redInvoiceLabel": "01",
"originInvoiceTotalTax": 0.09,
"originInvoiceSetCode": "",
"orgCode": "",
"sellerTaxName": "包头市乾芮医药连锁有限责任公司站北路店",
"deliverFlag": "1",
"taxNo": "91150202MA0Q17NK5H",
"originInvoiceDate": "2022-12-13 16:39:38",
"redInvoiceIsPaper": "",
"buyerTaxNo": "",
"entryIdentity": "01",
"drawer": "",
"originalInvoiceNo": "22157000000001278815",
"invoiceSource": "2",
"originInvoiceTotalPrice": 1.0,
"sellerTaxNo": "91150202MA0Q17NK5H",
"buyerPhone": "",
"originInvoiceType": "02",
"invoiceTotalPrice": -1.0,
"buyerTaxName": "广州市海珠区雅坞互动设计室",
"taxUserName": "",
"redConfirmSerialNo": "liushuihao"
}
```

sdk调用示例

```java
String url = "http://ip:port/post";       //接口地址
try
        {
OutputRedinvoiceAddRequest request = new OutputRedinvoiceAddRequest();
    request.

setOriginalPaperInvoiceCode("");
    request.

setPriceTaxMark("1");

List<OutputRedinvoiceAddObjectType> redConfirmDetailReqEntityList = new ArrayList<OutputRedinvoiceAddObjectType>();
OutputRedinvoiceAddObjectType outputRedinvoiceAddObjectType = new OutputRedinvoiceAddObjectType();
    outputRedinvoiceAddObjectType.

setGoodsTaxRate(BigDecimal.valueOf(0.09));
        outputRedinvoiceAddObjectType.

setGoodsTotalPriceTax(BigDecimal.valueOf(0.0));
        outputRedinvoiceAddObjectType.

setGoodsSimpleName("\u8C37\u7269");
    outputRedinvoiceAddObjectType.

setGoodsTotalPrice(BigDecimal.valueOf(-1.0));
        outputRedinvoiceAddObjectType.

setGoodsPriceTax(BigDecimal.valueOf(0.0));
        outputRedinvoiceAddObjectType.

setOriginalInvoiceDetailNo(1);
    outputRedinvoiceAddObjectType.

setGoodsSpecification("1");
    outputRedinvoiceAddObjectType.

setGoodsPrice("1.0000000000000000000");
    outputRedinvoiceAddObjectType.

setGoodsQuantity("-1");
    outputRedinvoiceAddObjectType.

setGoodsUnit("\u5143");
    outputRedinvoiceAddObjectType.

setGoodsTotalTax(BigDecimal.valueOf(-0.09));
        outputRedinvoiceAddObjectType.

setGoodsCode("1010101010000000000");
    outputRedinvoiceAddObjectType.

setProjectName("\u5927\u7C73");
    outputRedinvoiceAddObjectType.

setGoodsName("*\u8C37\u7269*\u5927\u7C73");
    outputRedinvoiceAddObjectType.

setGoodsLineNo(1);
    redConfirmDetailReqEntityList.

add(outputRedinvoiceAddObjectType);
    request.

setRedConfirmDetailReqEntityList(redConfirmDetailReqEntityList);
    request.

setOriginalPaperInvoiceNo("");
    request.

setBuyerEmail("");
    request.

setAutoIssueSwitch("N");
    request.

setInvoiceTotalTax(BigDecimal.valueOf(-0.09));
        request.

setOriginInvoiceIsPaper("N");
    request.

setRedInvoiceLabel("01");
    request.

setOriginInvoiceTotalTax(BigDecimal.valueOf(0.09));
        request.

setOriginInvoiceSetCode("");
    request.

setOrgCode("");
    request.

setSellerTaxName("\u5305\u5934\u5E02\u4E7E\u82AE\u533B\u836F\u8FDE\u9501\u6709\u9650\u8D23\u4EFB\u516C\u53F8\u7AD9\u5317\u8DEF\u5E97");
    request.

setDeliverFlag("1");
    request.

setTaxNo("91150202MA0Q17NK5H");
    request.

setOriginInvoiceDate("2022-12-13 16:39:38");
    request.

setRedInvoiceIsPaper("");
    request.

setBuyerTaxNo("");
    request.

setEntryIdentity("01");
    request.

setDrawer("");
    request.

setOriginalInvoiceNo("22157000000001278815");
    request.

setInvoiceSource("2");
    request.

setOriginInvoiceTotalPrice(BigDecimal.valueOf(1.0));
        request.

setSellerTaxNo("91150202MA0Q17NK5H");
    request.

setBuyerPhone("");
    request.

setOriginInvoiceType("02");
    request.

setInvoiceTotalPrice(BigDecimal.valueOf(-1.0));
        request.

setBuyerTaxName("\u5E7F\u5DDE\u5E02\u6D77\u73E0\u533A\u96C5\u575E\u4E92\u52A8\u8BBE\u8BA1\u5BA4");
    request.

setTaxUserName("");
    request.

setRedConfirmSerialNo("liushuihao");

IBWClient client = new BWRestClient(url); // 初始化一个客户端
OutputRedinvoiceAddResponse response = client.outputRedinvoice().add(request);
    if(response.

isSuccess()){
        System.out.

println(response.getModel());
        }else{
        System.out.

println(response.getMessage().

getErrorMessage());
        System.out.

println(response.getMessage().

getErrorCode());
        }
        }
        catch(
ISPException e){
        e.

printStackTrace();
}
```

## 七、响应示例

成功返回示例

```json
{
  "success": true,
  "requestId": "5c2bc8a3-1399-4ff0-a039-92688c6740e0",
  "model": [
    {
      "redConfirmUuid": "6df741db489945bcae761d0338087435",
      "redInvoiceNo": "23442000000063809100",
      "confirmState": "01",
      "redConfirmNo": "44060422121001100222",
      "confirmBillingMark": "",
      "redConfirmSerialNo": "liushuihao"
    }
  ]
}
```

失败返回示例

```json
{
  "requestId": "7eab30cb-f113-4571-941c-4df8f3f7b186",
  "success": false,
  "message": {
    "errorCode": "-1",
    "errorMessage": "No instances available for 10.100.3.2",
    "innerRequestId": ""
  }
}
```
