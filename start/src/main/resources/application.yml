spring:
  application:
    name: invoice-service
  profiles:
    active: dev
    include: actuator, druid, mybatis-flex
xtc:
  cors:
    allow-methods: "GET,PUT,POST,DELETE,OPTIONS"
    allow-headers: "Accept,Content-Type,Content-Length,Accept-Encoding,Authorization"
    allow-origin-patterns: "http://localhost:*,http://127.0.0.1:*,https://*.okii.com"
  feign:
    client:
      invoice-service:
        url: http://invoice-service.marketing-invoice-env-test:8080