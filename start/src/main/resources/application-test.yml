spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: WakKcUOV3565CVNL6fFv
    url: jdbc:mysql://***********:3306/invoice_service?useUnicode=true&characterEncoding=utf-8&autoReconnect=true&serverTimezone=GMT%2B8&allowMultiQueries=true
  data:
    redis:
      host: ************
      port: 30276
      password: bSpcWTb9cmhkn6fXfDlD
minio:
  endpoint: https://eds-prod-2.okii.com:12000
  access-key: BN1KWRIGIBJBWHAV574Z
  secret-key: 0zk6nMfpJrY85uDx8Ahd06DDjLwqwPK9zZNYepDH
xxl:
  job:
    access-token: sHOvbDODulPB6OTw5rWTR7Hlf9MSlHVul2_q0hvXp263QqDYnF8PjFHEZ6L6B_SK
    admin:
      addresses: http://xxl-job.marketing-component-env-test:8080/xxl-job-admin/
