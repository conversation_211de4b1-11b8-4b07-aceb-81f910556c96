management:
  # actuator 启用独立端口
  server:
    port: 8099
    basePath: ""
  # 应用指标，配合 prometheus 使用
  metrics:
    tags:
      application: ${spring.application.name}
  # 开启 health 端点
  endpoint:
    health:
      probes:
        enabled: true
  # 暴露端点
  endpoints:
    web:
      basePath: "/actuator"
      exposure:
        include: health,metrics,prometheus

server:
  # 默认 IMMEDIATE 表示立即关机，GRACEFUL 表示优雅关机，不接受新的请求，等待所有请求处理完成后关机，默认超时时间 30 秒
  shutdown: GRACEFUL