spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: marketing_invoice_user
    password: FebV0gNpkhnJlNVQUPjC
    url: *********************************************************************************************************************************************************
  data:
    redis:
      host: ************
      port: 6379
      password: gAHcgPNDsFg9A5pIS0q1
minio:
  endpoint: https://eds-prod-2.okii.com:12000
  access-key: 1VAA8EETYYVWNT9IPSA7
  secret-key: NGO5zl2nOaKQ8YrfKMBt6yXwUp24ggh8o7TH6lxg
xxl:
  job:
    access-token: O14vJg9vDsk2wtQxav1F20JJ8oGNyHj-_hqAOeCOco0zKpomRe3NhjfyfSboCk1Z
    admin:
      addresses: http://xxl-job-3.marketing-component-env-internal:8080/v3/xxl-job-admin/
arthas:
  tunnel-server: ws://arthas-tunnel-server.marketing-component-env-internal:7777/ws
