package com.xtc.marketing.invoiceservice.job;

import com.xtc.marketing.invoiceservice.Application;
import com.xtc.marketing.invoiceservice.constant.SystemConstant;
import com.xtc.marketing.invoiceservice.config.BaseJobBO;
import com.xtc.marketing.invoiceservice.config.BaseJobExe;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.slf4j.MDC;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Random;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.IntStream;

/**
 * BaseJobExe虚拟线程测试类
 * 测试Java 21虚拟线程的并发处理和中断处理功能
 */
@Slf4j
@SpringBootTest(classes = Application.class)
@ActiveProfiles("dev")
class BaseJobExeTest {

    /**
     * 测试虚拟线程日志追踪
     * 验证是否可以使用dataId查询到顺序日志
     */
    @Test
    void testVirtualThreadLog() throws Exception {
        // 设置主线程的trace.id
        String traceId = UUID.randomUUID().toString().replace("-", "");
        MDC.put(SystemConstant.MDC_TRACE_ID, traceId);
        log.info("设置主线程trace.id: {}", traceId);

        try {
            // 创建测试参数
            TestJobParam param = new TestJobParam();
            param.setStartTime(LocalDateTime.now().minusDays(1));
            param.setEndTime(LocalDateTime.now());

            // 用于追踪处理的数据数量
            AtomicInteger processedCount = new AtomicInteger(0);
            AtomicInteger failedCount = new AtomicInteger(0);

            // 执行任务
            log.info("开始执行虚拟线程测试...");
            BaseJobExe.execute(
                    param,
                    TestData::getId, // 数据ID提取函数
                    () -> generateTestData(10),
                    data -> { // 数据处理函数
                        processData(data);
                        processedCount.incrementAndGet();
                    },
                    (data, e) -> { // 错误处理函数
                        handleError(data, e);
                        failedCount.incrementAndGet();
                    });

            // 等待所有任务完成
            TimeUnit.SECONDS.sleep(1);

            // 验证处理结果
            log.info("成功处理数据: {}, 失败处理数据: {}", processedCount.get(), failedCount.get());
            Assertions.assertEquals(10, processedCount.get() + failedCount.get(),
                    "处理的总数据量应该等于10");

            log.info("测试完成");
        } finally {
            MDC.remove(SystemConstant.MDC_TRACE_ID);
        }
    }

    /**
     * 测试环节1: 业务数据处理被中断
     * 模拟在处理过程中特定线程被中断的情况
     */
    @Test
    void testBusinessProcessInterrupted() {
        TestJobParam param = new TestJobParam();
        param.setMaxConcurrent(5);

        AtomicInteger interruptedCount = new AtomicInteger(0);

        BaseJobExe.execute(
                param,
                TestData::getId,
                () -> generateTestData(10),
                data -> {
                    try {
                        log.info("开始处理数据: {}", data.getId());

                        // 模拟特定数据ID的处理会被中断
                        if (data.getId().contains("3") || data.getId().contains("7")) {
                            Thread.currentThread().interrupt();
                            throw new InterruptedException("人为中断测试");
                        }

                        TimeUnit.MILLISECONDS.sleep(300);
                        log.info("处理完成: {}", data.getId());
                    } catch (InterruptedException e) {
                        interruptedCount.incrementAndGet();
                        log.warn("处理被中断: {}", data.getId());
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("处理被中断", e);
                    }
                },
                (data, e) -> log.warn("处理失败: {}, 错误: {}", data.getId(), e.getMessage())
        );

        Assertions.assertEquals(2, interruptedCount.get(), "应该有2个任务被中断");
    }

    /**
     * 测试环节2: 业务数据处理超时中断
     * 模拟处理超时导致的中断
     */
    @Test
    void testExecutorInterrupted() {
        TestJobParam param = new TestJobParam();
        param.setMaxConcurrent(2);

        // 添加控制机制和状态跟踪
        CountDownLatch executionStarted = new CountDownLatch(1);
        CountDownLatch firstTaskStarted = new CountDownLatch(1);
        CountDownLatch interruptionHappened = new CountDownLatch(1);  // 新增：用于监测中断是否发生

        Thread mainThread = Thread.currentThread();

        // 创建一个线程，稍后中断主线程
        Thread interrupter = new Thread(() -> {
            try {
                // 等待任务开始执行
                log.info("中断线程等待执行开始...");
                boolean allStarted = executionStarted.await(2, TimeUnit.SECONDS);
                if (allStarted) {
                    // 再等待第一个任务开始处理
                    log.info("等待第一个任务开始处理...");
                    boolean taskStarted = firstTaskStarted.await(2, TimeUnit.SECONDS);
                    if (taskStarted) {
                        // 给任务一些时间运行
                        TimeUnit.MILLISECONDS.sleep(500);
                        log.info("即将中断主线程...");
                        mainThread.interrupt();
                        // 标记已发送中断
                        interruptionHappened.countDown();
                        log.info("已发送中断信号");
                    } else {
                        log.warn("等待任务开始处理超时，中断未执行");
                    }
                } else {
                    log.warn("等待执行开始超时，中断未执行");
                }
            } catch (InterruptedException e) {
                log.error("中断线程自身被中断", e);
            }
        });

        interrupter.start();

        try {
            BaseJobExe.execute(
                    param,
                    TestData::getId,
                    () -> {
                        // 通知中断线程执行已开始
                        log.info("任务开始执行，即将通知中断线程");
                        executionStarted.countDown();
                        return generateTestData(5);  // 减少数据量，使测试更快完成
                    },
                    data -> {
                        try {
                            log.info("开始处理数据: {}", data.getId());

                            // 通知第一个任务已开始处理
                            if (data.getId().equals("task-0")) {
                                firstTaskStarted.countDown();
                            }

                            // 处理过程
                            for (int i = 0; i < 5; i++) {
                                TimeUnit.MILLISECONDS.sleep(300);
                                log.info("处理数据 {}: 进度 {}0%", data.getId(), i + 1);
                            }

                            log.info("完成��理数据: {}", data.getId());
                        } catch (InterruptedException e) {
                            log.error("处理过程被中断: {}", data.getId());
                            Thread.currentThread().interrupt();
                            throw new RuntimeException(e);
                        }
                    }
            );
        } catch (Exception e) {
            log.info("捕获到异常: {}, 类型: {}", e.getMessage(), e.getClass().getSimpleName());
        }

        try {
            // 等待直到中断确实发生或超时
            boolean interruptOccurred = interruptionHappened.await(3, TimeUnit.SECONDS);
            Assertions.assertTrue(interruptOccurred, "中断信号应该已被发送");

            // 验证日志中是否包含"任务执行被中断"信息
            // 这一步在实际测试中需要通过TestLogAppender或类似机制检查日志内容
            log.info("中断信号已发送，测试通过");
        } catch (InterruptedException e) {
            log.error("等待中断确认时被中断", e);
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 测试环节4: 获取信号量许可等待
     * 测试并发控制机制是否正常工作
     */
    @Test
    void testSemaphoreWaiting() {
        TestJobParam param = new TestJobParam();
        // 设置非常小的并发度，确保会有等待
        param.setMaxConcurrent(1);

        AtomicInteger concurrentExecutions = new AtomicInteger(0);
        AtomicInteger maxConcurrent = new AtomicInteger(0);

        BaseJobExe.execute(
                param,
                TestData::getId,
                () -> generateTestData(10),
                data -> {
                    try {
                        // 记录并统计当前并发数
                        int current = concurrentExecutions.incrementAndGet();
                        int max = Math.max(maxConcurrent.get(), current);
                        maxConcurrent.set(max);

                        log.info("开始处理数据: {}, 当前并发数: {}", data.getId(), current);

                        // 模拟长时间处理，以确保并发控制可见
                        TimeUnit.MILLISECONDS.sleep(300);

                        log.info("完成处理数据: {}", data.getId());
                    } catch (InterruptedException e) {
                        log.error("处理被中断: {}", data.getId());
                        Thread.currentThread().interrupt();
                    } finally {
                        concurrentExecutions.decrementAndGet();
                    }
                }
        );

        // 验证最大并发没有超过设置值
        Assertions.assertEquals(1, maxConcurrent.get(), "最大并发应该是1");
        log.info("信号量并发控制测试通过，最大并发: {}", maxConcurrent.get());
    }

    /**
     * 处理数据
     */
    private void processData(TestData data) {
        Random random = new Random();

        log.info("开始处理任务: {}", data.getContent());

        try {
            // 模拟处理时间
            TimeUnit.MILLISECONDS.sleep(100 + random.nextInt(300));

            log.info("处理任务中间步骤1");
            TimeUnit.MILLISECONDS.sleep(100 + random.nextInt(200));

            log.info("处理任务中间步骤2");
            TimeUnit.MILLISECONDS.sleep(100 + random.nextInt(200));

            // 随机抛出异常
            if (random.nextInt(10) < 2) {
                throw new RuntimeException("随机失败测试");
            }

            log.info("任务处理完成");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("处理被中断", e);
        }
    }

    /**
     * 处理错误
     */
    private void handleError(TestData data, Exception e) {
        log.error("数据处理失败, dataId: {}, 错误: {}", data.getId(), e.getMessage());
    }

    /**
     * 生成指定数量的测试数据
     */
    private Collection<TestData> generateTestData(int count) {
        log.info("生成{}条测试数据...", count);
        return IntStream.range(0, count)
                .mapToObj(i -> new TestData("task-" + i, "Task " + i + " Content"))
                .toList();
    }

    /**
     * 测试数据类
     */
    @Data
    @AllArgsConstructor
    static class TestData {

        private String id;
        private String content;

    }

    /**
     * 测试任务参数类
     */
    @Getter
    @Setter
    @ToString
    static class TestJobParam extends BaseJobBO {

        public TestJobParam() {
            // 设置默认偏移量和限制天数，避免NPE
            setOffsetMinutes(60);
            setLimitDays(1);
            // 设置短超时时间（秒），便于超时测试
            setTimeoutSeconds(60);
        }

    }

}